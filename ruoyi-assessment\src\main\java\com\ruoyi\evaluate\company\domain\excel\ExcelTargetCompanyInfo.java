package com.ruoyi.evaluate.company.domain.excel;

import lombok.Data;

/**
 * 被评估单位基本信息Excel导入对象 - Sheet1
 * 用于固定单元格位置的表单读取
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ExcelTargetCompanyInfo {

    /** 单位名称 - 对应单元格B2 */
    private String companyName;

    /** 单位地址 - 对应单元格B3 */
    private String companyAddress;

    /** 邮政编码 - 对应单元格C3 */
    private String postCode;

    /** 注册地 - 对应单元格B4 */
    private String registerPlace;

    /** 统一社会信用代码 - 对应单元格C4 */
    private String creditCode;

    /** 组织机构类型 - 对应单元格B5 */
    private String orgType;

    /** 法定代表人姓名及国籍 - 对应单元格C5 */
    private String legalRepresentative;

    /** 分支单位 - 对应单元格B6 */
    private String branchUnit;

    /** 运营控制情况 - 对应单元格B7 */
    private String operationControl;

    /** 人员情况 - 对应单元格B8 */
    private String personnelSituation;

    /** 经营范围 - 对应单元格B9 */
    private String businessScope;

    /** 上市情况 - 对应单元格B10 */
    private String listingSituation;

    /** 是否开展了数据分类分级 - 对应单元格B11 */
    private String dataClassification;

    /** 重要数据、核心数据是否备案 - 对应单元格B12 */
    private String dataRecord;

    /** 备案回复情况 - 对应单元格B13 */
    private String recordReply;

    /** 本次评估是第几次数据安全风险评估 - 对应单元格B14 */
    private String timesNumber;
}
