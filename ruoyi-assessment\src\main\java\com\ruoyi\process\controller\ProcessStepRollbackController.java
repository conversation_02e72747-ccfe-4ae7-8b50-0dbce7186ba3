package com.ruoyi.process.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.process.domain.ProcessStepRollback;
import com.ruoyi.process.service.IProcessStepRollbackService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程回退记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/process/stepRollback")
@Api(value = "流程回退记录控制器", tags = {"流程回退记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessStepRollbackController extends BaseController
{
    private final IProcessStepRollbackService processStepRollbackService;

    /**
     * 查询流程回退记录列表
     */
    @ApiOperation("查询流程回退记录列表")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessStepRollback processStepRollback) {
        startPage();
        List<ProcessStepRollback> list = processStepRollbackService.list(new QueryWrapper<ProcessStepRollback>(processStepRollback));
        return getDataTable(list);
    }

    /**
     * 导出流程回退记录列表
     */
    @ApiOperation("导出流程回退记录列表")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:export')")
    @Log(title = "流程回退记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,ProcessStepRollback processStepRollback) {
        List<ProcessStepRollback> list = processStepRollbackService.list(new QueryWrapper<ProcessStepRollback>(processStepRollback));
        ExcelUtil<ProcessStepRollback> util = new ExcelUtil<ProcessStepRollback>(ProcessStepRollback.class);
        util.exportExcel(response,list, "流程回退记录数据");
    }

    /**
     * 获取流程回退记录详细信息
     */
    @ApiOperation("获取流程回退记录详细信息")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processStepRollbackService.getById(id));
    }

    /**
     * 新增流程回退记录
     */
    @ApiOperation("新增流程回退记录")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:add')")
    @Log(title = "流程回退记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessStepRollback processStepRollback) {
        return toAjax(processStepRollbackService.save(processStepRollback));
    }

    /**
     * 修改流程回退记录
     */
    @ApiOperation("修改流程回退记录")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:edit')")
    @Log(title = "流程回退记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessStepRollback processStepRollback) {
        return toAjax(processStepRollbackService.updateById(processStepRollback));
    }

    /**
     * 删除流程回退记录
     */
    @ApiOperation("删除流程回退记录")
    @PreAuthorize("@ss.hasPermi('process:stepRollback:remove')")
    @Log(title = "流程回退记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processStepRollbackService.removeByIds(Arrays.asList(ids)));
    }
}