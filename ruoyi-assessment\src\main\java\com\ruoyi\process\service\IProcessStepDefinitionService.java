package com.ruoyi.process.service;

import com.ruoyi.process.domain.ProcessStepDefinition;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 流程步骤定义Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IProcessStepDefinitionService extends IService<ProcessStepDefinition> {

    /**
     * 获取下个步骤信息
     *
     * @param processId 流程ID
     * @param currentStepOrder 当前步骤顺序
     * @return 下个步骤定义，如果没有则返回null
     */
    ProcessStepDefinition getNextStep(Long processId, Long currentStepOrder);

}
