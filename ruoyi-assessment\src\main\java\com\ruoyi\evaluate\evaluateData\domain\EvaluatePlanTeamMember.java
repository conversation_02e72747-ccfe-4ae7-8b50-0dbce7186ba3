package com.ruoyi.evaluate.evaluateData.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 评估单位评估团队成员对象 dsa_evaluate_plan_team_member
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_plan_team_member")
public class EvaluatePlanTeamMember extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 计划ID */
    @Excel(name = "计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 分组，1-项目管理组 2-评估组 3-技术支持组 4-质量保证组 5-配置管理组 */
    @Excel(name = "分组，1-项目管理组 2-评估组 3-技术支持组 4-质量保证组 5-配置管理组")
    @TableField(value = "group_id")
    private Integer groupId;

    /** 角色 */
    @Excel(name = "角色")
    @TableField(value = "role")
    private String role;

    /** 职责 */
    @Excel(name = "职责")
    @TableField(value = "duty")
    private String duty;

    /** 姓名 */
    @Excel(name = "姓名")
    @TableField(value = "name")
    private String name;

    /** 单位 */
    @Excel(name = "单位")
    @TableField(value = "unit")
    private String unit;

    /** 岗位 */
    @Excel(name = "岗位")
    @TableField(value = "position")
    private String position;

    /** 能力资质 */
    @Excel(name = "能力资质")
    @TableField(value = "ability")
    private String ability;

    /** 评估工作经验 */
    @Excel(name = "评估工作经验")
    @TableField(value = "experience")
    private String experience;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}