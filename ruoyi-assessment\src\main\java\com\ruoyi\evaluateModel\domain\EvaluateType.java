package com.ruoyi.evaluateModel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;



/**
 * 评估类型对象 dsa_evaluate_type
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_type")
public class EvaluateType extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 流程编码，唯一 */
    @Excel(name = "流程编码，唯一")
    @TableField(value = "process_code")
    private String processCode;

    /** 评估类型名称 */
    @Excel(name = "评估类型名称")
    @TableField(value = "title")
    private String title;

    /** 排序值，越大越靠前 */
    @Excel(name = "排序值，越大越靠前")
    @TableField(value = "sort")
    private Integer sort;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;

    /** 关联的评估模型 */
    @TableField(exist = false)
    private EvaluateModel model;






}