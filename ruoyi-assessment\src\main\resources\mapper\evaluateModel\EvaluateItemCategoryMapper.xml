<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluateModel.mapper.EvaluateItemCategoryMapper">
    
    <resultMap type="EvaluateItemCategory" id="EvaluateItemCategoryResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateItemCategoryVo">
        select id, model_id, parent_id, category_name, sort, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_item_category
    </sql>

    <select id="selectEvaluateItemCategoryList" parameterType="EvaluateItemCategory" resultMap="EvaluateItemCategoryResult">
        <include refid="selectEvaluateItemCategoryVo"/>
        <where>  
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateItemCategoryById" parameterType="Long" resultMap="EvaluateItemCategoryResult">
        <include refid="selectEvaluateItemCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateItemCategory" parameterType="EvaluateItemCategory" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_item_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelId != null">model_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelId != null">#{modelId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateItemCategory" parameterType="EvaluateItemCategory">
        update dsa_evaluate_item_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateItemCategoryById" parameterType="Long">
        delete from dsa_evaluate_item_category where id = #{id}
    </delete>

    <delete id="deleteEvaluateItemCategoryByIds" parameterType="String">
        delete from dsa_evaluate_item_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>