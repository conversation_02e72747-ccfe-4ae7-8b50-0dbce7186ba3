package com.ruoyi.dataItem.service;

import com.ruoyi.dataItem.domain.DataSystem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 数据承载系统Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IDataSystemService extends IService<DataSystem> {

    /**
     * 根据系统名称和orgId查找系统ID
     */
    Long getDataSystemIdByName(String systemName, Long orgId);

    /**
     * 插入新系统并返回ID，带orgId
     */
    Long insertDataSystem(String systemName, Long orgId);
}
