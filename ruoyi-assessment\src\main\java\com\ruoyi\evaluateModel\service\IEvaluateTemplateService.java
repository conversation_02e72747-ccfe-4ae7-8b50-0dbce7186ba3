package com.ruoyi.evaluateModel.service;

import com.ruoyi.evaluateModel.domain.EvaluateTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 评估报告模板Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IEvaluateTemplateService extends IService<EvaluateTemplate> {

    /**
     * 复制评估报告模板
     *
     * @param srcTemplateId 源模板ID
     * @param newTemplate 新模板信息
     * @return 复制结果
     */
    boolean copyTemplate(Long srcTemplateId, EvaluateTemplate newTemplate);
}
