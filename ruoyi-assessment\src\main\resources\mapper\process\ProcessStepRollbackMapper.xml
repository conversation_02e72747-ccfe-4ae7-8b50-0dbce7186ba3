<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.process.mapper.ProcessStepRollbackMapper">
    
    <resultMap type="ProcessStepRollback" id="ProcessStepRollbackResult">
        <result property="id"    column="id"    />
        <result property="planTaskId"    column="plan_task_id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="fromStepInstanceId"    column="from_step_instance_id"    />
        <result property="toStepDefinitionId"    column="to_step_definition_id"    />
        <result property="rollbackTime"    column="rollback_time"    />
        <result property="operator"    column="operator"    />
        <result property="rollbackReason"    column="rollback_reason"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProcessStepRollbackVo">
        select id, plan_task_id, process_instance_id, from_step_instance_id, to_step_definition_id, rollback_time, operator, rollback_reason, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_process_step_rollback
    </sql>

    <select id="selectProcessStepRollbackList" parameterType="ProcessStepRollback" resultMap="ProcessStepRollbackResult">
        <include refid="selectProcessStepRollbackVo"/>
        <where>  
            <if test="planTaskId != null "> and plan_task_id = #{planTaskId}</if>
            <if test="processInstanceId != null "> and process_instance_id = #{processInstanceId}</if>
            <if test="fromStepInstanceId != null "> and from_step_instance_id = #{fromStepInstanceId}</if>
            <if test="toStepDefinitionId != null "> and to_step_definition_id = #{toStepDefinitionId}</if>
            <if test="rollbackTime != null "> and rollback_time = #{rollbackTime}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="rollbackReason != null  and rollbackReason != ''"> and rollback_reason = #{rollbackReason}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectProcessStepRollbackById" parameterType="Long" resultMap="ProcessStepRollbackResult">
        <include refid="selectProcessStepRollbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertProcessStepRollback" parameterType="ProcessStepRollback" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_process_step_rollback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planTaskId != null">plan_task_id,</if>
            <if test="processInstanceId != null">process_instance_id,</if>
            <if test="fromStepInstanceId != null">from_step_instance_id,</if>
            <if test="toStepDefinitionId != null">to_step_definition_id,</if>
            <if test="rollbackTime != null">rollback_time,</if>
            <if test="operator != null">operator,</if>
            <if test="rollbackReason != null">rollback_reason,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planTaskId != null">#{planTaskId},</if>
            <if test="processInstanceId != null">#{processInstanceId},</if>
            <if test="fromStepInstanceId != null">#{fromStepInstanceId},</if>
            <if test="toStepDefinitionId != null">#{toStepDefinitionId},</if>
            <if test="rollbackTime != null">#{rollbackTime},</if>
            <if test="operator != null">#{operator},</if>
            <if test="rollbackReason != null">#{rollbackReason},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateProcessStepRollback" parameterType="ProcessStepRollback">
        update dsa_process_step_rollback
        <trim prefix="SET" suffixOverrides=",">
            <if test="planTaskId != null">plan_task_id = #{planTaskId},</if>
            <if test="processInstanceId != null">process_instance_id = #{processInstanceId},</if>
            <if test="fromStepInstanceId != null">from_step_instance_id = #{fromStepInstanceId},</if>
            <if test="toStepDefinitionId != null">to_step_definition_id = #{toStepDefinitionId},</if>
            <if test="rollbackTime != null">rollback_time = #{rollbackTime},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="rollbackReason != null">rollback_reason = #{rollbackReason},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessStepRollbackById" parameterType="Long">
        delete from dsa_process_step_rollback where id = #{id}
    </delete>

    <delete id="deleteProcessStepRollbackByIds" parameterType="String">
        delete from dsa_process_step_rollback where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>