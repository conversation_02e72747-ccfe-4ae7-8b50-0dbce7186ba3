package com.ruoyi.planList.service;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.planList.domain.PlanList;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.ExcelFieldValidate;
import java.lang.reflect.Field;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;

/**
 * <AUTHOR>
 */
@Service
public class PlanListValidatorService {

    @Autowired
    private ISysDictTypeService dictTypeService;

    public void validate(PlanList plan, List<ExcelImportHelper.RowError.ColumnError> errors) {
        Field[] fields = PlanList.class.getDeclaredFields();
        for (Field field : fields) {
            ExcelFieldValidate anno = field.getAnnotation(ExcelFieldValidate.class);
            if (anno != null) {
                field.setAccessible(true);
                try {
                    Object value = field.get(plan);
                    String strValue = value == null ? "" : value.toString();
                    // 必填校验
                    if (anno.required() && strValue.trim().isEmpty()) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason("不能为空或格式不正确");
                        errors.add(error);
                    }
                    // 长度校验
                    if (anno.maxLength() > 0 && strValue.length() > anno.maxLength()) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason("长度不能超过" + anno.maxLength());
                        errors.add(error);
                    }
                    // 正则校验
                    if (!anno.regex().isEmpty() && !strValue.matches(anno.regex())) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason(anno.regexMsg().isEmpty() ? ("格式不正确") : anno.regexMsg());
                        errors.add(error);
                    }
                    // 字典校验
                    if (!anno.dictType().isEmpty() && !strValue.isEmpty()) {
                        List<SysDictData> dictList = dictTypeService.selectDictDataByType(anno.dictType());
                        boolean valid = dictList != null && dictList.stream().anyMatch(d -> d.getDictLabel().equals(strValue));
                        if (!valid) {
                            ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                            error.setColumn(anno.name());
                            String dictDisplay = dictList == null ? "" : dictList.stream().map(SysDictData::getDictLabel).collect(Collectors.joining("、"));
                            error.setReason("值不合法，必须为：" + dictDisplay);
                            errors.add(error);
                        }
                    }
                } catch (IllegalAccessException e) {
                    // ignore
                }
            }
        }
        
        // 其他特殊业务校验可单独补充
        // 根据【年份（即版本号）+【数据项名称】+【数据级别】来判断唯一性
        // 需要在外部批量校验时调用 validateUniqueKeys
    }

    /**
     * 校验【年份（即版本号）+数据项名称+数据级别】唯一性
     * @param validList 本次导入的所有PlanList
     * @param dbList 数据库中已存在的PlanList
     * @param errorRows 错误收集对象
     * @return 数据库已存在的PlanList列表（需后续更新）
     */
    public List<PlanList> validateUniqueKeys(List<PlanList> validList, List<PlanList> dbList, List<Map<String, Object>> errorRows) {
        Set<String> uniqueKeys = new HashSet<>();
        Map<String, PlanList> dbKeyToObj = new HashMap<>();
        for (PlanList db : dbList) {
            String year = "";
            if (db.getPlannedEvalTime() != null) {
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(db.getPlannedEvalTime());
                year = String.valueOf(cal.get(java.util.Calendar.YEAR));
            }
            String key = year + "_" + db.getDataItemName() + "_" + db.getDataLevel();
            uniqueKeys.add(key);
            dbKeyToObj.put(key, db);
        }
        Map<String, Integer> keyToRow = new HashMap<>();
        int rowNum = 3; // 假设Excel数据从第3行开始

        List<PlanList> existInDbList = new ArrayList<>();
        Iterator<PlanList> iterator = validList.iterator();
        while (iterator.hasNext()) {
            PlanList plan = iterator.next();
            String year = "";
            if (plan.getPlannedEvalTime() != null) {
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(plan.getPlannedEvalTime());
                year = String.valueOf(cal.get(java.util.Calendar.YEAR));
            }
            String key = year + "_" + plan.getDataItemName() + "_" + plan.getDataLevel();
            if (uniqueKeys.contains(key)) {
                // 关键：赋值id
                PlanList dbObj = dbKeyToObj.get(key);
                if (dbObj != null) {
                    plan.setId(dbObj.getId());
                }
                existInDbList.add(plan);

                Map<String, Object> rowError = new HashMap<>();
                rowError.put("rowNum", rowNum);
                List<ExcelImportHelper.RowError.ColumnError> errors = new ArrayList<>();
                ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                error.setColumn("数据项唯一性");
                error.setReason("与数据库中已存在的数据重复（版本+数据项名称+数据级别），将进行更新");
                errors.add(error);
                rowError.put("errors", errors);
                errorRows.add(rowError);

                iterator.remove();
            } else if (keyToRow.containsKey(key)) {
                Map<String, Object> rowError = new HashMap<>();
                rowError.put("rowNum", rowNum);
                List<ExcelImportHelper.RowError.ColumnError> errors = new ArrayList<>();
                ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                error.setColumn("数据项唯一性");
                error.setReason("与第 " + keyToRow.get(key) + " 行数据重复（版本+数据项名称+数据级别）");
                errors.add(error);
                rowError.put("errors", errors);
                errorRows.add(rowError);
                iterator.remove();
            } else {
                keyToRow.put(key, rowNum);
            }
            rowNum++;
        }
        return existInDbList;
    }
}
