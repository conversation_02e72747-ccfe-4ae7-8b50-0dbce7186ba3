package com.ruoyi.dataItem.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.BeanConvertUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.dataItem.domain.excel.ExcelDataItem;
import com.ruoyi.dataItem.enums.OperationTypeEnum;
import com.ruoyi.dataItem.listener.DataItemListener;
import com.ruoyi.dataItem.service.IDataSystemService;
import com.ruoyi.dataItem.validator.ValidatorService;
import com.ruoyi.planList.domain.PlanList;
import com.ruoyi.planList.domain.excel.ExcelPlanList;
import com.ruoyi.planList.map.FieldLabelMap;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataItemMapper;
import com.ruoyi.dataItem.domain.DataItem;
import com.ruoyi.dataItem.service.IDataItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.dataItem.mapper.DataItemBaseMapper;
import com.ruoyi.dataItem.mapper.DataItemLogMapper;
import com.ruoyi.dataItem.domain.DataItemLog;
import com.ruoyi.dataItem.service.IDataItemLogService;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据项管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class DataItemServiceImpl extends ServiceImpl<DataItemMapper, DataItem> implements IDataItemService {
    @Autowired
    private ValidatorService validatorService;

    @Autowired
    private FieldLabelMap fieldLabelMap;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IDataSystemService dataSystemService;

    @Autowired
    private DataItemBaseMapper dataItemBaseMapper;

    @Autowired
    private DataItemLogMapper dataItemLogMapper;

    @Autowired
    private IDataItemLogService dataItemLogService;

    private String[] ignoreFields = new String[]{"serialVersionUID", "id", "orgId", "createBy", "createTime", "updateBy", "updateTime", "remark", "status", "deleteBy", "deleteTime", "dutyDept", "dutyUser"};

    /**
     * 物理删除指定单位下所有数据项及其关联数据
     */
    public void physicalDeleteByOrgIdWithRelations(Long orgId) {
        // 1. 查询本单位下所有数据项id
        List<Long> dataItemIds = this.lambdaQuery()
                .eq(DataItem::getOrgId, orgId)
                .list()
                .stream()
                .map(DataItem::getId)
                .collect(Collectors.toList());

        // 2. 物理删除数据项
        this.getBaseMapper().physicalDeleteByOrgId(orgId);

        // 3. 物理删除数据项基本信息（只删对应数据项id）
        if (!dataItemIds.isEmpty()) {
            dataItemBaseMapper.deleteByDataItemIds(dataItemIds);
            dataItemLogMapper.deleteByDataItemIds(dataItemIds);
        }
    }

    @Override
    public int batchImport(Long orgId, List<DataItem> excelList) {
        if (CollectionUtils.isEmpty(excelList)) {
            return 0;
        }

        List<DataItem> entityList = excelList.stream().map(item -> {
            DataItem entity = new DataItem();
            BeanUtils.copyProperties(item, entity);

            // 根据承载信息系统名称查询对应的系统ID，如果存在则获取id，如果不存在则插入
            entity.setDataSystemId(getDataSystemId(entity.getSystemName(), orgId));

            entity.setCreateTime(new Date());
            entity.setCreateBy(SecurityUtils.getUsername());
            return entity;
        }).collect(Collectors.toList());

        this.saveBatch(entityList);
        return entityList.size();
    }

    private Long getDataSystemId(String dataSystemName, Long orgId) {
        Long dataSystemId = dataSystemService.getDataSystemIdByName(dataSystemName, orgId);
        if (dataSystemId == null) {
            dataSystemId = dataSystemService.insertDataSystem(dataSystemName, orgId);
        }
        return dataSystemId;
    }

    @Override
    public Map<String, Object> importExcelList(MultipartFile file, Long orgId) throws IOException {
        Map<String, Object> result = new HashMap<>();
        if (orgId == null) {
            throw new ServiceException("请选择所属单位");
        }

        if (!sysConfigService.selectConfigByKey("dsa.dataItem.import").equals("1")) {
            throw new ServiceException("数据项备案目录导入配置已关闭，不允许导入；如果开启后，重新导入会【清空】当前备案目录下所有数据项，且不可恢复！！！");
        }

        //表头占几行
        int tableHeaderCount = 6;

        // 物理删除，绕过逻辑删除
        this.physicalDeleteByOrgIdWithRelations(orgId);

        // 1. 解析Excel
        log.info("1. 解析Excel");
        List<ExcelDataItem> excelData = parseExcel(file, tableHeaderCount);

        // 3. 转换为PlanList
        log.info("3. 转换");
        List<DataItem> planList = convertExcelListToDbList(excelData, orgId);
        log.info("3. 转换 -> {}", planList);

        // 4. 单条校验
        log.info("4. 单条校验");
        List<DataItem> validList = new ArrayList<>();
        List<Map<String, Object>> errorRows = new ArrayList<>();
        validateSingleItem(tableHeaderCount, planList, validList, errorRows);
        log.info("4. 单条校验完成 -> {}", validList);

        // 5. 唯一性校验
        log.info("5. 唯一性校验");
        List<DataItem> dbList = selectList(orgId);
        validatorService.validateUniqueKeys(validList, dbList, errorRows);
        log.info("5. 唯一性校验完成 -> {}", validList);

        // 6. 入库（新增）
        log.info("6. 入库（新增）");
        int successCount = batchImport(orgId, validList);

        // 如果存在新增数据、更新数据，则将dsa.dataItem.import设置为0
        if (successCount > 0) {
            // sysConfigService.updateConfigByKey("dsa.dataItem.import", "0");
        }

        // 8. 结果组装
        result.put("successCount", successCount);
        result.put("errorCount", errorRows.size());
        result.put("errorRows", errorRows);
        return result;
    }

    @Override
    public void exportExcelWithTemplate(HttpServletResponse response, DataItem element, String templatePath) {
        try (InputStream templateInputStream = new FileInputStream(templatePath)) {
            List<DataItem> dataList = this.list(new QueryWrapper<>(element).orderByAsc("id"));
            List<ExcelDataItem> excelList = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                ExcelDataItem excel = new ExcelDataItem();
                BeanUtils.copyProperties(dataList.get(i), excel);
                excelList.add(excel);
            }
            log.info("数据转换完成 -> {}", excelList);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("工业和信息化领域重要数据和核心数据目录备案表数据", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateInputStream)
                    .build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.fill(excelList, writeSheet);
            }
        } catch (Exception e) {
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新PlanList（数据库已存在的数据）
     * 可根据实际业务完善更新逻辑
     */
    private int batchUpdate(List<DataItem> updateList, Long orgId) {
        if (updateList == null || updateList.isEmpty()) {
            return 0;
        }
        // 这里可以根据唯一性条件查出数据库原有数据，然后set新值后批量更新
        // 这里只做简单实现，实际可根据主键或唯一性条件更新
        for (DataItem update : updateList) {
            // 你可以根据唯一性条件查出原有数据，然后set新值
            // 这里只是简单调用updateById，实际可根据业务调整
            this.updateById(update);
        }
        return updateList.size();
    }

    // 解析Excel
    private List<ExcelDataItem> parseExcel(MultipartFile file, int tableHeaderCount) throws IOException {
        DataItemListener listener = new DataItemListener();
        EasyExcel.read(file.getInputStream(), ExcelDataItem.class, listener)
                .headRowNumber(tableHeaderCount)
                .sheet()
                .doRead();
        return listener.getCacheList();
    }

    // 转换
    private List<DataItem> convertExcelListToDbList(List<ExcelDataItem> excelData, Long orgId) {
        List<DataItem> planList = new ArrayList<>();
        for (ExcelDataItem excel : excelData) {
            DataItem plan = convertToDbList(excel, orgId);
            plan.setOrgId(orgId);
            planList.add(plan);
        }
        return planList;
    }

    // 单条校验
    private void validateSingleItem(int tableHeaderCount, List<DataItem> eleList, List<DataItem> validList, List<Map<String, Object>> errorRows) {
        int rowNum = tableHeaderCount + 1;
        for (DataItem item : eleList) {
            List<ExcelImportHelper.RowError.ColumnError> rowErrors = new ArrayList<>();
            validatorService.validate(item, rowErrors);
            if (rowErrors.isEmpty()) {
                validList.add(item);
            } else {
                Map<String, Object> rowError = new HashMap<>();
                rowError.put("rowNum", rowNum);
                rowError.put("errors", rowErrors);
                errorRows.add(rowError);
            }
            rowNum++;
        }
    }

    // 查询数据库PlanList
    private List<DataItem> selectList(Long orgId) {
        return this.lambdaQuery()
                .eq(DataItem::getOrgId, orgId)
                .list();
    }

    @Override
    public DataItem convertToDbList(ExcelDataItem excel, Long orgId) {
        DataItem target = new DataItem();
        // 复制基础属性
        BeanUtils.copyProperties(excel, target);
        // 日期类型转换
        target.setAssessTime(BeanConvertUtil.parseDate(excel.getAssessTime()));
        // target.setActualEvalTime(BeanConvertUtil.parseDate(excel.getActualEvalTime()));
        // 自动处理所有映射字段
        fieldLabelMap.getFieldLabelToValue().forEach((fieldName, mapping) -> {
            try {
                Field srcField = ExcelPlanList.class.getDeclaredField(fieldName);
                srcField.setAccessible(true);
                Object srcValue = srcField.get(excel);
                String label = srcValue == null ? "" : srcValue.toString();
                String mappedValue = mapping.getOrDefault(label, label);
                Field targetField = PlanList.class.getDeclaredField(fieldName);
                targetField.setAccessible(true);
                targetField.set(target, mappedValue);
            } catch (Exception e) {
                // ignore or log
            }
        });
        target.setOrgId(orgId);
        return target;
    }

    @Override
    public boolean updateById(DataItem dataItem) {
        log.info("更新数据项 -> {}", dataItem);
        // 字段黑名单，黑名单内字段变动不记录日志
        Set<String> fieldBlacklist = new HashSet<>(Arrays.asList(ignoreFields));
        // 查询原始数据
        DataItem oldDataItem = this.getById(dataItem.getId());
        if (oldDataItem == null) {
            throw new ServiceException("数据项不存在");
        }
        List<DataItemLog> logList = new ArrayList<>();
        // 通过反射比对字段
        Field[] fields = DataItem.class.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            if (fieldBlacklist.contains(fieldName)) {
                continue;
            }
            try {
                Object newValue = field.get(dataItem);
                log.debug("字段：{}，新值：{}", fieldName, newValue);
                if (newValue != null) {
                    Object oldValue = field.get(oldDataItem);
                    log.debug("字段：{}，旧值：{}", fieldName, oldValue);
                    if (!Objects.equals(oldValue, newValue)) {
                        logList.add(
                            buildDataItemLog(
                                dataItem.getId(), 
                                oldDataItem.getOrgId(), 
                                fieldName, 
                                oldValue, 
                                newValue, 
                                OperationTypeEnum.UPDATE.getValue()
                            )
                        );
                    }
                }
            } catch (IllegalAccessException e) {
                // 可记录日志
            }
        }
        if (logList.isEmpty()) {
            throw new ServiceException("本次提交未做任何变更");
        }
        dataItemLogService.saveBatch(logList);
        return super.updateById(dataItem);
    }

    /**
     * 构建数据项变更日志对象
     */
    private DataItemLog buildDataItemLog(Long dataItemId, Long orgId, String fieldName, Object beforeValue, Object afterValue, Integer operationType) {
        DataItemLog log = new DataItemLog();
        log.setDataItemId(dataItemId);
        log.setOrgId(orgId);
        log.setFieldName(fieldName);
        log.setBeforeValue(beforeValue == null ? null : beforeValue.toString());
        log.setAfterValue(afterValue == null ? null : afterValue.toString());
        log.setOperationType(operationType);
        log.setOperationUser(SecurityUtils.getUsername());
        log.setOperationTime(new Date());
        return log;
    }

    /**
     * 新增数据项并记录所有字段变更日志
     */
    public boolean saveWithLog(DataItem dataItem) {
        boolean result = super.save(dataItem);
        if (result) {
            List<DataItemLog> logList = new ArrayList<>();
            Field[] fields = DataItem.class.getDeclaredFields();
            Set<String> fieldBlacklist = new HashSet<>(Arrays.asList(ignoreFields));
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (fieldBlacklist.contains(fieldName)) {
                    continue;
                }
                try {
                    Object value = field.get(dataItem);
                    if (value != null) {
                        logList.add(
                            buildDataItemLog(
                                dataItem.getId(), 
                                dataItem.getOrgId(), 
                                fieldName, 
                                null, 
                                value, 
                                OperationTypeEnum.INSERT.getValue()
                            )
                        );
                    }
                } catch (IllegalAccessException e) {
                    // ignore
                }
            }
            if (!logList.isEmpty()) {
                dataItemLogService.saveBatch(logList);
            }
        }
        return result;
    }

    /**
     * 删除数据项并记录所有字段变更日志
     */
    @Override
    public boolean removeByIdWithLog(Long id) {
        DataItem oldDataItem = this.getById(id);
        if (oldDataItem == null) {
            throw new ServiceException("数据项不存在");
        }
        // 构造一个新对象用于逻辑删除和赋值
        DataItem update = new DataItem();
        update.setId(id);
        update.setDeleteTime(new Date()); // 赋值当前时间
        update.setDeleteBy(SecurityUtils.getUsername()); // 赋值当前用户
        update.setDelFlag("1"); 
        boolean result = this.baseMapper.updateById(update) == 1; // 只更新deleteTime、deleteBy、delFlag等
        this.baseMapper.deleteById(id);

        if (result) {
            List<DataItemLog> logList = new ArrayList<>();
            Field[] fields = DataItem.class.getDeclaredFields();
            Set<String> fieldBlacklist = new HashSet<>(Arrays.asList(ignoreFields));
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (fieldBlacklist.contains(fieldName)) {
                    continue;
                }
                try {
                    Object value = field.get(oldDataItem);
                    if (value != null) {
                        logList.add(
                            buildDataItemLog(
                                oldDataItem.getId(), 
                                oldDataItem.getOrgId(), 
                                fieldName, 
                                value, 
                                null, 
                                OperationTypeEnum.DELETE.getValue()
                            )
                        );
                    }
                } catch (IllegalAccessException e) {
                    // ignore
                }
            }
            if (!logList.isEmpty()) {
                dataItemLogService.saveBatch(logList);
            }
        }
        return result;
    }

    @Override
    public boolean save(DataItem dataItem) {
        return saveWithLog(dataItem);
    }

    // 移除@Override注解，避免linter报错
    public boolean removeById(Long id) {
        return removeByIdWithLog(id);
    }
}
