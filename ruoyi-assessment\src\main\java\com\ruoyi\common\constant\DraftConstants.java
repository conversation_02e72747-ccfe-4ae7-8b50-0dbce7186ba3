package com.ruoyi.common.constant;

/**
 * 暂存功能相关常量
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DraftConstants {

    /**
     * 暂存数据 Redis key 前缀
     */
    public static final String DRAFT_CACHE_KEY_PREFIX = "draft_storage:";

    /**
     * 暂存数据索引 Redis key 前缀（用于快速查询用户的所有暂存数据）
     */
    public static final String DRAFT_INDEX_KEY_PREFIX = "draft_index:";

    /**
     * 暂存数据元数据 Redis key 前缀
     */
    public static final String DRAFT_METADATA_KEY_PREFIX = "draft_metadata:";

    /**
     * 默认过期时间（小时）
     */
    public static final int DEFAULT_EXPIRE_HOURS = 24;

    /**
     * 最大过期时间（天）
     */
    public static final int MAX_EXPIRE_DAYS = 30;

    /**
     * 单个用户最大暂存数据条数
     */
    public static final int MAX_DRAFT_COUNT_PER_USER = 100;

    /**
     * 单条暂存数据最大大小（字节）- 1MB
     */
    public static final long MAX_DRAFT_SIZE_BYTES = 1024 * 1024;

    /**
     * 业务类型 - 评估计划任务
     */
    public static final String BUSINESS_TYPE_EVALUATE_PLAN_TASK = "evaluate_plan_task";

    /**
     * 业务类型 - 数据项管理
     */
    public static final String BUSINESS_TYPE_DATA_ITEM = "data_item";

    /**
     * 业务类型 - 评估报告
     */
    public static final String BUSINESS_TYPE_EVALUATE_REPORT = "evaluate_report";

    /**
     * 暂存数据状态 - 正常
     */
    public static final String DRAFT_STATUS_NORMAL = "normal";

    /**
     * 暂存数据状态 - 已过期
     */
    public static final String DRAFT_STATUS_EXPIRED = "expired";

    /**
     * 暂存数据状态 - 已删除
     */
    public static final String DRAFT_STATUS_DELETED = "deleted";

    /**
     * 系统配置键 - 暂存功能开关
     */
    public static final String CONFIG_KEY_DRAFT_ENABLED = "dsa.draft.enabled";

    /**
     * 系统配置键 - 默认过期时间（小时）
     */
    public static final String CONFIG_KEY_DEFAULT_EXPIRE_HOURS = "dsa.draft.default.expire.hours";

    /**
     * 系统配置键 - 最大过期时间（天）
     */
    public static final String CONFIG_KEY_MAX_EXPIRE_DAYS = "dsa.draft.max.expire.days";

    /**
     * 系统配置键 - 单用户最大暂存条数
     */
    public static final String CONFIG_KEY_MAX_COUNT_PER_USER = "dsa.draft.max.count.per.user";

    /**
     * 系统配置键 - 单条数据最大大小（MB）
     */
    public static final String CONFIG_KEY_MAX_SIZE_MB = "dsa.draft.max.size.mb";

    /**
     * 系统配置键 - 自动清理过期数据开关
     */
    public static final String CONFIG_KEY_AUTO_CLEANUP_ENABLED = "dsa.draft.auto.cleanup.enabled";

    /**
     * 系统配置键 - 自动清理间隔（小时）
     */
    public static final String CONFIG_KEY_AUTO_CLEANUP_INTERVAL_HOURS = "dsa.draft.auto.cleanup.interval.hours";

    /**
     * 构建暂存数据的完整Redis key
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @return 完整的Redis key
     */
    public static String buildDraftCacheKey(String businessType, Long userId, String draftKey) {
        return DRAFT_CACHE_KEY_PREFIX + businessType + ":" + userId + ":" + draftKey;
    }

    /**
     * 构建暂存数据索引的Redis key
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @return 索引Redis key
     */
    public static String buildDraftIndexKey(String businessType, Long userId) {
        return DRAFT_INDEX_KEY_PREFIX + businessType + ":" + userId;
    }

    /**
     * 构建暂存数据元数据的Redis key
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @return 元数据Redis key
     */
    public static String buildDraftMetadataKey(String businessType, Long userId, String draftKey) {
        return DRAFT_METADATA_KEY_PREFIX + businessType + ":" + userId + ":" + draftKey;
    }

    /**
     * 构建业务类型的通配符模式
     *
     * @param businessType 业务类型
     * @return 通配符模式
     */
    public static String buildBusinessTypePattern(String businessType) {
        return DRAFT_CACHE_KEY_PREFIX + businessType + ":*";
    }

    /**
     * 构建用户的通配符模式
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @return 通配符模式
     */
    public static String buildUserPattern(String businessType, Long userId) {
        return DRAFT_CACHE_KEY_PREFIX + businessType + ":" + userId + ":*";
    }

    /**
     * 从Redis key中解析业务类型
     *
     * @param redisKey Redis key
     * @return 业务类型
     */
    public static String parseBusinessTypeFromKey(String redisKey) {
        if (redisKey == null || !redisKey.startsWith(DRAFT_CACHE_KEY_PREFIX)) {
            return null;
        }
        
        String suffix = redisKey.substring(DRAFT_CACHE_KEY_PREFIX.length());
        String[] parts = suffix.split(":");
        return parts.length > 0 ? parts[0] : null;
    }

    /**
     * 从Redis key中解析用户ID
     *
     * @param redisKey Redis key
     * @return 用户ID
     */
    public static Long parseUserIdFromKey(String redisKey) {
        if (redisKey == null || !redisKey.startsWith(DRAFT_CACHE_KEY_PREFIX)) {
            return null;
        }
        
        String suffix = redisKey.substring(DRAFT_CACHE_KEY_PREFIX.length());
        String[] parts = suffix.split(":");
        if (parts.length > 1) {
            try {
                return Long.parseLong(parts[1]);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 从Redis key中解析暂存键
     *
     * @param redisKey Redis key
     * @return 暂存键
     */
    public static String parseDraftKeyFromKey(String redisKey) {
        if (redisKey == null || !redisKey.startsWith(DRAFT_CACHE_KEY_PREFIX)) {
            return null;
        }
        
        String suffix = redisKey.substring(DRAFT_CACHE_KEY_PREFIX.length());
        String[] parts = suffix.split(":");
        return parts.length > 2 ? parts[2] : null;
    }
}
