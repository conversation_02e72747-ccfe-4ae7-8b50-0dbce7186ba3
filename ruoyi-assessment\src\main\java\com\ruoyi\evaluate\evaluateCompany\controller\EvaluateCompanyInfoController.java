package com.ruoyi.evaluate.evaluateCompany.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyInfo;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估单位信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/evaluateCompany/info")
@Api(value = "评估单位信息控制器", tags = {"评估单位信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateCompanyInfoController extends BaseController
{
    private final IEvaluateCompanyInfoService evaluateCompanyInfoService;

    /**
     * 查询评估单位信息列表
     */
    @ApiOperation("查询评估单位信息列表")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluateCompanyInfo evaluateCompanyInfo) {
        startPage();
        List<EvaluateCompanyInfo> list = evaluateCompanyInfoService.list(new QueryWrapper<EvaluateCompanyInfo>(evaluateCompanyInfo).orderByDesc("id"));
        return getDataTable(list);
    }

    /**
     * 获取评估单位信息详细信息
     */
    @ApiOperation("获取评估单位信息详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateCompanyInfoService.getById(id));
    }

    /**
     * 新增评估单位信息
     */
    @ApiOperation("新增评估单位信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:info:add')")
    @Log(title = "评估单位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluateCompanyInfo evaluateCompanyInfo) {
        return toAjax(evaluateCompanyInfoService.save(evaluateCompanyInfo));
    }

    /**
     * 修改评估单位信息
     */
    @ApiOperation("修改评估单位信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:info:edit')")
    @Log(title = "评估单位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluateCompanyInfo evaluateCompanyInfo) {
        return toAjax(evaluateCompanyInfoService.updateById(evaluateCompanyInfo));
    }

    /**
     * 删除评估单位信息
     */
    @ApiOperation("删除评估单位信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:info:remove')")
    @Log(title = "评估单位信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateCompanyInfoService.removeByIds(Arrays.asList(ids)));
    }
}