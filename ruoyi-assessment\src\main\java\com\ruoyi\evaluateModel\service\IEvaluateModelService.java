package com.ruoyi.evaluateModel.service;

import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 评估模型Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IEvaluateModelService extends IService<EvaluateModel> {

    /**
     * 从Excel导入评估模型数据
     * @param modelId 模型ID
     * @param file Excel数据
     */
    Map<String, Object> importFromExcel(Long modelId, MultipartFile file) throws Exception;

    /**
     * 拷贝评估模型
     * @param modelId 模型ID
     * @param evaluateModel 拷贝数据
     */
    int copy(Long modelId, EvaluateModel evaluateModel);

    /**
     * 启用某个评估模型（同时禁用同type_id下的其他模型）
     * @param modelId 要启用的模型ID
     * @return 操作结果
     */
    boolean enableModel(Long modelId);

    /**
     * 保存评估模型（重写以支持启用状态逻辑）
     * @param evaluateModel 评估模型
     * @return 保存结果
     */
    @Override
    boolean save(EvaluateModel evaluateModel);
}
