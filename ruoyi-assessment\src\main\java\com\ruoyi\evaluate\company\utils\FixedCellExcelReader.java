package com.ruoyi.evaluate.company.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.evaluate.company.domain.ImageInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetCompanyInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelContactUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 固定单元格位置Excel读取工具类
 * 用于读取固定格式的Excel表单
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class FixedCellExcelReader {

    private static final List<String> IMAGE_EXTENSIONS = Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".bmp");

    /**
     * 读取Sheet1的固定单元格数据
     */
    public static ExcelTargetCompanyInfo readSheet1FixedCells(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return readSheet1FixedCells(inputStream);
        }
    }

    /**
     * 读取Sheet0的固定单元格数据
     */
    public static ExcelContactUserInfo readSheet0FixedCells(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return readSheet0FixedCells(inputStream);
        }
    }

    /**
     * 读取Sheet2中F2单元格的组织架构示意图链接，并遍历目录获取图片信息
     */
    public static String readSheet2FlowChart(MultipartFile file, String workingDirectory) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return readSheet2FlowChart(inputStream, workingDirectory);
        }
    }

    /**
     * 读取Sheet1的固定单元格数据
     */
    public static ExcelTargetCompanyInfo readSheet1FixedCells(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        try {
            // 尝试创建工作簿
            workbook = WorkbookFactory.create(inputStream);

            // 获取Sheet1
            Sheet sheet = workbook.getSheetAt(1); // Sheet1是索引1
            if (sheet == null) {
                throw new IOException("Sheet1不存在");
            }

            ExcelTargetCompanyInfo companyInfo = new ExcelTargetCompanyInfo();

            // 读取固定单元格的值（恢复到原始位置）
            companyInfo.setCompanyName(getCellValueAsString(sheet, 1, 1)); // B2 - 单位名称
            companyInfo.setCompanyAddress(getCellValueAsString(sheet, 2, 1)); // B3 - 单位地址
            companyInfo.setPostCode(getCellValueAsString(sheet, 2, 2)); // C3 - 邮政编码
            companyInfo.setRegisterPlace(getCellValueAsString(sheet, 3, 1)); // B4 - 注册地
            companyInfo.setCreditCode(getCellValueAsString(sheet, 3, 2)); // C4 - 统一社会信用代码
            companyInfo.setOrgType(getCellValueAsString(sheet, 4, 1)); // B5 - 组织机构类型
            companyInfo.setLegalRepresentative(getCellValueAsString(sheet, 4, 2)); // C5 - 法定代表人姓名及国籍
            companyInfo.setBranchUnit(getCellValueAsString(sheet, 5, 1)); // B6 - 分支单位
            companyInfo.setOperationControl(getCellValueAsString(sheet, 6, 1)); // B7 - 运营控制情况
            companyInfo.setPersonnelSituation(getCellValueAsString(sheet, 7, 1)); // B8 - 人员情况
            companyInfo.setBusinessScope(getCellValueAsString(sheet, 8, 1)); // B9 - 经营范围
            companyInfo.setListingSituation(getCellValueAsString(sheet, 9, 1)); // B10 - 上市情况
            companyInfo.setDataClassification(getCellValueAsString(sheet, 10, 1)); // B11 - 是否开展了数据分类分级
            companyInfo.setDataRecord(getCellValueAsString(sheet, 11, 1)); // B12 - 重要数据、核心数据是否备案
            companyInfo.setRecordReply(getCellValueAsString(sheet, 12, 1)); // B13 - 备案回复情况
            companyInfo.setTimesNumber(getCellValueAsString(sheet, 13, 1)); // B14 - 本次评估是第几次数据安全风险评估

            log.info("成功读取Sheet1固定单元格数据: {}", companyInfo);
            return companyInfo;

        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 读取Sheet0的固定单元格数据
     */
    public static ExcelContactUserInfo readSheet0FixedCells(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        try {
            // 尝试创建工作簿
            workbook = WorkbookFactory.create(inputStream);

            // 获取Sheet0
            Sheet sheet = workbook.getSheetAt(0); // Sheet0是索引0
            if (sheet == null) {
                throw new IOException("Sheet0不存在");
            }

            ExcelContactUserInfo contactInfo = new ExcelContactUserInfo();

            // 读取固定单元格的值
            contactInfo.setContactName(getCellValueAsString(sheet, 1, 1)); // B2 - 姓名
            contactInfo.setJob(getCellValueAsString(sheet, 1, 3)); // D2 - 职务/职称
            contactInfo.setDept(getCellValueAsString(sheet, 2, 1)); // B3 - 所属部门
            contactInfo.setTel(getCellValueAsString(sheet, 2, 3)); // D3 - 办公电话
            contactInfo.setMobile(getCellValueAsString(sheet, 3, 1)); // B4 - 移动电话
            contactInfo.setEmail(getCellValueAsString(sheet, 3, 3)); // D4 - 电子邮件

            log.info("成功读取Sheet0固定单元格数据: {}", contactInfo);
            return contactInfo;

        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 读取Sheet2中F2单元格的组织架构示意图链接，并遍历目录获取图片信息
     */
    public static String readSheet2FlowChart(InputStream inputStream, String workingDirectory) throws IOException {
        Workbook workbook = null;
        try {
            // 尝试创建工作簿
            workbook = WorkbookFactory.create(inputStream);

            // 获取Sheet2
            Sheet sheet = workbook.getSheetAt(2); // Sheet2是索引2
            if (sheet == null) {
                throw new IOException("Sheet2不存在");
            }

            // 读取F2单元格（行索引1，列索引5）
            Row row = sheet.getRow(1);
            if (row == null) {
                log.warn("Sheet2第2行不存在");
                return "[]"; // 返回空JSON数组
            }

            Cell cell = row.getCell(5); // F列是索引5
            if (cell == null) {
                log.warn("Sheet2 F2单元格不存在");
                return "[]"; // 返回空JSON数组
            }

            String linkAddress = null;

            // 检查是否有超链接
            Hyperlink hyperlink = cell.getHyperlink();
            if (hyperlink != null) {
                linkAddress = hyperlink.getAddress();
                log.info("成功读取Sheet2 F2单元格的组织架构示意图链接: {}", linkAddress);
            } else {
                // 如果没有超链接，尝试读取单元格的文本内容
                linkAddress = getCellValueAsString(sheet, 1, 5);
                log.info("Sheet2 F2单元格没有超链接，读取文本内容: {}", linkAddress);
            }

            // 如果没有链接地址，返回空JSON数组
            if (linkAddress == null || linkAddress.trim().isEmpty()) {
                log.warn("未找到组织架构示意图链接地址");
                return "[]";
            }

            // 遍历目录获取图片信息，使用工作目录作为基础路径
            List<ImageInfo> imageInfoList = scanImagesFromPath(linkAddress, workingDirectory);

            // 转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonResult = objectMapper.writeValueAsString(imageInfoList);

            log.info("成功扫描到{}张图片，JSON结果: {}", imageInfoList.size(), jsonResult);
            return jsonResult;

        } catch (JsonProcessingException e) {
            log.error("转换图片信息为JSON时发生错误: {}", e.getMessage());
            return "[]";
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 扫描指定路径下的所有图片文件
     */
    private static List<ImageInfo> scanImagesFromPath(String pathStr, String workingDirectory) {
        List<ImageInfo> imageInfos = new ArrayList<>();
        
        try {
            Path fullPath;
            if (Paths.get(pathStr).isAbsolute()) {
                fullPath = Paths.get(pathStr);
            } else {
                fullPath = Paths.get(workingDirectory, pathStr);
            }
            
            log.info("扫描图片路径: {}", fullPath.toAbsolutePath());
            
            if (!Files.exists(fullPath)) {
                log.warn("路径不存在: {}", fullPath.toAbsolutePath());
                return imageInfos;
            }
            
            if (Files.isDirectory(fullPath)) {
                try (Stream<Path> paths = Files.walk(fullPath)) {
                    paths.filter(Files::isRegularFile)
                         .filter(FixedCellExcelReader::isImageFile)
                         .forEach(imagePath -> {
                             String fileName = imagePath.getFileName().toString();
                             String filePath = imagePath.toAbsolutePath().toString();
                             imageInfos.add(new ImageInfo(fileName, filePath));
                             log.debug("找到图片文件: {}", fileName);
                         });
                }
            } else if (Files.isRegularFile(fullPath) && isImageFile(fullPath)) {
                String fileName = fullPath.getFileName().toString();
                String filePath = fullPath.toAbsolutePath().toString();
                imageInfos.add(new ImageInfo(fileName, filePath));
                log.debug("找到图片文件: {}", fileName);
            }
            
        } catch (Exception e) {
            log.error("扫描图片文件时发生异常: {}", e.getMessage(), e);
        }
        
        return imageInfos;
    }

    /**
     * 判断文件是否为图片文件
     */
    private static boolean isImageFile(Path filePath) {
        String fileName = filePath.getFileName().toString().toLowerCase();
        return IMAGE_EXTENSIONS.stream().anyMatch(fileName::endsWith);
    }

    /**
     * 获取单元格的字符串值
     */
    private static String getCellValueAsString(Sheet sheet, int rowIndex, int colIndex) {
        try {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                return "";
            }
            
            Cell cell = row.getCell(colIndex);
            if (cell == null) {
                return "";
            }
            
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return cell.getStringCellValue().trim();
                    } catch (Exception e) {
                        try {
                            double numericValue = cell.getNumericCellValue();
                            if (numericValue == (long) numericValue) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        } catch (Exception ex) {
                            return "";
                        }
                    }
                case BLANK:
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("读取单元格[{},{}]时发生异常: {}", rowIndex, colIndex, e.getMessage());
            return "";
        }
    }
}
