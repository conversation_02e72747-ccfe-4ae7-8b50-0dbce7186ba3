# 多Sheet Excel导入功能测试指南

## 测试概述

本测试指南用于验证多Sheet Excel导入功能，该功能使用EasyExcel和对应的listener处理包含5个sheet的Excel文件。

## Excel文件结构

### 测试文件：`target_company_multi_sheet.xlsx`

#### Sheet0: 联系人信息
```
列A: 姓名 | 列B: 职务/职称 | 列C: 所属部门 | 列D: 办公电话 | 列E: 移动电话 | 列F: 电子邮件
张三    | 经理        | 技术部      | 010-12345678 | 13800138000 | <EMAIL>
李四    | 总监        | 运营部      | 021-87654321 | 13900139000 | <EMAIL>
```

#### Sheet1: 公司信息基本部分
```
列A: 公司名称 | 列B: 单位简称 | 列C: 公司地址 | 列D: 邮政编码 | 列E: 联系人姓名 | 列F: 职务 | 列G: 部门 | 列H: 电话 | 列I: 手机号码 | 列J: 邮箱 | 列K: 注册地 | 列L: 统一社会信用代码 | 列M: 组织类型 | 列N: 法定代表人 | 列O: 分支机构 | 列P: 运营控制 | 列Q: 人员情况 | 列R: 经营范围 | 列S: 上市情况
测试公司A    | 公司A      | 北京市朝阳区 | 100000    | 张三         | 经理   | 技术部 | 010-12345678 | 13800138000 | <EMAIL> | 北京 | 91110000000000000A | 有限公司 | 张三 | 无 | 自主运营 | 100人 | 软件开发 | 未上市
```

#### Sheet2: 部门信息
```
列A: 所属单位ID | 列B: 部门名称 | 列C: 部门职责 | 列D: 部门负责人 | 列E: 状态 | 列F: 备注
1             | 技术部      | 负责系统开发和维护 | 张三 | 1 | 技术部门
1             | 运营部      | 负责业务运营 | 李四 | 1 | 运营部门
```

#### Sheet3: 人员信息
```
列A: 所属单位ID | 列B: 数据处理活动 | 列C: 所属部门ID | 列D: 人员姓名 | 列E: 岗位或角色名称 | 列F: 岗位职责 | 列G: 所属部门 | 列H: 涉及的数据处理活动 | 列I: 是否专职 | 列J: 国籍 | 列K: 状态 | 列L: 备注
1             | 数据开发     | 1          | 张三      | 技术经理        | 负责技术团队管理 | 技术部 | 数据开发 | 是 | 中国 | 1 | 技术负责人
1             | 数据运营     | 2          | 李四      | 运营总监        | 负责运营策略制定 | 运营部 | 数据运营 | 是 | 中国 | 1 | 运营负责人
```

#### Sheet4: 文档信息
```
列A: 所属单位ID | 列B: 文档名称 | 列C: 主要内容 | 列D: 使用范围 | 列E: 数据处理活动 | 列F: 状态 | 列G: 备注
1             | 数据安全管理制度 | 规范数据安全管理流程 | 全公司 | 数据管理 | 1 | 重要制度
1             | 技术开发规范 | 软件开发技术标准 | 技术部 | 数据开发 | 1 | 技术规范
```

## 测试步骤

### 1. 准备测试环境

1. 确保服务已启动
2. 确保用户具有 `targetCompany:companyInfo:import` 权限
3. 准备包含5个sheet的Excel测试文件

### 2. API测试

#### 使用curl命令测试
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importMultiSheetExcel" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@target_company_multi_sheet.xlsx"
```

#### 使用Postman测试
1. 创建POST请求：`http://localhost:8080/targetCompany/companyInfo/importMultiSheetExcel`
2. 在Body中选择form-data
3. 添加key为"file"，类型为File的参数
4. 选择测试Excel文件
5. 发送请求

### 3. 预期结果

#### 成功响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 6,
    "totalFailure": 0,
    "message": "多Sheet导入完成。总计成功：6条，失败：0条。详情：公司信息：成功1条，失败0条；部门信息：成功2条，失败0条；人员信息：成功2条，失败0条；文档信息：成功2条，失败0条；"
  }
}
```

#### 部分失败响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 5,
    "totalFailure": 1,
    "message": "多Sheet导入完成。总计成功：5条，失败：1条。详情：公司信息：成功1条，失败0条；部门信息：成功2条，失败0条；人员信息：成功1条，失败1条；文档信息：成功2条，失败0条；Sheet3第2行，第1列，值\"abc\"转换失败；"
  }
}
```

### 4. 数据验证

导入完成后，验证数据是否正确保存：

#### 验证公司信息
```bash
curl -X GET "http://localhost:8080/targetCompany/companyInfo/list"
```

#### 验证部门信息
```bash
curl -X GET "http://localhost:8080/targetDept/info/list"
```

#### 验证人员信息
```bash
curl -X GET "http://localhost:8080/targetMember/info/list"
```

#### 验证文档信息
```bash
curl -X GET "http://localhost:8080/targetDocuments/documents/list"
```

## 错误测试场景

### 1. 文件格式错误
- 上传非Excel文件
- 上传损坏的Excel文件
- 预期：返回相应的错误信息

### 2. Sheet缺失
- 上传只包含部分sheet的Excel文件
- 预期：缺失的sheet不会被处理，其他sheet正常导入

### 3. 数据类型错误
- 在数字字段中填入文本
- 在必需字段中留空
- 预期：返回具体的错误信息，包含行号和列号

### 4. 外键约束错误
- 在部门、人员、文档信息中使用不存在的orgId
- 预期：保存失败，返回相应错误信息

## 性能测试

### 1. 大数据量测试
- 创建包含1000条记录的Excel文件
- 测试导入时间和内存使用情况
- 验证所有数据是否正确导入

### 2. 并发测试
- 同时进行多个导入操作
- 测试系统的并发处理能力

## 日志验证

检查应用日志，确认以下信息：

1. **Sheet解析日志**
```
Sheet1 Excel 数据解析完成，共 1 条
Sheet0 Excel 数据解析完成，共 1 条
Sheet2 Excel 数据解析完成，共 2 条
Sheet3 Excel 数据解析完成，共 2 条
Sheet4 Excel 数据解析完成，共 2 条
```

2. **数据处理日志**
```
Sheet1 table row: ExcelTargetCompanyInfo1(companyName=测试公司A, ...)
Sheet0 table row: ExcelTargetCompanyInfo0(dataClassification=是, ...)
Sheet2 table row: ExcelTargetOrgDeptInfo(orgId=1, departmentName=技术部, ...)
```

3. **错误日志**（如果有错误）
```
Sheet3第2行，第1列，值"abc"转换失败
```

## 注意事项

1. **数据顺序**：确保Sheet1和Sheet0的数据行数一致，因为它们会按索引合并
2. **数据类型**：所有Excel中的数据都是字符串类型，系统会自动转换为相应的数据类型
3. **必需字段**：确保必需字段不为空，否则会导致保存失败
4. **外键关系**：部门、人员、文档信息的orgId必须是已存在的公司ID

## 故障排除

### 常见问题及解决方案

1. **导入失败，提示权限不足**
   - 检查用户是否具有 `targetCompany:companyInfo:import` 权限

2. **部分数据导入失败**
   - 检查Excel文件中的数据格式是否正确
   - 查看返回的错误信息，定位具体的错误行和列

3. **文件上传失败**
   - 检查文件大小是否超过限制
   - 确认文件格式为Excel (.xlsx)

4. **数据库保存失败**
   - 检查数据库连接是否正常
   - 确认外键关系是否正确
