package com.ruoyi.evaluatePlan.strategy;

import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;

/**
 * 流程感知的评估策略接口
 * 扩展基础策略接口，增加流程管理功能
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ProcessAwareEvaluateStrategy extends EvaluateStrategy {

    /**
     * 获取流程编码
     * @return 流程编码，对应dsa_process_definition.code
     */
    default String getProcessCode() {
        return getEvaluateType();
    }

    /**
     * 执行指定步骤
     * @param request 评估请求
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 步骤执行结果
     */
    EvaluatePlanResponse executeStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId);

    /**
     * 验证步骤执行条件
     * @param request 评估请求
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 验证结果
     */
    boolean validateStepCondition(EvaluatePlanRequest request, String stepCode, Long processInstanceId);

    /**
     * 获取步骤执行结果
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 步骤结果
     */
    Object getStepResult(String stepCode, Long processInstanceId);

    /**
     * 处理步骤回退
     * @param request 评估请求
     * @param fromStepCode 源步骤编码
     * @param toStepCode 目标步骤编码
     * @param processInstanceId 流程实例ID
     * @param reason 回退原因
     * @return 回退处理结果
     */
    boolean handleStepRollback(EvaluatePlanRequest request, String fromStepCode, String toStepCode, 
                              Long processInstanceId, String reason);

    /**
     * 获取可回退的步骤列表
     * @param currentStepCode 当前步骤编码
     * @param processInstanceId 流程实例ID
     * @return 可回退的步骤编码列表
     */
    java.util.List<String> getAvailableRollbackSteps(String currentStepCode, Long processInstanceId);

    /**
     * 检查是否可以跳过某个步骤
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 是否可以跳过
     */
    boolean canSkipStep(String stepCode, Long processInstanceId);

    /**
     * 获取步骤预估执行时间（毫秒）
     * @param stepCode 步骤编码
     * @return 预估执行时间
     */
    Long getEstimatedStepDuration(String stepCode);

    /**
     * 获取步骤依赖关系
     * @param stepCode 步骤编码
     * @return 依赖的步骤编码列表
     */
    java.util.List<String> getStepDependencies(String stepCode);
}
