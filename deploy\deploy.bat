@echo off
setlocal enabledelayedexpansion

REM RuoYi Data Security Assessment Platform Docker Deploy Script (Windows)
REM Usage: deploy.bat [start|stop|restart|build|logs|status]

set PROJECT_NAME=ruoyi-data-security
set JAR_NAME=ruoyi_admin.jar

REM Main logic
REM If parameters provided, use command line mode
if not "%~1"=="" (
    call :handle_command_line %*
    goto :end
)

REM Interactive menu mode
:main_menu
cls
call :show_menu
call :get_user_choice
goto :main_menu

:end
goto :eof

REM Check if Docker and Docker Compose are installed
:check_requirements
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker not installed, please install Docker Desktop first
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose not installed, please install Docker Compose first
    pause
    exit /b 1
)

REM Check if required files exist
:check_files
if not exist "docker-compose.yml" (
    echo [ERROR] File docker-compose.yml not found
    pause
    exit /b 1
)
if not exist ".env" (
    echo [ERROR] File .env not found
    pause
    exit /b 1
)
if not exist "Dockerfile" (
    echo [ERROR] File Dockerfile not found
    pause
    exit /b 1
)

REM Check if JAR package exists
:check_jar
echo [INFO] Checking if JAR package exists...
if not exist "%JAR_NAME%" (
    echo [ERROR] JAR file %JAR_NAME% not found in current directory
    echo [INFO] Please rename your compiled JAR package to %JAR_NAME% and place it in current directory
    pause
    exit /b 1
) else (
    echo [SUCCESS] Found JAR package: %JAR_NAME%
)
goto :eof

REM Start services
:start_services
call :check_jar
echo [INFO] Starting Docker services...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] Service startup failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Services started successfully
    echo [INFO] Please wait for services to fully start (about 1-2 minutes)
    echo [INFO] Access URL: http://localhost:8080
    echo [INFO] Default account: admin/admin123
)
goto :eof

REM Stop services
:stop_services
echo [INFO] Stopping Docker services...
docker-compose down
if errorlevel 1 (
    echo [ERROR] Service stop failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Services stopped successfully
)
goto :eof

REM Restart services
:restart_services
echo [INFO] Restarting Docker services...
docker-compose restart
if errorlevel 1 (
    echo [ERROR] Service restart failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Services restarted successfully
)
goto :eof

REM Build Docker images
:build_images
call :check_jar
echo [INFO] Building Docker images...
docker-compose build --no-cache
if errorlevel 1 (
    echo [ERROR] Docker image build failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Docker images built successfully
)
goto :eof

REM View logs
:view_logs
if "%~2"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %~2
)
goto :eof

REM Check service status
:check_status
echo [INFO] Service status:
docker-compose ps
echo.
echo [INFO] Container health status:
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
goto :eof

REM Clean data
:clean_data
echo [WARNING] This operation will delete all data, including database data!
set /p confirm="Confirm to continue? (y/N): "
if /i "!confirm!"=="y" (
    echo [INFO] Stopping services and cleaning data...
    docker-compose down -v
    docker system prune -f
    echo [SUCCESS] Data cleanup completed
) else (
    echo [INFO] Operation cancelled
)
goto :eof

REM Show interactive menu
:show_menu
echo ================================================================================
echo                        若依数据安全风险评估平台
echo                           Docker服务管理工具
echo ================================================================================
echo (1) 检查JAR包状态                         (6) 查看服务状态
echo (2) 构建Docker镜像                       (7) 查看所有日志
echo (3) 启动所有服务                         (8) 查看应用日志
echo (4) 停止所有服务                         (9) 清理所有数据
echo (5) 重启所有服务                         (0) 退出程序
echo ================================================================================
echo.
echo 使用前准备:
echo - 确保当前目录下存在 %JAR_NAME% 文件
echo.
echo 使用说明:
echo - 应用访问地址: http://localhost:8080
echo - 默认登录账号: admin/admin123
echo.
goto :eof

REM Get user choice and execute
:get_user_choice
set /p choice="请输入选项编号: "

if "%choice%"=="1" (
    echo.
    echo [INFO] Checking JAR package...
    call :check_jar
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="2" (
    echo.
    echo [INFO] Building Docker images...
    call :check_requirements
    call :check_files
    call :build_images
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="3" (
    echo.
    echo [INFO] Starting all services...
    call :check_requirements
    call :check_files
    call :start_services
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="4" (
    echo.
    echo [INFO] Stopping all services...
    call :check_requirements
    call :check_files
    call :stop_services
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="5" (
    echo.
    echo [INFO] Restarting all services...
    call :check_requirements
    call :check_files
    call :restart_services
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="6" (
    echo.
    echo [INFO] Viewing service status...
    call :check_requirements
    call :check_files
    call :check_status
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="7" (
    echo.
    echo [INFO] Viewing all logs...
    call :check_requirements
    call :check_files
    call :view_logs
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="8" (
    echo.
    echo [INFO] Viewing application logs...
    call :check_requirements
    call :check_files
    call :view_logs ruoyi-app
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="9" (
    echo.
    echo [WARNING] This will delete all data including database!
    set /p confirm="Are you sure? (y/N): "
    if /i "!confirm!"=="y" (
        echo [INFO] Cleaning all data...
        call :check_requirements
        call :check_files
        call :clean_data
    ) else (
        echo [INFO] Operation cancelled
    )
    echo.
    echo Press any key to return to menu...
    pause >nul
) else if "%choice%"=="0" (
    echo.
    echo [INFO] Exiting...
    exit /b 0
) else (
    echo.
    echo [ERROR] Invalid option: %choice%
    echo Please enter a number between 0-9
    echo.
    echo Press any key to continue...
    pause >nul
)
goto :eof

REM Handle command line mode
:handle_command_line
if "%~1"=="help" (
    call :show_command_help
) else if "%~1"=="--help" (
    call :show_command_help
) else if "%~1"=="-h" (
    call :show_command_help
) else if "%~1"=="check" (
    call :check_jar
) else if "%~1"=="build" (
    call :check_requirements
    call :check_files
    call :build_images
) else if "%~1"=="start" (
    call :check_requirements
    call :check_files
    call :start_services
) else if "%~1"=="stop" (
    call :check_requirements
    call :check_files
    call :stop_services
) else if "%~1"=="restart" (
    call :check_requirements
    call :check_files
    call :restart_services
) else if "%~1"=="logs" (
    call :check_requirements
    call :check_files
    call :view_logs %*
) else if "%~1"=="status" (
    call :check_requirements
    call :check_files
    call :check_status
) else if "%~1"=="clean" (
    call :check_requirements
    call :check_files
    call :clean_data
) else (
    echo [ERROR] Unknown command '%~1'
    echo.
    call :show_command_help
    exit /b 1
)
goto :eof

REM Show command line help
:show_command_help
echo ================================================================================
echo  RuoYi Data Security Assessment Platform - Docker Deploy Script (Windows)
echo ================================================================================
echo.
echo USAGE:
echo   %~nx0                    Interactive menu mode (RECOMMENDED)
echo   %~nx0 [command] [options] Command line mode
echo.
echo COMMANDS:
echo   check            Check if JAR package exists
echo   build            Build Docker images
echo   start            Start all services
echo   stop             Stop all services
echo   restart          Restart all services
echo   logs [service]   View logs (optionally specify service name)
echo   status           View service status
echo   clean            Clean all data (WARNING: deletes all data)
echo   help             Show this help information
echo.
echo EXAMPLES:
echo   %~nx0                Interactive menu
echo   %~nx0 start          Start all services
echo   %~nx0 logs ruoyi-app View application logs only
echo.
echo ================================================================================
goto :eof


