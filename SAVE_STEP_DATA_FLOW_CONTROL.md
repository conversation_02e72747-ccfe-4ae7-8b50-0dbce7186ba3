# 评估计划任务步骤数据保存流程控制功能

## 功能概述

在 `EvaluatePlanTaskServiceImpl` 的 `saveCurrentStepData` 方法中新增了流程控制功能。当步骤数据保存成功后，系统会自动将当前步骤标记为完成状态，实现数据保存与流程控制的联动。

## 功能流程

### 原有流程
1. 参数校验
2. 获取评估计划任务
3. 获取流程实例
4. 获取评估类型
5. 通过工厂获取对应的处理器
6. 调用处理器保存步骤数据

### 新增流程控制
7. **保存成功后，将当前步骤设置为完成状态**
   - 查找当前步骤实例
   - 检查步骤状态
   - 标记步骤为完成

## 核心实现

### 1. 主流程修改

在 `saveCurrentStepData` 方法中，数据保存成功后添加流程控制：

```java
// 7. 保存成功后，将当前步骤设置为完成状态
boolean stepCompleted = completeCurrentStep(Long.valueOf(processInstance.getId()), draftSaveRequest.getStep());
if (!stepCompleted) {
    log.warn("设置步骤完成状态失败，但数据保存成功，任务ID: {}, 步骤: {}", 
            draftSaveRequest.getPlanId(), draftSaveRequest.getStep());
}

log.info("保存步骤数据成功，任务ID: {}, 步骤: {}, 评估类型: {}, 步骤完成状态: {}",
        draftSaveRequest.getPlanId(), draftSaveRequest.getStep(), evaluateType, stepCompleted);
```

### 2. 步骤完成方法

新增 `completeCurrentStep` 私有方法：

```java
private boolean completeCurrentStep(Long processInstanceId, String stepName) {
    try {
        // 1. 获取流程实例的所有步骤实例
        List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);
        
        // 2. 查找当前步骤实例
        ProcessStepInstance currentStepInstance = null;
        for (ProcessStepInstance stepInstance : stepInstances) {
            if (stepName.equals(stepInstance.getStepName())) {
                currentStepInstance = stepInstance;
                break;
            }
        }
        
        // 3. 检查步骤是否已经完成
        if (ProcessStepStatusEnum.FINISHED.getCode().equals(currentStepInstance.getStatus())) {
            log.info("步骤已经完成，无需重复标记，步骤实例ID: {}", currentStepInstance.getId());
            return true;
        }
        
        // 4. 标记步骤为完成
        boolean updateResult = processFlowService.updateStepInstanceStatus(
                currentStepInstance.getId(),
                ProcessStepStatusEnum.FINISHED.getCode(),
                SecurityUtils.getUsername(),
                "步骤数据保存完成，自动标记步骤完成"
        );
        
        return updateResult;
        
    } catch (Exception e) {
        log.error("完成当前步骤时发生异常，流程实例ID: {}, 步骤名称: {}", processInstanceId, stepName, e);
        return false;
    }
}
```

## 功能特点

### 1. 容错处理
- **步骤实例不存在**：记录警告日志，但不影响数据保存结果
- **步骤已完成**：检查步骤状态，避免重复标记
- **标记失败**：记录警告日志，但不影响数据保存结果
- **异常处理**：捕获所有异常，确保数据保存不受影响

### 2. 日志记录
- **成功日志**：记录步骤完成状态和相关信息
- **警告日志**：记录各种异常情况
- **错误日志**：记录异常详情，便于问题排查

### 3. 状态管理
- 使用 `ProcessStepStatusEnum.FINISHED` 标记步骤完成
- 自动设置操作人和备注信息
- 通过流程服务统一管理步骤状态

## 业务价值

### 1. 数据与流程同步
- 确保数据保存与流程状态的一致性
- 避免数据已保存但流程状态未更新的情况

### 2. 自动化流程控制
- 减少手动操作，提高效率
- 降低人为错误的可能性

### 3. 流程可视化
- 用户可以清楚地看到当前流程进度
- 便于项目管理和进度跟踪

## 使用场景

1. **表单数据保存**：用户填写并保存表单数据时，自动推进流程
2. **步骤完成确认**：系统自动确认步骤完成，无需手动操作
3. **流程进度跟踪**：实时更新流程状态，便于监控项目进度
4. **质量控制**：确保每个步骤都有明确的完成标记

## 注意事项

### 1. 幂等性
- 方法具有幂等性，重复调用不会产生副作用
- 已完成的步骤不会被重复标记

### 2. 事务处理
- 流程控制在数据保存成功后执行
- 流程控制失败不会影响数据保存结果

### 3. 权限控制
- 使用当前登录用户作为操作人
- 遵循现有的权限控制机制

### 4. 性能考虑
- 流程控制操作相对轻量
- 不会显著影响数据保存性能

## 错误处理

| 情况 | 处理方式 | 影响 |
|------|----------|------|
| 步骤实例不存在 | 记录警告日志，返回false | 不影响数据保存 |
| 步骤已完成 | 记录信息日志，返回true | 正常情况 |
| 标记失败 | 记录警告日志，返回false | 不影响数据保存 |
| 系统异常 | 记录错误日志，返回false | 不影响数据保存 |

## 监控建议

1. **监控警告日志**：关注步骤完成失败的情况
2. **流程进度统计**：定期检查流程完成情况
3. **性能监控**：监控方法执行时间，确保性能稳定
