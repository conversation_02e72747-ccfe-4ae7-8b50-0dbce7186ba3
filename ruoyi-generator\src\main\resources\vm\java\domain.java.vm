package ${packageName}.domain;

    #foreach ($import in $importList)
    import ${import};
    #end
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="MyBaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("${tableName}")
public class ${ClassName} extends ${Entity} {
    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
    @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
            #else
    @Excel(name = "${comment}")
            #end
        #end
        #if(${column.isPk} == "1")
    @TableId(value = "${column.columnName}" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
        #else
    @TableField(value = "${column.columnName}")
        #end
    private $column.javaType $column.javaField;
    #end

#end
}