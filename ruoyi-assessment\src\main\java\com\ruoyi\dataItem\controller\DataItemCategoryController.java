package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemCategory;
import com.ruoyi.dataItem.service.IDataItemCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 数据项分类Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/category")
@Api(value = "数据项分类控制器", tags = {"数据项分类管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemCategoryController extends BaseController
{
    private final IDataItemCategoryService dataItemCategoryService;

    /**
     * 查询数据项分类列表
     */
    @ApiOperation("查询数据项分类列表")
    @PreAuthorize("@ss.hasPermi('dataItem:category:list')")
    @GetMapping("/list")
    public AjaxResult list(DataItemCategory dataItemCategory) {
        List<DataItemCategory> list = dataItemCategoryService.list(new QueryWrapper<DataItemCategory>(dataItemCategory));
        return AjaxResult.success(list);
    }

    /**
     * 获取数据项分类详细信息
     */
    @ApiOperation("获取数据项分类详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemCategoryService.getById(id));
    }

    /**
     * 新增数据项分类
     */
    @ApiOperation("新增数据项分类")
    @PreAuthorize("@ss.hasPermi('dataItem:category:add')")
    @Log(title = "数据项分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataItemCategory dataItemCategory) {
        return toAjax(dataItemCategoryService.save(dataItemCategory));
    }

    /**
     * 修改数据项分类
     */
    @ApiOperation("修改数据项分类")
    @PreAuthorize("@ss.hasPermi('dataItem:category:edit')")
    @Log(title = "数据项分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemCategory dataItemCategory) {
        return toAjax(dataItemCategoryService.updateById(dataItemCategory));
    }

    /**
     * 删除数据项分类
     */
    @ApiOperation("删除数据项分类")
    @PreAuthorize("@ss.hasPermi('dataItem:category:remove')")
    @Log(title = "数据项分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemCategoryService.removeByIds(Arrays.asList(ids)));
    }
}