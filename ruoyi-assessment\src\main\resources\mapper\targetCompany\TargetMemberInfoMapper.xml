<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.company.mapper.TargetMemberInfoMapper">
    
    <resultMap type="TargetMemberInfo" id="TargetMemberInfoResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="processActivityId"    column="process_activity_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="name"    column="name"    />
        <result property="post"    column="post"    />
        <result property="duty"    column="duty"    />
        <result property="department"    column="department"    />
        <result property="dataProcessing"    column="data_processing"    />
        <result property="fullTime"    column="full_time"    />
        <result property="nationality"    column="nationality"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTargetMemberInfoVo">
        select id, org_id, process_activity_id, dept_id, name, post, duty, department, data_processing, full_time, nationality, status, create_by, create_time, update_by, update_time, remark, del_flag from dsa_target_member_info
    </sql>

    <select id="selectTargetMemberInfoList" parameterType="TargetMemberInfo" resultMap="TargetMemberInfoResult">
        <include refid="selectTargetMemberInfoVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="processActivityId != null  and processActivityId != ''"> and process_activity_id = #{processActivityId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="post != null  and post != ''"> and post = #{post}</if>
            <if test="duty != null  and duty != ''"> and duty = #{duty}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="dataProcessing != null  and dataProcessing != ''"> and data_processing = #{dataProcessing}</if>
            <if test="fullTime != null  and fullTime != ''"> and full_time = #{fullTime}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTargetMemberInfoById" parameterType="Long" resultMap="TargetMemberInfoResult">
        <include refid="selectTargetMemberInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTargetMemberInfo" parameterType="TargetMemberInfo" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_target_member_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="processActivityId != null">process_activity_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="name != null">name,</if>
            <if test="post != null">post,</if>
            <if test="duty != null">duty,</if>
            <if test="department != null">department,</if>
            <if test="dataProcessing != null">data_processing,</if>
            <if test="fullTime != null">full_time,</if>
            <if test="nationality != null">nationality,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="processActivityId != null">#{processActivityId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="name != null">#{name},</if>
            <if test="post != null">#{post},</if>
            <if test="duty != null">#{duty},</if>
            <if test="department != null">#{department},</if>
            <if test="dataProcessing != null">#{dataProcessing},</if>
            <if test="fullTime != null">#{fullTime},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTargetMemberInfo" parameterType="TargetMemberInfo">
        update dsa_target_member_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="processActivityId != null">process_activity_id = #{processActivityId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="post != null">post = #{post},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="department != null">department = #{department},</if>
            <if test="dataProcessing != null">data_processing = #{dataProcessing},</if>
            <if test="fullTime != null">full_time = #{fullTime},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTargetMemberInfoById" parameterType="Long">
        delete from dsa_target_member_info where id = #{id}
    </delete>

    <delete id="deleteTargetMemberInfoByIds" parameterType="String">
        delete from dsa_target_member_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>