# Sheet0 联系人信息示例

## 概述

根据您提供的Sheet0结构，这是一个联系人信息表，包含以下字段：

## Excel表格结构

### 表头（第1行）
```
联系人信息
```

### 字段行（第2行）
```
姓名 | 职务/职称 | 所属部门 | 办公电话
```

### 数据行（第3行及以后）
```
移动电话 | 电子邮件
```

## 字段映射关系

| Excel列 | 字段名称 | 对应TargetCompanyInfo字段 | 说明 |
|---------|----------|---------------------------|------|
| A列 | 姓名 | contactName | 公司联系人姓名 |
| B列 | 职务/职称 | job | 联系人职务 |
| C列 | 所属部门 | dept | 联系人所属部门 |
| D列 | 办公电话 | tel | 公司固定电话 |
| E列 | 移动电话 | mobile | 公司移动电话 |
| F列 | 电子邮件 | email | 公司邮箱地址 |

## 数据示例

### Excel文件内容示例
```
A1: 联系人信息
A2: 姓名        B2: 职务/职称    C2: 所属部门    D2: 办公电话
A3: 移动电话    B3: 电子邮件
A4: 张三        B4: 技术经理     C4: 技术部      D4: 010-12345678
A5: 13800138000 B5: <EMAIL>
A6: 李四        B6: 运营总监     C6: 运营部      D6: 021-87654321
A7: 13900139000 B7: <EMAIL>
```

### 对应的数据库记录
导入后，会在TargetCompanyInfo表中创建记录：

**记录1：**
- contactName: "张三"
- job: "技术经理"
- dept: "技术部"
- tel: "010-12345678"
- mobile: "13800138000"
- email: "<EMAIL>"

**记录2：**
- contactName: "李四"
- job: "运营总监"
- dept: "运营部"
- tel: "021-87654321"
- mobile: "13900139000"
- email: "<EMAIL>"

## 数据处理逻辑

1. **读取顺序**：系统会先读取Sheet1的公司基本信息，再读取Sheet0的联系人信息
2. **数据合并**：Sheet0的联系人信息会合并到对应的公司记录中
3. **索引对应**：Sheet0的第i行联系人信息对应Sheet1的第i行公司信息
4. **字段映射**：联系人信息的各个字段会映射到公司信息的对应字段

## 注意事项

1. **数据对应**：确保Sheet0和Sheet1的数据行数一致，按行索引进行对应
2. **必填字段**：姓名字段建议填写，作为联系人的基本标识
3. **电话格式**：电话号码建议使用标准格式，如：010-12345678
4. **邮箱格式**：邮箱地址需要符合标准邮箱格式
5. **部门信息**：所属部门应与实际的组织架构保持一致

## 错误处理

如果Sheet0中的数据格式不正确，系统会记录具体的错误信息：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 1,
    "totalFailure": 1,
    "message": "多Sheet导入完成。总计成功：1条，失败：1条。详情：公司信息：成功1条，失败0条；Sheet0联系人信息第3行，第5列，值\"invalid-email\"转换失败；"
  }
}
```

## 测试建议

1. **正常数据测试**：使用完整、正确的联系人信息进行测试
2. **缺失数据测试**：测试部分字段为空的情况
3. **格式错误测试**：测试邮箱格式错误、电话格式错误等情况
4. **特殊字符测试**：测试姓名中包含特殊字符的情况

## 扩展说明

如果需要支持多个联系人，可以考虑：

1. **多行数据**：在Sheet0中添加多行联系人信息
2. **关联关系**：通过公司ID建立联系人与公司的关联关系
3. **独立表结构**：创建独立的联系人表，与公司表建立外键关系

当前的实现是将联系人信息直接合并到公司信息中，适合每个公司只有一个主要联系人的场景。
