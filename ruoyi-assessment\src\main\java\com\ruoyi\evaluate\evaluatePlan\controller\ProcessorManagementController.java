package com.ruoyi.evaluate.evaluatePlan.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluatePlan.service.processor.ProcessorHotReloadHelper;
import com.ruoyi.evaluate.evaluatePlan.service.processor.ProcessorRegistry;
import com.ruoyi.evaluate.evaluatePlan.service.processor.StepDataProcessorFactory;
import com.ruoyi.evaluate.evaluatePlan.service.processor.IStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.ProcessorManagementRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 处理器管理控制器
 * <p>
 * 提供处理器的手动注册、刷新、状态查询等管理功能
 * 主要用于开发环境的处理器热重载和运维管理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/evaluate/processor")
@Api(value = "处理器管理控制器", tags = {"处理器管理"})
@ConditionalOnProperty(name = "processor.management.enabled", havingValue = "true", matchIfMissing = true)
public class ProcessorManagementController extends BaseController {

    @Autowired
    private ProcessorRegistry processorRegistry;

    @Autowired
    private StepDataProcessorFactory processorFactory;

    @Autowired(required = false)
    private ProcessorHotReloadHelper hotReloadHelper;

    /**
     * 获取处理器注册表状态
     */
    @ApiOperation("获取处理器注册表状态")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:status')")
    @GetMapping("/status")
    public AjaxResult getProcessorStatus() {
        try {
            log.info("获取处理器注册表状态");
            
            Map<String, Object> status = processorRegistry.getRegistryStatus();
            
            // 添加热重载状态信息
            if (hotReloadHelper != null) {
                status.put("hotReloadEnabled", hotReloadHelper.isHotReloadEnabled());
                status.put("hotReloadHelper", "已启用");
            } else {
                status.put("hotReloadEnabled", false);
                status.put("hotReloadHelper", "未启用");
            }
            
            return AjaxResult.success("获取处理器状态成功", status);
            
        } catch (Exception e) {
            log.error("获取处理器状态失败", e);
            return AjaxResult.error("获取处理器状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取处理器状态报告
     */
    @ApiOperation("获取处理器状态报告")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:report')")
    @GetMapping("/report")
    public AjaxResult getProcessorReport() {
        try {
            log.info("生成处理器状态报告");
            
            String report;
            if (hotReloadHelper != null) {
                report = hotReloadHelper.getStatusReport();
            } else {
                // 如果热重载助手未启用，生成基础报告
                Map<String, Object> status = processorRegistry.getRegistryStatus();
                StringBuilder sb = new StringBuilder();
                sb.append("=== 处理器状态报告 ===\n");
                sb.append(String.format("总处理器数: %s\n", status.get("totalProcessors")));
                sb.append(String.format("特定处理器: %s\n", status.get("specificProcessors")));
                sb.append(String.format("通用处理器: %s\n", status.get("commonProcessors")));
                sb.append(String.format("默认处理器: %s\n", 
                    (Boolean) status.get("hasDefaultProcessor") ? "已配置" : "未配置"));
                sb.append(String.format("注册表状态: %s\n", 
                    (Boolean) status.get("initialized") ? "已初始化" : "未初始化"));
                sb.append(String.format("最后注册时间: %s\n", status.get("lastRegistrationTime")));
                report = sb.toString();
            }
            
            return AjaxResult.success("生成状态报告成功", report);
            
        } catch (Exception e) {
            log.error("生成处理器状态报告失败", e);
            return AjaxResult.error("生成状态报告失败: " + e.getMessage());
        }
    }

    /**
     * 手动刷新处理器注册表
     */
    @ApiOperation("手动刷新处理器注册表")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:refresh')")
    @Log(title = "处理器管理", businessType = BusinessType.UPDATE)
    @PostMapping("/refresh")
    public AjaxResult refreshProcessors() {
        try {
            log.info("开始手动刷新处理器注册表");
            
            if (hotReloadHelper != null) {
                hotReloadHelper.refreshProcessors();
            } else {
                processorRegistry.refreshRegistry();
            }
            
            // 获取刷新后的状态
            Map<String, Object> status = processorRegistry.getRegistryStatus();
            
            log.info("处理器注册表刷新成功");
            return AjaxResult.success("处理器注册表刷新成功", status);
            
        } catch (Exception e) {
            log.error("刷新处理器注册表失败", e);
            return AjaxResult.error("刷新处理器注册表失败: " + e.getMessage());
        }
    }

    /**
     * 强制重新注册所有处理器
     */
    @ApiOperation("强制重新注册所有处理器")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:reregister')")
    @Log(title = "处理器管理", businessType = BusinessType.UPDATE)
    @PostMapping("/reregister")
    public AjaxResult forceReregisterAll() {
        try {
            log.info("开始强制重新注册所有处理器");
            
            if (hotReloadHelper != null) {
                hotReloadHelper.forceReregisterAll();
            } else {
                processorRegistry.forceReregister();
            }
            
            // 获取重新注册后的状态
            Map<String, Object> status = processorRegistry.getRegistryStatus();
            
            log.info("强制重新注册所有处理器成功");
            return AjaxResult.success("强制重新注册成功", status);
            
        } catch (Exception e) {
            log.error("强制重新注册处理器失败", e);
            return AjaxResult.error("强制重新注册失败: " + e.getMessage());
        }
    }

    /**
     * 测试处理器功能
     */
    @ApiOperation("测试处理器功能")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:test')")
    @PostMapping("/test")
    public AjaxResult testProcessors() {
        try {
            log.info("开始测试处理器功能");
            
            if (hotReloadHelper != null) {
                hotReloadHelper.testProcessors();
                return AjaxResult.success("处理器功能测试完成，详情请查看日志");
            } else {
                // 如果热重载助手未启用，执行基础测试
                Map<String, Object> testResults = performBasicProcessorTest();
                return AjaxResult.success("处理器功能测试完成", testResults);
            }
            
        } catch (Exception e) {
            log.error("测试处理器功能失败", e);
            return AjaxResult.error("测试处理器功能失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的评估类型列表
     */
    @ApiOperation("获取支持的评估类型列表")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:types')")
    @GetMapping("/types")
    public AjaxResult getSupportedEvaluateTypes() {
        try {
            Set<String> supportedTypes = processorRegistry.getSupportedEvaluateTypes();
            
            Map<String, Object> result = new HashMap<>();
            result.put("supportedTypes", supportedTypes);
            result.put("count", supportedTypes.size());
            
            return AjaxResult.success("获取支持的评估类型成功", result);
            
        } catch (Exception e) {
            log.error("获取支持的评估类型失败", e);
            return AjaxResult.error("获取支持的评估类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定评估类型支持的步骤列表
     */
    @ApiOperation("获取指定评估类型支持的步骤列表")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:steps')")
    @GetMapping("/steps/{evaluateType}")
    public AjaxResult getSupportedStepCodes(
            @ApiParam(value = "评估类型", required = true) @PathVariable String evaluateType) {
        try {
            Set<String> supportedSteps = processorRegistry.getSupportedStepCodes(evaluateType);
            
            Map<String, Object> result = new HashMap<>();
            result.put("evaluateType", evaluateType);
            result.put("supportedSteps", supportedSteps);
            result.put("count", supportedSteps.size());
            
            return AjaxResult.success("获取支持的步骤列表成功", result);
            
        } catch (Exception e) {
            log.error("获取支持的步骤列表失败: evaluateType={}", evaluateType, e);
            return AjaxResult.error("获取支持的步骤列表失败: " + e.getMessage());
        }
    }

    /**
     * 查找指定处理器
     */
    @ApiOperation("查找指定处理器")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:find')")
    @GetMapping("/find")
    public AjaxResult findProcessor(
            @ApiParam(value = "评估类型") @RequestParam(required = false) String evaluateType,
            @ApiParam(value = "步骤编码") @RequestParam(required = false) String stepCode) {
        try {
            IStepDataProcessor processor = processorFactory.getProcessor(evaluateType, stepCode);
            
            Map<String, Object> result = new HashMap<>();
            if (processor != null) {
                result.put("found", true);
                result.put("processorClass", processor.getClass().getSimpleName());
                result.put("processorName", processor.getClass().getName());
                result.put("evaluateType", processor.getEvaluateType());
                result.put("stepCode", processor.getStepCode());
            } else {
                result.put("found", false);
                result.put("message", "未找到匹配的处理器");
            }
            
            Map<String, String> searchCriteria = new HashMap<>();
            searchCriteria.put("evaluateType", evaluateType != null ? evaluateType : "null");
            searchCriteria.put("stepCode", stepCode != null ? stepCode : "null");
            result.put("searchCriteria", searchCriteria);
            
            return AjaxResult.success("处理器查找完成", result);
            
        } catch (Exception e) {
            log.error("查找处理器失败: evaluateType={}, stepCode={}", evaluateType, stepCode, e);
            return AjaxResult.error("查找处理器失败: " + e.getMessage());
        }
    }

    /**
     * 执行基础处理器测试
     */
    private Map<String, Object> performBasicProcessorTest() {
        Map<String, Object> testResults = new HashMap<>();
        List<Map<String, Object>> testCases = new ArrayList<>();
        
        // 测试数据安全评估处理器
        testCases.add(testProcessorMatch("data_security_plan", "create_plan", "数据安全-创建计划"));
        testCases.add(testProcessorMatch("data_security_plan", "evaluate_scope", "数据安全-评估范围"));
        
        // 测试基础信息评估处理器
        testCases.add(testProcessorMatch("basic_info", "current_analysis", "基础信息-现状分析"));
        
        // 测试默认处理器
        testCases.add(testProcessorMatch("unknown_type", "unknown_step", "未知类型-默认处理器"));
        
        testResults.put("testCases", testCases);
        testResults.put("totalTests", testCases.size());
        testResults.put("successCount", testCases.stream().mapToInt(tc -> (Boolean) tc.get("success") ? 1 : 0).sum());
        
        return testResults;
    }

    /**
     * 测试单个处理器匹配
     */
    private Map<String, Object> testProcessorMatch(String evaluateType, String stepCode, String description) {
        Map<String, Object> testCase = new HashMap<>();
        testCase.put("description", description);
        testCase.put("evaluateType", evaluateType);
        testCase.put("stepCode", stepCode);
        
        try {
            IStepDataProcessor processor = processorFactory.getProcessor(evaluateType, stepCode);
            if (processor != null) {
                testCase.put("success", true);
                testCase.put("processorClass", processor.getClass().getSimpleName());
                testCase.put("message", "找到处理器");
                log.info("✅ {} - 找到处理器: {}", description, processor.getClass().getSimpleName());
            } else {
                testCase.put("success", false);
                testCase.put("message", "未找到处理器");
                log.warn("⚠️ {} - 未找到处理器", description);
            }
        } catch (Exception e) {
            testCase.put("success", false);
            testCase.put("message", "处理器匹配异常: " + e.getMessage());
            log.error("❌ {} - 处理器匹配异常: {}", description, e.getMessage());
        }
        
        return testCase;
    }

    /**
     * 手动注册处理器
     */
    @ApiOperation("手动注册处理器")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:register')")
    @Log(title = "处理器管理", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public AjaxResult registerProcessor(@Valid @RequestBody ProcessorManagementRequest request) {
        try {
            log.info("开始手动注册处理器: {}", request.getProcessorClassName());

            // 验证请求参数
            if (!"REGISTER".equals(request.getOperationType())) {
                return AjaxResult.error("操作类型必须为REGISTER");
            }

            if (request.getProcessorClassName() == null || request.getProcessorClassName().trim().isEmpty()) {
                return AjaxResult.error("处理器类名不能为空");
            }

            // 执行手动注册逻辑
            Map<String, Object> result = performManualRegistration(request);

            log.info("手动注册处理器成功: {}", request.getProcessorClassName());
            return AjaxResult.success("手动注册处理器成功", result);

        } catch (Exception e) {
            log.error("手动注册处理器失败: {}", request.getProcessorClassName(), e);
            return AjaxResult.error("手动注册处理器失败: " + e.getMessage());
        }
    }

    /**
     * 批量注册处理器
     */
    @ApiOperation("批量注册处理器")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:batchRegister')")
    @Log(title = "处理器管理", businessType = BusinessType.INSERT)
    @PostMapping("/batch-register")
    public AjaxResult batchRegisterProcessors(@Valid @RequestBody ProcessorManagementRequest request) {
        try {
            log.info("开始批量注册处理器，数量: {}",
                request.getProcessors() != null ? request.getProcessors().size() : 0);

            // 验证请求参数
            if (!"BATCH_REGISTER".equals(request.getOperationType())) {
                return AjaxResult.error("操作类型必须为BATCH_REGISTER");
            }

            if (request.getProcessors() == null || request.getProcessors().isEmpty()) {
                return AjaxResult.error("批量处理器信息不能为空");
            }

            // 执行批量注册逻辑
            Map<String, Object> result = performBatchRegistration(request);

            log.info("批量注册处理器完成");
            return AjaxResult.success("批量注册处理器完成", result);

        } catch (Exception e) {
            log.error("批量注册处理器失败", e);
            return AjaxResult.error("批量注册处理器失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有已注册的处理器列表
     */
    @ApiOperation("获取所有已注册的处理器列表")
    @PreAuthorize("@ss.hasPermi('evaluate:processor:list')")
    @GetMapping("/list")
    public AjaxResult getAllProcessors() {
        try {
            List<IStepDataProcessor> allProcessors = processorRegistry.getAllProcessors();

            List<Map<String, Object>> processorList = new ArrayList<>();
            for (IStepDataProcessor processor : allProcessors) {
                Map<String, Object> processorInfo = new HashMap<>();
                processorInfo.put("className", processor.getClass().getName());
                processorInfo.put("simpleName", processor.getClass().getSimpleName());
                processorInfo.put("evaluateType", processor.getEvaluateType());
                processorInfo.put("stepCode", processor.getStepCode());
                processorInfo.put("packageName", processor.getClass().getPackage().getName());
                processorList.add(processorInfo);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("processors", processorList);
            result.put("totalCount", processorList.size());

            return AjaxResult.success("获取处理器列表成功", result);

        } catch (Exception e) {
            log.error("获取处理器列表失败", e);
            return AjaxResult.error("获取处理器列表失败: " + e.getMessage());
        }
    }

    /**
     * 执行手动注册逻辑
     */
    private Map<String, Object> performManualRegistration(ProcessorManagementRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 这里可以扩展实际的手动注册逻辑
            // 由于当前架构是基于Spring自动扫描，手动注册需要更复杂的实现
            // 目前提供模拟实现，记录注册请求

            result.put("action", "MANUAL_REGISTER");
            result.put("processorClassName", request.getProcessorClassName());
            result.put("evaluateType", request.getEvaluateType());
            result.put("stepCode", request.getStepCode());
            result.put("description", request.getDescription());
            result.put("forceRegister", request.getForceRegister());
            result.put("operator", request.getOperator());
            result.put("timestamp", System.currentTimeMillis());
            result.put("status", "SIMULATED");
            result.put("message", "手动注册功能已记录，实际注册需要重启应用或使用热重载功能");

            // 触发注册表刷新
            processorRegistry.refreshRegistry();

        } catch (Exception e) {
            result.put("status", "FAILED");
            result.put("error", e.getMessage());
            throw e;
        }

        return result;
    }

    /**
     * 执行批量注册逻辑
     */
    private Map<String, Object> performBatchRegistration(ProcessorManagementRequest request) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> registrationResults = new ArrayList<>();

        int successCount = 0;
        int failureCount = 0;

        for (ProcessorManagementRequest.ProcessorInfo processorInfo : request.getProcessors()) {
            Map<String, Object> singleResult = new HashMap<>();

            try {
                // 模拟单个处理器注册
                singleResult.put("className", processorInfo.getClassName());
                singleResult.put("evaluateType", processorInfo.getEvaluateType());
                singleResult.put("stepCode", processorInfo.getStepCode());
                singleResult.put("description", processorInfo.getDescription());
                singleResult.put("priority", processorInfo.getPriority());
                singleResult.put("enabled", processorInfo.getEnabled());
                singleResult.put("status", "SIMULATED");
                singleResult.put("message", "批量注册已记录");

                successCount++;

            } catch (Exception e) {
                singleResult.put("status", "FAILED");
                singleResult.put("error", e.getMessage());
                failureCount++;
            }

            registrationResults.add(singleResult);
        }

        result.put("action", "BATCH_REGISTER");
        result.put("totalCount", request.getProcessors().size());
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("registrationResults", registrationResults);
        result.put("operator", request.getOperator());
        result.put("timestamp", System.currentTimeMillis());

        // 触发注册表刷新
        processorRegistry.refreshRegistry();

        return result;
    }
}
