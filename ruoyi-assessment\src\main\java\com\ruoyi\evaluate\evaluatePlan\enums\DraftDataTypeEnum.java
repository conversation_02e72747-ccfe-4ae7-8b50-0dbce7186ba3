package com.ruoyi.evaluate.evaluatePlan.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum DraftDataTypeEnum {
    /**
     * 表单
     */
    FORM("form", "表单"),

    /**
     * 列表
     */
    LIST("list", "列表");

    private final String code;
    private final String name;

    DraftDataTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public static DraftDataTypeEnum fromValue(String value) {
        for (DraftDataTypeEnum type : DraftDataTypeEnum.values()) {
            if (Objects.equals(type.code, value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown OperationType value: " + value);
    }
} 