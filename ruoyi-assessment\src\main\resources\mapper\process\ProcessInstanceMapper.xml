<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.process.mapper.ProcessInstanceMapper">
    
    <resultMap type="ProcessInstance" id="ProcessInstanceResult">
        <result property="id"    column="id"    />
        <result property="processId"    column="process_id"    />
        <result property="businessId"    column="business_id"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProcessInstanceVo">
        select id, process_id, business_id, type, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_process_instance
    </sql>

    <select id="selectProcessInstanceList" parameterType="ProcessInstance" resultMap="ProcessInstanceResult">
        <include refid="selectProcessInstanceVo"/>
        <where>  
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectProcessInstanceById" parameterType="Long" resultMap="ProcessInstanceResult">
        <include refid="selectProcessInstanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertProcessInstance" parameterType="ProcessInstance" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_process_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processId != null">process_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processId != null">#{processId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateProcessInstance" parameterType="ProcessInstance">
        update dsa_process_instance
        <trim prefix="SET" suffixOverrides=",">
            <if test="processId != null">process_id = #{processId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessInstanceById" parameterType="Long">
        delete from dsa_process_instance where id = #{id}
    </delete>

    <delete id="deleteProcessInstanceByIds" parameterType="String">
        delete from dsa_process_instance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>