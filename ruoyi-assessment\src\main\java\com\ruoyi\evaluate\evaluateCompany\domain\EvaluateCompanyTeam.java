package com.ruoyi.evaluate.evaluateCompany.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 评估团队对象 dsa_evaluate_company_team
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_company_team")
public class EvaluateCompanyTeam extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @NotNull(message = "团队id不能为空", groups = {EditGroup.class})
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 评估单位id */
    @NotNull(message = "评估单位id不能为空", groups = {AddGroup.class, ListGroup.class})
    @Excel(name = "评估单位id")
    @TableField(value = "org_id")
    private Long orgId;

    /** 团队名称 */
    @Excel(name = "团队名称")
    @TableField(value = "team_name")
    private String teamName;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}