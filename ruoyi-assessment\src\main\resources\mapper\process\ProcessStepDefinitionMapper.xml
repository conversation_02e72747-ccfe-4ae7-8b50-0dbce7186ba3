<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.process.mapper.ProcessStepDefinitionMapper">
    
    <resultMap type="ProcessStepDefinition" id="ProcessStepDefinitionResult">
        <result property="id"    column="id"    />
        <result property="processId"    column="process_id"    />
        <result property="stepOrder"    column="step_order"    />
        <result property="stepName"    column="step_name"    />
        <result property="stepCode"    column="step_code"    />
        <result property="handlerType"    column="handler_type"    />
        <result property="description"    column="description"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProcessStepDefinitionVo">
        select id, process_id, step_order, step_name, step_code, handler_type, description, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_process_step_definition
    </sql>

    <select id="selectProcessStepDefinitionList" parameterType="ProcessStepDefinition" resultMap="ProcessStepDefinitionResult">
        <include refid="selectProcessStepDefinitionVo"/>
        <where>  
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="stepOrder != null "> and step_order = #{stepOrder}</if>
            <if test="stepName != null  and stepName != ''"> and step_name like concat('%', #{stepName}, '%')</if>
            <if test="stepCode != null  and stepCode != ''"> and step_code = #{stepCode}</if>
            <if test="handlerType != null  and handlerType != ''"> and handler_type = #{handlerType}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectProcessStepDefinitionById" parameterType="Long" resultMap="ProcessStepDefinitionResult">
        <include refid="selectProcessStepDefinitionVo"/>
        where id = #{id}
    </select>

    <insert id="insertProcessStepDefinition" parameterType="ProcessStepDefinition" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_process_step_definition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processId != null">process_id,</if>
            <if test="stepOrder != null">step_order,</if>
            <if test="stepName != null and stepName != ''">step_name,</if>
            <if test="stepCode != null and stepCode != ''">step_code,</if>
            <if test="handlerType != null">handler_type,</if>
            <if test="description != null">description,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processId != null">#{processId},</if>
            <if test="stepOrder != null">#{stepOrder},</if>
            <if test="stepName != null and stepName != ''">#{stepName},</if>
            <if test="stepCode != null and stepCode != ''">#{stepCode},</if>
            <if test="handlerType != null">#{handlerType},</if>
            <if test="description != null">#{description},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateProcessStepDefinition" parameterType="ProcessStepDefinition">
        update dsa_process_step_definition
        <trim prefix="SET" suffixOverrides=",">
            <if test="processId != null">process_id = #{processId},</if>
            <if test="stepOrder != null">step_order = #{stepOrder},</if>
            <if test="stepName != null and stepName != ''">step_name = #{stepName},</if>
            <if test="stepCode != null and stepCode != ''">step_code = #{stepCode},</if>
            <if test="handlerType != null">handler_type = #{handlerType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessStepDefinitionById" parameterType="Long">
        delete from dsa_process_step_definition where id = #{id}
    </delete>

    <delete id="deleteProcessStepDefinitionByIds" parameterType="String">
        delete from dsa_process_step_definition where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>