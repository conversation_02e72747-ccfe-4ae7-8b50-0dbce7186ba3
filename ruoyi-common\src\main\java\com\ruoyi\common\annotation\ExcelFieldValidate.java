package com.ruoyi.common.annotation;

import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelFieldValidate {
    String name(); // 列名
    boolean required() default false; // 是否必填
    int maxLength() default -1; // 最大长度
    String regex() default ""; // 正则表达式
    String regexMsg() default ""; // 正则不通过时的提示
    String dictType() default ""; // 新增：字典类型
}