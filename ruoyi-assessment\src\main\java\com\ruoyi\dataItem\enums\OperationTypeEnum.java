package com.ruoyi.dataItem.enums;

/**
 * <AUTHOR>
 */

public enum OperationTypeEnum {
    /**
     * 新增
     */
    INSERT(1, "新增"),

    /**
     * 修改
     */
    UPDATE(2, "修改"),

    /**
     * 删除
     */
    DELETE(3, "删除");

    private final Integer code;
    private final String name;

    OperationTypeEnum(Integer value, String name) {
        this.code = value;
        this.name = name;
    }

    public Integer getValue() {
        return code;
    }

    public static OperationTypeEnum fromValue(int value) {
        for (OperationTypeEnum type : OperationTypeEnum.values()) {
            if (type.code == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown OperationType value: " + value);
    }
} 