# 评估计划控制器 API 使用示例

## 概述

本文档展示了如何使用增强后的评估计划控制器来实现"在某种类型的某个评估计划任务下执行不同的任务内容"的需求。

## 🚀 新增功能

### 1. 执行指定任务内容 (`/evaluatePlan/executeTask`)

允许在特定评估类型下执行不同的任务内容，支持任务参数化配置。

**请求示例：**
```json
POST /evaluatePlan/executeTask
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "data_security_plan",
    "taskType": "data_collection",
    "taskTitle": "数据收集任务",
    "taskDescription": "收集客户数据资产信息",
    "targetCompanyId": 1,
    "evaluateCompanyId": 1,
    "taskParams": {
        "dataScope": "all",
        "includeBackup": true,
        "timeRange": "2024-01-01 to 2024-12-31"
    },
    "taskConfig": {
        "timeout": 3600,
        "retryCount": 3,
        "priority": "high"
    },
    "executeMode": "async",
    "operator": "admin"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "任务内容执行成功",
    "data": {
        "planId": 1001,
        "success": true,
        "message": "数据安全风险评估执行成功",
        "progress": 100,
        "status": 2,
        "resultData": {
            "totalRisks": 18,
            "highRisks": 4,
            "mediumRisks": 8,
            "lowRisks": 6,
            "enhancedFeatures": "支持步骤级别执行、任务内容定制、流程回退等"
        },
        "extData": {
            "taskType": "data_collection",
            "taskParams": {
                "dataScope": "all",
                "includeBackup": true,
                "timeRange": "2024-01-01 to 2024-12-31"
            }
        }
    }
}
```

### 2. 执行指定步骤任务 (`/evaluatePlan/executeStep`)

允许执行评估流程中的特定步骤，支持流程控制和步骤依赖管理。

**请求示例：**
```json
POST /evaluatePlan/executeStep
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "data_security_plan",
    "stepCode": "risk_identify",
    "processInstanceId": 2001,
    "stepName": "风险识别分析",
    "stepDescription": "识别数据安全相关风险点",
    "targetCompanyId": 1,
    "evaluateCompanyId": 1,
    "stepParams": {
        "analysisDepth": "detailed",
        "includeThirdParty": true,
        "riskCategories": ["access_control", "data_leakage", "system_vulnerability"]
    },
    "executeMode": "manual",
    "operator": "risk_analyst"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "步骤任务执行成功",
    "data": {
        "planId": 1001,
        "success": true,
        "message": "步骤执行成功: risk_identify",
        "progress": 50,
        "status": 1,
        "resultData": {
            "identifiedRisks": 18,
            "riskCategories": ["访问控制", "数据泄露", "系统漏洞"],
            "stepCode": "risk_identify",
            "executeTime": "2025-07-28 14:30:00",
            "processInstanceId": 2001
        },
        "steps": [{
            "stepName": "风险识别分析",
            "stepStatus": 2,
            "description": "步骤执行完成",
            "executeTime": "2025-07-28 14:30:00",
            "result": "{identifiedRisks=18, riskCategories=[访问控制, 数据泄露, 系统漏洞]}"
        }],
        "extData": {
            "stepCode": "risk_identify",
            "stepParams": {
                "analysisDepth": "detailed",
                "includeThirdParty": true,
                "riskCategories": ["access_control", "data_leakage", "system_vulnerability"]
            }
        }
    }
}
```

### 3. 获取任务内容列表 (`/evaluatePlan/taskList`)

获取指定评估类型支持的所有任务内容类型。

**请求示例：**
```
GET /evaluatePlan/taskList?evaluateType=data_security_plan&planId=1001
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取任务列表成功",
    "data": {
        "evaluateType": "data_security_plan",
        "strategyName": "数据安全风险评估方案",
        "planId": 1001,
        "tasks": [
            {
                "taskType": "data_collection",
                "taskName": "数据收集",
                "description": "收集评估所需的基础数据",
                "status": "available"
            },
            {
                "taskType": "risk_analysis",
                "taskName": "风险分析",
                "description": "分析识别的风险点",
                "status": "available"
            },
            {
                "taskType": "report_generation",
                "taskName": "报告生成",
                "description": "生成评估报告",
                "status": "available"
            }
        ],
        "totalCount": 3
    }
}
```

### 4. 获取步骤任务状态 (`/evaluatePlan/stepStatus`)

获取评估流程中各步骤的执行状态和详细信息。

**请求示例：**
```
GET /evaluatePlan/stepStatus?evaluateType=data_security_plan&planId=1001&stepCode=risk_identify
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取步骤状态成功",
    "data": {
        "evaluateType": "data_security_plan",
        "strategyName": "数据安全风险评估方案",
        "planId": 1001,
        "stepCode": "risk_identify",
        "stepInfo": {
            "stepCode": "risk_identify",
            "stepName": "风险识别分析",
            "status": "completed",
            "progress": 100,
            "canSkip": false,
            "estimatedDuration": 2700000,
            "dependencies": ["data_classify"]
        }
    }
}
```

## 🎯 使用场景

### 场景1：数据安全评估中的不同任务内容

```javascript
// 1. 首先获取可用的任务类型
const taskListResponse = await fetch('/evaluatePlan/taskList?evaluateType=data_security_plan&planId=1001');
const taskList = await taskListResponse.json();

// 2. 执行数据收集任务
const dataCollectionTask = {
    planId: 1001,
    evaluateType: "data_security_plan",
    taskType: "data_collection",
    taskParams: {
        dataScope: "sensitive_data_only",
        includeBackup: true
    }
};
const collectionResponse = await fetch('/evaluatePlan/executeTask', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(dataCollectionTask)
});

// 3. 执行风险分析任务
const riskAnalysisTask = {
    planId: 1001,
    evaluateType: "data_security_plan",
    taskType: "risk_analysis",
    taskParams: {
        analysisLevel: "comprehensive",
        includeCompliance: true
    }
};
const analysisResponse = await fetch('/evaluatePlan/executeTask', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(riskAnalysisTask)
});
```

### 场景2：流程步骤级别的精细控制

```javascript
// 1. 获取步骤状态
const stepStatusResponse = await fetch('/evaluatePlan/stepStatus?evaluateType=data_security_plan&planId=1001');
const stepStatus = await stepStatusResponse.json();

// 2. 执行特定步骤
const stepRequest = {
    planId: 1001,
    evaluateType: "data_security_plan",
    stepCode: "risk_assessment",
    processInstanceId: 2001,
    stepParams: {
        assessmentMethod: "quantitative",
        riskThreshold: "medium"
    }
};
const stepResponse = await fetch('/evaluatePlan/executeStep', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(stepRequest)
});
```

## 📋 支持的评估类型

| 评估类型编码 | 策略名称 | 支持功能 |
|-------------|----------|----------|
| `data_security_plan` | 数据安全风险评估方案 | 基础评估流程 + 任务内容定制 + 步骤级控制 + 流程回退 |


## 🔧 扩展指南

要添加新的任务内容类型，需要：

1. **扩展策略实现**：在对应的策略类中添加新的任务处理逻辑
2. **更新任务列表**：在`getAvailableTasks`方法中添加新的任务类型
3. **实现任务执行**：在`executeEvaluate`方法中根据任务参数执行不同逻辑

要添加新的步骤，需要：

1. **实现ProcessAwareEvaluateStrategy接口**
2. **定义步骤依赖关系**：在`getStepDependencies`方法中配置
3. **实现步骤执行逻辑**：在`executeStep`方法中处理具体步骤

## ⚠️ 注意事项

1. **权限控制**：确保用户具有相应的权限才能执行任务
2. **参数验证**：所有请求参数都会进行严格验证
3. **异常处理**：系统会捕获并返回详细的错误信息
4. **并发控制**：同一计划的步骤执行需要考虑并发安全
5. **数据一致性**：步骤回退时会清理相关的缓存数据
