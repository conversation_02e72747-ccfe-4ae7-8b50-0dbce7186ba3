package com.ruoyi.evaluate.company.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 被评估单位安全管理文档信息Excel导入对象 - Sheet4
 * 数据从第4行开始读取（跳过表头和示例数据）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ExcelTargetManageDocuments {

    /** 序号 - A列 */
    @ExcelProperty(index = 0)
    private String serialNumber;

    /** 文档名称 - B列 */
    @ExcelProperty(index = 1)
    private String documentName;

    /** 主要内容 - C列 */
    @ExcelProperty(index = 2)
    private String content;

    /** 适用范围 - D列 */
    @ExcelProperty(index = 3)
    private String scope;
}
