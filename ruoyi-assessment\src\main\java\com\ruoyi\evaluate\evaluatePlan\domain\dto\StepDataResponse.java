package com.ruoyi.evaluate.evaluatePlan.domain.dto;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.process.domain.ProcessStepInstance;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.Map;
import java.util.HashMap;

/**
 * 步骤数据响应实体
 * <p>
 * 用于标准化步骤数据的返回结构，提供类型安全的基础字段
 * 同时保持扩展数据的灵活性
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepDataResponse {

    /**
     * 步骤实例ID
     */
    private Long stepInstanceId;

    /**
     * 步骤编码
     */
    private String stepCode;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 评估类型
     */
    private String evaluateType;

    /**
     * 评估计划任务
     */
    private EvaluatePlanTask planTask;

    /**
     * 步骤实例
     */
    private ProcessStepInstance stepInstance;

    /**
     * 计划ID
     */
    // private Long planId;

    /**
     * 计划名称
     */
    // private String planName;

    /**
     * 处理时间
     */
    // private Date processTime;

    /**
     * 状态名称
     */
    // private String statusName;

    /**
     * 开始时间
     */
    // private Date startTime;

    /**
     * 结束时间
     */
    // private Date endTime;

    /**
     * 操作人
     */
    // private String operator;

    /**
     * 扩展数据
     * 用于存储各步骤特有的数据，保持灵活性
     */
    @Builder.Default
    private Map<String, Object> stepData = new HashMap<>();

    /**
     * 添加扩展数据
     */
    public void addStepData(String key, Object value) {
        if (this.stepData == null) {
            this.stepData = new HashMap<>();
        }
        this.stepData.put(key, value);
    }

    /**
     * 批量添加扩展数据
     */
    public void addStepData(Map<String, Object> data) {
        if (this.stepData == null) {
            this.stepData = new HashMap<>();
        }
        if (data != null) {
            this.stepData.putAll(data);
        }
    }

    /**
     * 获取扩展数据
     */
    public Object getStepData(String key) {
        return this.stepData != null ? this.stepData.get(key) : null;
    }
}
