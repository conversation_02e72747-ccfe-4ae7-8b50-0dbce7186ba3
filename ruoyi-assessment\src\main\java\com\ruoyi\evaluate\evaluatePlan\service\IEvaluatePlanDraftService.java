package com.ruoyi.evaluate.evaluatePlan.service;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.DraftStepRequest;

import java.util.List;
import java.util.Map;

/**
 * 评估计划暂存服务接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IEvaluatePlanDraftService {

    /**
     * 暂存评估计划步骤数据
     *
     * @param userId 用户ID
     * @param request 暂存请求参数
     * @return 暂存结果
     */
    Map<String, Object> saveDraft(Long userId, SaveStepRequest request);

    /**
     * 获取评估计划步骤暂存数据
     *
     * @param userId 用户ID
     * @param request 步骤请求参数
     * @return 暂存数据结果
     */
    StepDataResponse getDraft(Long userId, DraftStepRequest request);

    /**
     * 删除评估计划步骤暂存数据
     *
     * @param userId 用户ID
     * @param request 步骤请求参数
     * @return 删除结果
     */
    Map<String, Object> deleteDraft(Long userId, DraftStepRequest request);

    /**
     * 检查评估计划步骤暂存数据是否存在
     *
     * @param userId 用户ID
     * @param request 步骤请求参数
     * @return 检查结果
     */
    Map<String, Object> existsDraft(Long userId, DraftStepRequest request);

    /**
     * 从暂存数据创建评估计划任务
     *
     * @param userId 用户ID
     * @param request 步骤请求参数
     * @param deleteDraft 是否删除暂存数据
     * @return 创建结果
     */
    Map<String, Object> createFromDraft(Long userId, DraftStepRequest request, boolean deleteDraft);

    /**
     * 删除上一步的暂存数据
     *
     * @param userId 用户ID
     * @param planId 评估计划ID
     * @param previousStep 上一步骤
     * @return 删除结果
     */
    Map<String, Object> deleteStepDraft(Long userId, Long planId, String previousStep);

    /**
     * 批量获取评估计划步骤暂存数据
     *
     * @param userId 用户ID
     * @param planId 评估计划ID
     * @param steps 步骤列表
     * @return 批量获取结果
     */
    Map<String, Object> batchGetDrafts(Long userId, Long planId, List<String> steps);

    /**
     * 批量删除评估计划步骤暂存数据
     *
     * @param userId 用户ID
     * @param planId 评估计划ID
     * @param steps 步骤列表
     * @return 批量删除结果
     */
    Map<String, Object> batchDeleteDrafts(Long userId, Long planId, List<String> steps);

    /**
     * 获取用户所有暂存数据摘要
     *
     * @param userId 用户ID
     * @return 暂存数据摘要
     */
    Map<String, Map<String, Object>> getUserDraftSummary(Long userId);

    /**
     * 清理用户所有暂存数据
     *
     * @param userId 用户ID
     * @return 清理结果
     */
    Map<String, Object> clearUserDrafts(Long userId);

    /**
     * 批量暂存评估计划步骤数据
     *
     * @param userId 用户ID
     * @param planId 评估计划ID
     * @param drafts 暂存数据Map（步骤->数据）
     * @return 批量暂存结果
     */
    Map<String, Object> batchSaveDrafts(Long userId, Long planId, Map<String, Object> drafts);
}
