package com.ruoyi.evaluate.company.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;

/**
 * 文件系统MultipartFile实现
 * 用于将本地文件转换为MultipartFile接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class FileSystemMultipartFile implements MultipartFile {
    
    private final File file;
    private final String name;
    private final String originalFilename;
    private final String contentType;
    
    public FileSystemMultipartFile(File file) {
        this.file = file;
        this.name = file.getName();
        this.originalFilename = file.getName();
        this.contentType = determineContentType(file.getName());
    }
    
    public FileSystemMultipartFile(File file, String name) {
        this.file = file;
        this.name = name;
        this.originalFilename = file.getName();
        this.contentType = determineContentType(file.getName());
    }
    
    private String determineContentType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }
        
        String lowerName = filename.toLowerCase();
        if (lowerName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerName.endsWith(".zip")) {
            return "application/zip";
        } else {
            return "application/octet-stream";
        }
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    @Override
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public boolean isEmpty() {
        return file.length() == 0;
    }
    
    @Override
    public long getSize() {
        return file.length();
    }
    
    @Override
    public byte[] getBytes() throws IOException {
        return Files.readAllBytes(file.toPath());
    }
    
    @Override
    public InputStream getInputStream() throws IOException {
        return new FileInputStream(file);
    }
    
    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        Files.copy(file.toPath(), dest.toPath());
    }
}
