package com.ruoyi.evaluateModel.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.ruoyi.evaluateModel.domain.excel.ExcelEvaluateItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 评估模型Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/evaluateModel/model")
@Api(value = "评估模型控制器", tags = {"评估模型管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateModelController extends BaseController {
    private final IEvaluateModelService evaluateModelService;

    /**
     * 查询评估模型列表
     */
    @ApiOperation("查询评估模型列表")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluateModel evaluateModel) {
        startPage();
        List<EvaluateModel> list = evaluateModelService.list(new QueryWrapper<EvaluateModel>(evaluateModel));
        return getDataTable(list);
    }

    /**
     * 获取评估模型详细信息
     */
    @ApiOperation("获取评估模型详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateModelService.getById(id));
    }

    /**
     * 新增评估模型
     */
    @ApiOperation("新增评估模型")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:add')")
    @Log(title = "评估模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EvaluateModel evaluateModel) {
        try {
            return toAjax(evaluateModelService.save(evaluateModel));
        } catch (Exception e) {
            return handleException(e, "新增评估模型失败");
        }
    }

    /**
     * 修改评估模型
     */
    @ApiOperation("修改评估模型")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:edit')")
    @Log(title = "评估模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EvaluateModel evaluateModel) {
        return toAjax(evaluateModelService.updateById(evaluateModel));
    }

    /**
     * 删除评估模型
     */
    @ApiOperation("删除评估模型")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:remove')")
    @Log(title = "评估模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateModelService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 导入评估模型数据
     */
    @ApiOperation("导入评估模型数据")
    @PreAuthorize("@ss.hasPermi('evaluateModel:model:import')")
    @Log(title = "评估模型数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData/{modelId}")
    public AjaxResult importData(MultipartFile file, @PathVariable Long modelId) throws Exception {
        Map<String, Object> result = evaluateModelService.importFromExcel(modelId, file);
        return AjaxResult.success(result);
    }

    /**
     * 拷贝评估模型
     */
    @ApiOperation("拷贝评估模型")
    @PostMapping("/copy/{modelId}")
    public AjaxResult copy(@PathVariable Long modelId, @RequestBody EvaluateModel evaluateModel) {
        int flag = evaluateModelService.copy(modelId, evaluateModel);
        return flag == 1 ? AjaxResult.success() : AjaxResult.error();
    }
}