-- ----------------------------
-- 评估计划任务表
-- 用于管理评估计划中的具体任务安排
-- 创建时间: 2025-07-28
-- ----------------------------

DROP TABLE IF EXISTS `dsa_evaluate_plan_task`;

CREATE TABLE `dsa_evaluate_plan_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属单位ID',
  `evaluate_org_id` bigint(20) DEFAULT NULL COMMENT '评估单位ID',
  `evaluate_type` int(11) DEFAULT NULL COMMENT '评估类型ID',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '评估计划名称',
  `report_no` varchar(100) DEFAULT NULL COMMENT '报告编号',
  `name` varchar(255) NOT NULL COMMENT '任务名称',
  `plan_start_date` date DEFAULT NULL COMMENT '计划开始日期',
  `plan_end_date` date DEFAULT NULL COMMENT '计划结束日期',
  `real_start_date` date DEFAULT NULL COMMENT '实际开始日期',
  `real_end_date` date DEFAULT NULL COMMENT '实际结束日期',
  `deadline` date DEFAULT NULL COMMENT '截止日期',
  `mode` int(1) DEFAULT '1' COMMENT '模式，1-手动模式 2-批量模式',
  `task_status` varchar(20) DEFAULT 'NOT_STARTED' COMMENT '任务状态（NOT_STARTED-未开始，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消）',
  `master_user` varchar(100) DEFAULT NULL COMMENT '负责人',
  `master_dept` varchar(100) DEFAULT NULL COMMENT '负责部门',
  `task_description` text COMMENT '任务描述',
  `completion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '完成率（0.00-100.00）',
  `plan_hours` decimal(8,2) DEFAULT NULL COMMENT '预估工时（小时）',
  `real_hours` decimal(8,2) DEFAULT NULL COMMENT '实际工时（小时）',
  `origin_task_id` bigint(20) DEFAULT NULL COMMENT '父任务ID（用于任务层级关系）',
  `version_id` bigint(20) DEFAULT NULL COMMENT '版本ID',
  `status` int(1) DEFAULT '1' COMMENT '状态，1-正常 0-禁用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_plan_dates` (`plan_start_date`, `plan_end_date`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_master_user` (`master_user`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_evaluate_org_id` (`evaluate_org_id`),
  KEY `idx_evaluate_type` (`evaluate_type`),
  KEY `idx_origin_task` (`origin_task_id`),
  KEY `idx_deadline` (`deadline`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评估计划任务表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

INSERT INTO `dsa_evaluate_plan_task` VALUES
(1, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '数据资产识别与梳理', '2025-08-01', '2025-08-05', NULL, NULL, '2025-08-31', 1, 'NOT_STARTED', '张三', '信息安全部', '识别和梳理组织内所有数据资产，建立完整的数据资产清单', 0.00, 32.00, NULL, NULL, 1, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '重要里程碑任务', 0),

(2, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '数据分类分级标记', '2025-08-06', '2025-08-10', NULL, NULL, '2025-08-31', 1, 'NOT_STARTED', '李四', '信息安全部', '按照敏感程度对数据资产进行分类分级标记', 0.00, 24.00, NULL, 1, 2, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '依赖数据资产识别完成', 0),

(3, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '风险识别与分析', '2025-08-11', '2025-08-15', NULL, NULL, '2025-08-31', 1, 'NOT_STARTED', '王五', '风险管理部', '识别数据安全相关的各类风险点和威胁', 0.00, 40.00, NULL, 2, 3, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '关键里程碑', 0),

(4, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '风险等级评定', '2025-08-16', '2025-08-18', NULL, NULL, '2025-08-31', 2, 'NOT_STARTED', '赵六', '风险管理部', '对识别的风险进行等级评定和影响分析', 0.00, 16.00, NULL, 3, 4, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '基于风险识别结果', 0),

(5, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '控制措施制定', '2025-08-19', '2025-08-23', NULL, NULL, '2025-08-31', 1, 'NOT_STARTED', '孙七', '信息安全部', '制定相应的安全控制措施和整改建议', 0.00, 32.00, NULL, 4, 5, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '制定具体的改进措施', 0),

(6, 1, 101, 'data_security_plan', '数据安全风险评估计划', 'DSA-2025-001', '评估报告编制', '2025-08-24', '2025-08-28', NULL, NULL, '2025-08-31', 1, 'NOT_STARTED', '周八', '综合管理部', '编制完整的数据安全风险评估报告', 0.00, 24.00, NULL, 5, 6, 1001, 1, 1, 'admin', '2025-07-28 10:00:00', '', '2025-07-28 10:00:00', '项目交付里程碑', 0);

-- ----------------------------
-- 表结构说明
-- ----------------------------
/*
字段说明：
核心字段（对应界面需求）：
1. name: 任务名称 - 对应界面中的任务名称字段
2. plan_start_date: 计划开始日期 - 对应界面中的计划开始日期
3. plan_end_date: 计划结束日期 - 对应界面中的计划结束日期
4. real_start_date: 实际开始日期 - 对应界面中的实际开始日期
5. real_end_date: 实际结束日期 - 对应界面中的实际结束日期

整合字段（来自dsa_assess_plan表）：
- org_id: 所属单位ID
- assess_company_id: 评估单位ID
- plan_type_id: 评估类型ID
- plan_name: 评估计划名称
- report_no: 报告编号
- mode: 模式（1-手动模式 2-批量模式）
- deadline: 截止日期
- status: 状态（1-正常 0-禁用）
- remark: 备注
- del_flag: 逻辑删除标志（0 正常，1 删除）

扩展字段：
- task_status: 任务状态，支持任务生命周期管理
- master_user: 负责人，明确任务责任
- master_dept: 负责部门，便于部门协调
- completion_rate: 完成率，支持进度跟踪
- plan_hours/real_hours: 工时管理
- origin_task_id: 支持任务层级关系
- task_order: 任务排序
- task_description: 任务描述

索引设计：
- 主要查询字段都建立了索引，提高查询性能
- 支持按时间、状态、负责人、评估单位等多维度查询
- 新增评估单位ID和评估类型ID的索引

数据示例：
- 提供了6个示例任务，展示了完整的评估流程
- 包含了评估计划的基本信息（计划名称、报告编号等）
- 体现了手动和批量两种模式
- 统一的截止日期和状态管理
*/
