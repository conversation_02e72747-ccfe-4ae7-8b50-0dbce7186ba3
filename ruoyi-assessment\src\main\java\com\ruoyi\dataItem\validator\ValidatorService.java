package com.ruoyi.dataItem.validator;

import com.ruoyi.common.annotation.ExcelFieldValidate;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.dataItem.domain.DataItem;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ValidatorService {

    @Autowired
    private ISysDictTypeService dictTypeService;

    public void validate(DataItem plan, List<ExcelImportHelper.RowError.ColumnError> errors) {
        Field[] fields = DataItem.class.getDeclaredFields();
        for (Field field : fields) {
            ExcelFieldValidate anno = field.getAnnotation(ExcelFieldValidate.class);
            if (anno != null) {
                field.setAccessible(true);
                try {
                    Object value = field.get(plan);
                    String strValue = value == null ? "" : value.toString();
                    // 必填校验
                    if (anno.required() && strValue.trim().isEmpty()) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason("不能为空或格式不正确");
                        errors.add(error);
                    }
                    // 长度校验
                    if (anno.maxLength() > 0 && strValue.length() > anno.maxLength()) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason("长度不能超过" + anno.maxLength());
                        errors.add(error);
                    }
                    // 正则校验
                    if (!anno.regex().isEmpty() && !strValue.matches(anno.regex())) {
                        ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                        error.setColumn(anno.name());
                        error.setReason(anno.regexMsg().isEmpty() ? ("格式不正确") : anno.regexMsg());
                        errors.add(error);
                    }
                    // 字典校验
                    if (!anno.dictType().isEmpty() && !strValue.isEmpty()) {
                        List<SysDictData> dictList = dictTypeService.selectDictDataByType(anno.dictType());
                        boolean valid = dictList != null && dictList.stream().anyMatch(d -> d.getDictLabel().equals(strValue));
                        if (!valid) {
                            ExcelImportHelper.RowError.ColumnError error = new ExcelImportHelper.RowError.ColumnError();
                            error.setColumn(anno.name());
                            String dictDisplay = dictList == null ? "" : dictList.stream().map(SysDictData::getDictLabel).collect(Collectors.joining("、"));
                            error.setReason("值不正确，必须为：" + dictDisplay);
                            errors.add(error);
                        }
                    }
                } catch (IllegalAccessException e) {
                    // ignore
                }
            }
        }

        // 其他特殊业务校验可单独补充
        // 根据【年份（即版本号）+【数据项名称】+【数据级别】来判断唯一性
        // 需要在外部批量校验时调用 validateUniqueKeys
    }

    /**
     * 校验【年份（即版本号）+数据项名称+数据级别】唯一性
     *
     * @param validList 本次导入的所有DataItem
     * @param dbList    数据库中已存在的DataItem
     * @param errorRows 错误收集对象
     * @return 数据库已存在的DataItem列表（需后续更新）
     */
    public List<DataItem> validateUniqueKeys(List<DataItem> validList, List<DataItem> dbList, List<Map<String, Object>> errorRows) {
        return dbList;
    }
}
