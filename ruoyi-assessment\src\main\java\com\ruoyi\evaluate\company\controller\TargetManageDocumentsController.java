package com.ruoyi.evaluate.company.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.ListGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.company.domain.TargetManageDocuments;
import com.ruoyi.evaluate.company.service.ITargetManageDocumentsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 被评估单位文档信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/targetDocuments/documents")
@Api(value = "被评估单位文档信息控制器", tags = {"被评估单位文档信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TargetManageDocumentsController extends BaseController {
    private final ITargetManageDocumentsService targetManageDocumentsService;

    /**
     * 查询被评估单位文档信息列表
     */
    @ApiOperation("查询被评估单位文档信息列表")
    @PreAuthorize("@ss.hasPermi('targetDocuments:documents:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) TargetManageDocuments targetManageDocuments) {
        startPage();
        List<TargetManageDocuments> list = targetManageDocumentsService.list(new QueryWrapper<TargetManageDocuments>(targetManageDocuments));
        return getDataTable(list);
    }

    /**
     * 导出被评估单位文档信息列表
     */
    // @ApiOperation("导出被评估单位文档信息列表")
    // @PreAuthorize("@ss.hasPermi('targetDocuments:documents:export')")
    // @Log(title = "被评估单位文档信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response,TargetManageDocuments targetManageDocuments) {
    //     List<TargetManageDocuments> list = targetManageDocumentsService.list(new QueryWrapper<TargetManageDocuments>(targetManageDocuments));
    //     ExcelUtil<TargetManageDocuments> util = new ExcelUtil<TargetManageDocuments>(TargetManageDocuments.class);
    //     util.exportExcel(response,list, "被评估单位文档信息数据");
    // }

    /**
     * 获取被评估单位文档信息详细信息
     */
    @ApiOperation("获取被评估单位文档信息详细信息")
    @PreAuthorize("@ss.hasPermi('targetDocuments:documents:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(targetManageDocumentsService.getById(id));
    }

    /**
     * 新增被评估单位文档信息
     */
    @ApiOperation("新增被评估单位文档信息")
    @PreAuthorize("@ss.hasPermi('targetDocuments:documents:add')")
    @Log(title = "被评估单位文档信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody TargetManageDocuments targetManageDocuments) {
        return toAjax(targetManageDocumentsService.save(targetManageDocuments));
    }

    /**
     * 修改被评估单位文档信息
     */
    @ApiOperation("修改被评估单位文档信息")
    @PreAuthorize("@ss.hasPermi('targetDocuments:documents:edit')")
    @Log(title = "被评估单位文档信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TargetManageDocuments targetManageDocuments) {
        return toAjax(targetManageDocumentsService.updateById(targetManageDocuments));
    }

    /**
     * 删除被评估单位文档信息
     */
    @ApiOperation("删除被评估单位文档信息")
    @PreAuthorize("@ss.hasPermi('targetDocuments:documents:remove')")
    @Log(title = "被评估单位文档信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(targetManageDocumentsService.removeByIds(Arrays.asList(ids)));
    }
}