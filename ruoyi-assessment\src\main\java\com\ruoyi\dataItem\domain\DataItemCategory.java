package com.ruoyi.dataItem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据项分类对象 dsa_data_item_category
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_item_category")
public class DataItemCategory extends TreeEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;


    /** 分类名称 */
    @Excel(name = "分类名称")
    @TableField(value = "category_name")
    private String categoryName;

    /** 分类级别 */
    @Excel(name = "分类级别")
    @TableField(value = "level")
    private Integer level;

    /** 排序值 */
    @Excel(name = "排序值")
    @TableField(value = "sort")
    private Long sort;


    /** 状态 */
    @Excel(name = "状态")
    @TableField(value = "status")
    private Integer status;

}