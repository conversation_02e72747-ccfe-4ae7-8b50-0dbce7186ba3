package com.ruoyi.evaluateModel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.ruoyi.evaluateModel.domain.excel.ExcelEvaluateItemVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 评估清单
 *
 * <AUTHOR>
 */
@Slf4j
public class EvaluateItemListener extends AnalysisEventListener<ExcelEvaluateItemVo> {

    private final List<ExcelEvaluateItemVo> cacheList = new ArrayList<>();
    private final List<String> errorMessages = new ArrayList<>();

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param analysisContext
     */
    @Override
    public void invoke(ExcelEvaluateItemVo data, AnalysisContext analysisContext) {
        log.info("table row: {}", data);
        cacheList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // log.info("all analysed yet");
        log.info("Excel 数据解析完成，共 {} 条", cacheList.size());
        saveData();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // log.info("table header {}", JSONUtil.toJsonStr(headMap));
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException ex = (ExcelDataConvertException) exception;
            String msg = String.format("第%d行，第%d列，值“%s”转换失败",
                    ex.getRowIndex() + 1,
                    ex.getColumnIndex() + 1,
                    ex.getCellData() != null ? ex.getCellData().getStringValue() : "null");
            errorMessages.add(msg);
            log.warn(msg);
        } else {
            errorMessages.add("未知错误：" + exception.getMessage());
            log.error("读取异常", exception);
        }
    }

    public List<ExcelEvaluateItemVo> getCacheList() {
        return cacheList;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

    private void saveData() {
        log.info("存储数据库成功！");
    }
}
