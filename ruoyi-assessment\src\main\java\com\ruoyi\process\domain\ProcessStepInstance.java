package com.ruoyi.process.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 流程步骤实例对象 dsa_process_step_instance
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_process_step_instance")
public class ProcessStepInstance extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 流程实例ID */
    @Excel(name = "流程实例ID")
    @TableField(value = "process_instance_id")
    private Long processInstanceId;

    /** 步骤定义ID */
    @Excel(name = "步骤定义ID")
    @TableField(value = "step_definition_id")
    private Long stepDefinitionId;

    /** 步骤名称 */
    @Excel(name = "步骤名称")
    @TableField(value = "step_name")
    private String stepName;

    /** 状态：0-待执行 1-处理中 2-完成 */
    @Excel(name = "状态：0-待执行 1-处理中 2-完成")
    @TableField(value = "status")
    private Integer status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "start_time")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "end_time")
    private Date endTime;

    /** 耗时（毫秒） */
    @Excel(name = "耗时")
    @TableField(value = "duration_ms")
    private Long durationMs;

    /** 操作人 */
    @Excel(name = "操作人")
    @TableField(value = "operator")
    private String operator;

    /** 负责人 */
    @Excel(name = "负责人")
    @TableField(value = "next_step_assignee")
    private String assignee;

    /** 负责部门 */
    @Excel(name = "负责部门")
    @TableField(value = "next_step_dept")
    private String stepDept;

    /** 下一步截止日期（相对天数） */
    @Excel(name = "下一步截止日期（相对天数）")
    @TableField(value = "next_step_deadline_days")
    private Integer deadlineDays;

    /** 是否设置任务截止日期（0-否，1-是） */
    @Excel(name = "是否设置任务截止日期")
    @TableField(value = "set_task_deadline")
    private Integer setTaskDeadline;







}