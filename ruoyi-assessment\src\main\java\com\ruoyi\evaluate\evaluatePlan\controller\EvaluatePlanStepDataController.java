package com.ruoyi.evaluate.evaluatePlan.controller;

import java.util.Map;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanStepDataService;

/**
 * 评估计划步骤数据Controller
 * <p>
 * 用于获取评估计划中不同步骤的数据，每个步骤的处理逻辑和数据结构都不一样
 * 支持步骤处理逻辑的拆分和单独处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/evaluatePlan/stepData")
@Api(value = "评估计划步骤数据控制器", tags = {"评估计划步骤数据管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanStepDataController extends BaseController {

    private final IEvaluatePlanStepDataService evaluatePlanStepDataService;



    /**
     * 获取评估计划所有步骤的数据概览
     */
    /*@ApiOperation("获取评估计划所有步骤的数据概览")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:stepData:list')")
    @GetMapping("/overview/{planId}")
    public AjaxResult getStepDataOverview(
            @ApiParam(value = "评估计划ID", required = true) @PathVariable("planId") Long planId) {
        try {
            Map<String, Object> overview = evaluatePlanStepDataService.getStepDataOverview(planId);
            return AjaxResult.success("获取步骤数据概览成功", overview);
        } catch (Exception e) {
            return handleException(e, "获取步骤数据概览失败");
        }
    }*/

    /**
     * 获取指定步骤的数据结构定义
     */
    /*@ApiOperation("获取指定步骤的数据结构定义")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:stepData:schema')")
    @GetMapping("/schema/{stepCode}")
    public AjaxResult getStepDataSchema(
            @ApiParam(value = "步骤编码", required = true) @PathVariable("stepCode") String stepCode,
            @ApiParam(value = "评估类型") @RequestParam(value = "evaluateType", required = false) String evaluateType) {
        try {
            Map<String, Object> schema = evaluatePlanStepDataService.getStepDataSchema(stepCode, evaluateType);
            return AjaxResult.success("获取步骤数据结构成功", schema);
        } catch (Exception e) {
            return handleException(e, "获取步骤数据结构失败");
        }
    }*/

    /**
     * 获取步骤数据处理状态
     */
    /*@ApiOperation("获取步骤数据处理状态")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:stepData:status')")
    @GetMapping("/status/{planId}/{stepCode}")
    public AjaxResult getStepDataStatus(
            @ApiParam(value = "评估计划ID", required = true) @PathVariable("planId") Long planId,
            @ApiParam(value = "步骤编码", required = true) @PathVariable("stepCode") String stepCode) {
        try {
            Map<String, Object> status = evaluatePlanStepDataService.getStepDataStatus(planId, stepCode);
            return AjaxResult.success("获取步骤数据状态成功", status);
        } catch (Exception e) {
            return handleException(e, "获取步骤数据状态失败");
        }
    }*/

    /**
     * 获取步骤数据统计信息
     */
    /*@ApiOperation("获取步骤数据统计信息")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:stepData:statistics')")
    @GetMapping("/statistics/{planId}/{stepCode}")
    public AjaxResult getStepDataStatistics(
            @ApiParam(value = "评估计划ID", required = true) @PathVariable("planId") Long planId,
            @ApiParam(value = "步骤编码", required = true) @PathVariable("stepCode") String stepCode) {
        try {
            Map<String, Object> statistics = evaluatePlanStepDataService.getStepDataStatistics(planId, stepCode);
            return AjaxResult.success("获取步骤数据统计成功", statistics);
        } catch (Exception e) {
            return handleException(e, "获取步骤数据统计失败");
        }
    }*/
}
