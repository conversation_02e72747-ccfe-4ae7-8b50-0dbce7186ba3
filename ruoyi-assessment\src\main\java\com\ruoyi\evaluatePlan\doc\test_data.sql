-- =====================================================
-- 评估计划流程管理测试数据
-- 支持多种评估类型的完整测试数据集
-- 生成时间: 2025-07-28
-- =====================================================

-- 清理现有测试数据
DELETE FROM `dsa_process_step_rollback` WHERE `id` > 0;
DELETE FROM `dsa_process_step_instance` WHERE `id` > 0;
DELETE FROM `dsa_process_instance` WHERE `id` > 0;
DELETE FROM `dsa_process_step_definition` WHERE `id` > 0;
DELETE FROM `dsa_process_definition` WHERE `id` > 0;

-- 重置自增ID
ALTER TABLE `dsa_process_definition` AUTO_INCREMENT = 1;
ALTER TABLE `dsa_process_instance` AUTO_INCREMENT = 1;
ALTER TABLE `dsa_process_step_definition` AUTO_INCREMENT = 1;
ALTER TABLE `dsa_process_step_instance` AUTO_INCREMENT = 1;
ALTER TABLE `dsa_process_step_rollback` AUTO_INCREMENT = 1;

-- =====================================================
-- 1. 流程定义数据 (dsa_process_definition)
-- =====================================================

INSERT INTO `dsa_process_definition` (`id`, `name`, `code`, `description`, `remark`, `status`, `create_by`, `create_time`) VALUES
(1, '数据安全风险评估方案', 'data_security_plan', '针对组织数据安全进行全面风险评估，包括数据资产识别、风险分析、等级评定和控制措施建议', '数据安全专项评估流程', 1, 'admin', NOW());

-- =====================================================
-- 2. 流程步骤定义数据 (dsa_process_step_definition)
-- =====================================================

-- 数据安全风险评估方案步骤
INSERT INTO `dsa_process_step_definition` (`process_id`, `step_order`, `step_name`, `step_code`, `handler_type`, `description`, `status`, `create_by`, `create_time`) VALUES
(1, 1, '数据资产识别', 'data_asset_identify', 'manual', '识别和梳理组织内所有数据资产，建立数据资产清单', 1, 'admin', NOW()),
(1, 2, '数据分类分级', 'data_classify', 'manual', '按照敏感程度对数据资产进行分类分级标记', 1, 'admin', NOW()),
(1, 3, '风险识别分析', 'risk_identify', 'auto', '识别数据安全相关的各类风险点和威胁', 1, 'admin', NOW()),
(1, 4, '风险等级评定', 'risk_assessment', 'auto', '对识别的风险进行等级评定和影响分析', 1, 'admin', NOW()),
(1, 5, '控制措施建议', 'control_measures', 'manual', '提出相应的安全控制措施和整改建议', 1, 'admin', NOW()),
(1, 6, '报告生成', 'report_generate', 'auto', '生成数据安全风险评估报告', 1, 'admin', NOW());

-- =====================================================
-- 3. 流程实例数据 (dsa_process_instance)
-- =====================================================

INSERT INTO `dsa_process_instance` (`id`, `process_id`, `business_id`, `remark`, `status`, `create_by`, `create_time`) VALUES
(1, 1, 1001, '某科技公司数据安全风险评估项目', 1, 'admin', '2025-07-28 09:00:00'),
(2, 1, 1002, '某医疗机构数据安全评估项目', 1, 'admin', '2025-07-28 14:00:00'),
(3, 1, 1003, '某金融机构数据安全评估项目', 1, 'admin', '2025-07-28 16:00:00');

-- =====================================================
-- 4. 流程步骤实例数据 (dsa_process_step_instance)
-- =====================================================

-- 流程实例1的步骤实例（数据安全风险评估 - 已完成）
INSERT INTO `dsa_process_step_instance` (`process_instance_id`, `step_definition_id`, `step_name`, `status`, `start_time`, `end_time`, `duration_ms`, `operator`, `remark`, `create_by`, `create_time`) VALUES
(1, 1, '数据资产识别', 2, '2025-07-28 09:00:00', '2025-07-28 10:30:00', 5400000, 'evaluator1', '已完成数据资产清单建立，共识别120项数据资产', 'admin', '2025-07-28 09:00:00'),
(1, 2, '数据分类分级', 2, '2025-07-28 10:30:00', '2025-07-28 12:00:00', 5400000, 'evaluator1', '完成数据分类分级，核心数据15项，重要数据45项，一般数据60项', 'admin', '2025-07-28 10:30:00'),
(1, 3, '风险识别分析', 2, '2025-07-28 12:00:00', '2025-07-28 13:30:00', 5400000, 'system', '系统自动识别15个风险点', 'admin', '2025-07-28 12:00:00'),
(1, 4, '风险等级评定', 2, '2025-07-28 13:30:00', '2025-07-28 14:00:00', 1800000, 'system', '风险等级评定完成：高风险3个，中风险7个，低风险5个', 'admin', '2025-07-28 13:30:00'),
(1, 5, '控制措施建议', 2, '2025-07-28 14:00:00', '2025-07-28 15:30:00', 5400000, 'evaluator2', '已提出20项控制措施建议', 'admin', '2025-07-28 14:00:00'),
(1, 6, '报告生成', 2, '2025-07-28 15:30:00', '2025-07-28 16:00:00', 1800000, 'system', '数据安全风险评估报告生成完成', 'admin', '2025-07-28 15:30:00');

-- 流程实例2的步骤实例（数据安全风险评估 - 进行中）
INSERT INTO `dsa_process_step_instance` (`process_instance_id`, `step_definition_id`, `step_name`, `status`, `start_time`, `end_time`, `duration_ms`, `operator`, `remark`, `create_by`, `create_time`) VALUES
(2, 1, '数据资产识别', 2, '2025-07-28 14:00:00', '2025-07-28 15:30:00', 5400000, 'evaluator3', '已完成数据资产清单建立，共识别85项数据资产', 'admin', '2025-07-28 14:00:00'),
(2, 2, '数据分类分级', 2, '2025-07-28 15:30:00', '2025-07-28 16:30:00', 3600000, 'evaluator3', '完成数据分类分级，核心数据8项，重要数据32项，一般数据45项', 'admin', '2025-07-28 15:30:00'),
(2, 3, '风险识别分析', 1, '2025-07-28 16:30:00', NULL, NULL, 'system', '正在进行风险识别分析', 'admin', '2025-07-28 16:30:00');

-- 流程实例3的步骤实例（数据安全风险评估 - 待开始）
INSERT INTO `dsa_process_step_instance` (`process_instance_id`, `step_definition_id`, `step_name`, `status`, `start_time`, `end_time`, `duration_ms`, `operator`, `remark`, `create_by`, `create_time`) VALUES
(3, 1, '数据资产识别', 1, '2025-07-28 16:00:00', NULL, NULL, 'evaluator4', '准备开始数据资产识别工作', 'admin', '2025-07-28 16:00:00');

-- =====================================================
-- 5. 流程回退记录数据 (dsa_process_step_rollback)
-- =====================================================

INSERT INTO `dsa_process_step_rollback` (`process_instance_id`, `from_step_instance_id`, `to_step_definition_id`, `rollback_time`, `operator`, `rollback_reason`, `remark`, `status`, `create_by`, `create_time`) VALUES
(2, 3, 2, '2025-07-28 17:00:00', 'supervisor1', '发现数据分类分级存在问题，需要重新分级', '回退到数据分类分级步骤重新处理', 1, 'admin', '2025-07-28 17:00:00');

-- =====================================================
-- 6. 评估类型表数据 (dsa_evaluate_type) - 如果需要的话
-- =====================================================

-- 注意：这个表可能已经存在，如果需要可以取消注释
/*
INSERT INTO `dsa_evaluate_type` (`process_code`, `title`, `sort`, `status`, `create_by`, `create_time`) VALUES
('data_security_plan', '数据安全风险评估方案', 1, 1, 'admin', NOW());
*/

-- =====================================================
-- 测试数据说明
-- =====================================================
/*
数据安全风险评估方案流程测试数据说明：

1. 流程定义：定义了数据安全风险评估方案的完整流程
2. 步骤定义：包含6个步骤，涵盖手动和自动处理
   - 数据资产识别（手动）
   - 数据分类分级（手动）
   - 风险识别分析（自动）
   - 风险等级评定（自动）
   - 控制措施建议（手动）
   - 报告生成（自动）

3. 流程实例：创建了3个流程实例作为测试案例
   - 实例1：已完成的完整流程
   - 实例2：进行中的流程（执行到第3步）
   - 实例3：刚开始的流程（第1步进行中）

4. 步骤实例：包含不同状态的步骤实例
   - 状态1：进行中
   - 状态2：已完成

5. 回退记录：演示流程回退功能
   - 从风险识别分析回退到数据分类分级

使用说明：
- 可以通过流程实例ID查询完整的执行流程
- 支持流程回退和重新执行
- 区分手动步骤和自动步骤的处理方式
- 记录详细的执行时间和操作人信息
- 与策略模式中的data_security_plan类型对应
*/
