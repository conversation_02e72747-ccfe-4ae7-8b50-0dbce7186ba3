package com.ruoyi.dataItem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataSystemMemberMapper;
import com.ruoyi.dataItem.domain.DataSystemMember;
import com.ruoyi.dataItem.service.IDataSystemMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B2 部门及人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class DataSystemMemberServiceImpl extends ServiceImpl<DataSystemMemberMapper, DataSystemMember> implements IDataSystemMemberService {

}
