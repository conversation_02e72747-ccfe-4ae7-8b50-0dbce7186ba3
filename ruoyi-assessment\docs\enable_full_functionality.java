// 数据库迁移完成后，用以下代码替换 EvaluateModelServiceImpl.java 中的相应方法

/**
 * 保存评估模型（重写以支持启用状态逻辑）
 * @param evaluateModel 评估模型
 * @return 保存结果
 */
@Override
@Transactional(rollbackFor = Exception.class)
public boolean save(EvaluateModel evaluateModel) {
    if (evaluateModel == null) {
        return false;
    }

    try {
        // 设置默认值
        if (evaluateModel.getStatus() == null) {
            evaluateModel.setStatus(1); // 默认正常状态
        }

        // 新添加的模型默认启用，其他同type_id的模型设置为不启用
        if (evaluateModel.getIsEnabled() == null) {
            evaluateModel.setIsEnabled(1); // 默认启用
        }

        // 如果新模型要启用，先禁用同type_id下的其他模型
        if (evaluateModel.getIsEnabled() == 1 && evaluateModel.getTypeId() != null) {
            try {
                int disabledCount = this.baseMapper.updateEnabledStatusByTypeId(
                    evaluateModel.getTypeId(), 0, null, SecurityUtils.getUsername(), new Date());
                log.info("新增模型时禁用同类型其他模型，typeId: {}, 禁用数量: {}", evaluateModel.getTypeId(), disabledCount);
            } catch (Exception e) {
                log.error("禁用同类型其他模型失败: {}", e.getMessage());
                // 如果失败，不影响主流程
            }
        }

        // 保存模型
        boolean result = super.save(evaluateModel);

        if (result) {
            log.info("成功保存评估模型，modelId: {}, typeId: {}, isEnabled: {}",
                evaluateModel.getId(), evaluateModel.getTypeId(), evaluateModel.getIsEnabled());
        }

        return result;
    } catch (Exception e) {
        log.error("保存评估模型失败", e);
        throw new ServiceException("保存评估模型失败: " + e.getMessage());
    }
}

/**
 * 启用某个评估模型（同时禁用同type_id下的其他模型）
 * @param modelId 要启用的模型ID
 * @return 操作结果
 */
@Override
@Transactional(rollbackFor = Exception.class)
public boolean enableModel(Long modelId) {
    if (modelId == null) {
        throw new ServiceException("模型ID不能为空");
    }

    // 1. 获取要启用的模型信息
    EvaluateModel model = this.getById(modelId);
    if (model == null) {
        throw new ServiceException("评估模型不存在");
    }

    try {
        // 2. 先禁用同type_id下的所有其他模型
        int disabledCount = this.baseMapper.updateEnabledStatusByTypeId(
            model.getTypeId(), 0, modelId, SecurityUtils.getUsername(), new Date());
        log.info("禁用同类型其他模型，typeId: {}, 禁用数量: {}", model.getTypeId(), disabledCount);

        // 3. 启用当前模型
        model.setIsEnabled(1);
        model.setUpdateBy(SecurityUtils.getUsername());
        model.setUpdateTime(new Date());
        boolean result = this.updateById(model);
        
        if (result) {
            log.info("成功启用评估模型，modelId: {}, typeId: {}", modelId, model.getTypeId());
        }
        
        return result;
    } catch (Exception e) {
        log.error("启用评估模型失败，modelId: {}", modelId, e);
        throw new ServiceException("启用评估模型失败: " + e.getMessage());
    }
}
