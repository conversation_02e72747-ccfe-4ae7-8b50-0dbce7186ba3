package com.ruoyi.evaluate.company.domain.excel;

import lombok.Data;

/**
 * 联系人信息Excel导入对象 - Sheet0
 * 用于固定单元格位置的表单读取
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ExcelContactUserInfo {

    /** 姓名 - 对应单元格B2 */
    private String contactName;

    /** 职务/职称 - 对应单元格C2 */
    private String job;

    /** 所属部门 - 对应单元格D2 */
    private String dept;

    /** 办公电话 - 对应单元格B3 */
    private String tel;

    /** 移动电话 - 对应单元格C3 */
    private String mobile;

    /** 电子邮件 - 对应单元格D3 */
    private String email;
}
