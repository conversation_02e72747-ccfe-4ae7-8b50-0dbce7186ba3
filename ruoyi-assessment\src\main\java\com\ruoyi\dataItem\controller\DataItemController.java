package com.ruoyi.dataItem.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.group.ListGroup;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItem;
import com.ruoyi.dataItem.service.IDataItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;

/**
 * 数据项管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/dataItem")
@Api(value = "数据项管理控制器", tags = {"数据项管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemController extends BaseController {
    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询数据项管理列表
     */
    @ApiOperation("查询数据项管理列表")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) DataItem dataItem) {
        startPage();
        List<DataItem> list = dataItemService.list(new QueryWrapper<DataItem>(dataItem));
        return getDataTable(list);
    }

    /**
     * 导出数据项管理列表
     */
    @ApiOperation("导出数据项管理列表")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:export')")
    @Log(title = "数据项管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataItem dataItem) {
        List<DataItem> list = dataItemService.list(new QueryWrapper<DataItem>(dataItem));
        ExcelUtil<DataItem> util = new ExcelUtil<DataItem>(DataItem.class);
        util.exportExcel(response, list, "数据项管理数据");
    }

    /**
     * 获取数据项管理详细信息
     */
    @ApiOperation("获取数据项管理详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemService.getById(id));
    }

    /**
     * 新增数据项管理
     */
    @ApiOperation("新增数据项管理")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:add')")
    @Log(title = "数据项管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody DataItem dataItem) {
        return toAjax(dataItemService.save(dataItem));
    }

    /**
     * 修改数据项管理
     */
    @ApiOperation("修改数据项管理")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:edit')")
    @Log(title = "数据项管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated(EditGroup.class) @RequestBody DataItem dataItem) {
        return toAjax(dataItemService.updateById(dataItem));
    }

    /**
     * 删除数据项管理
     */
    @ApiOperation("删除数据项管理")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:remove')")
    @Log(title = "数据项管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int successCount = 0;
        for (Long id : ids) {
            if (dataItemService.removeByIdWithLog(id)) {
                successCount++;
            }
        }
        return toAjax(successCount);
    }

    /**
     * 获取数据项备案目录Excel模板
     *
     * @param response
     */
    @ApiOperation("获取数据项备案目录Excel模板，文件流形式")
    @GetMapping("/getExcelTemplate")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/data_item/工业和信息化领域重要数据和核心数据目录备案表模板.xlsx";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "工业和信息化领域重要数据和核心数据目录备案表模板.xlsx");
    }

    @ApiOperation("导入数据项备案目录")
    @PreAuthorize("@ss.hasPermi('dataItem:dataItem:import')")
    @PostMapping("/import")
    public AjaxResult importPlanList(@RequestParam("file") MultipartFile file, @RequestParam("orgId") Long orgId) throws IOException {
        Map<String, Object> result = dataItemService.importExcelList(file, orgId);
        return AjaxResult.success(result);
    }

    @ApiOperation("备案目录数据项配置信息")
    @GetMapping("/getConfig")
    public AjaxResult getConfig() {
        String config = sysConfigService.selectConfigByKey("dsa.dataItem.import");
        Map<String, Object> map = new HashMap<>();
        map.put("import", null != config && (config.equals("1")));
        return AjaxResult.success(map);
    }

    @ApiOperation("通过评估计划提取数据项信息")
    @PostMapping("/importToPlan")
    public AjaxResult importToPlan(@RequestParam("planId") Long planId) {
        //todo 该方法需要实现
        List<DataItem> list = dataItemService.list(new QueryWrapper<DataItem>().eq("plan_id", planId));
        int count = dataItemService.batchImport(planId, list);
        return AjaxResult.success("成功导入" + count + "条数据项");
    }
}