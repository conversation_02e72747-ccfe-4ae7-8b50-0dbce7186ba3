package com.ruoyi.dataItem.service;

import com.ruoyi.dataItem.domain.DataItemBase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.exception.ServiceException;

/**
 * 数据项基本信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IDataItemBaseService extends IService<DataItemBase> {

    /**
     * 带校验保存数据项基本信息
     * @param dataItemBase 数据项基本信息
     * @return true-保存成功，false-失败
     * @throws ServiceException 校验失败时抛出
     */
    boolean saveWithValidation(DataItemBase dataItemBase);
}
