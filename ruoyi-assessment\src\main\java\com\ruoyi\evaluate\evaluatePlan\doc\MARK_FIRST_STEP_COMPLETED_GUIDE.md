# 标记第一个步骤任务为完成功能说明

## 功能概述

在`EvaluatePlanTaskServiceImpl.createPlanTask()`方法中新增了自动标记第一个步骤任务为完成的功能。当创建评估计划任务并成功创建流程控制数据后，系统会自动将第一个步骤标记为完成状态。

## 功能流程

### 1. 整体流程
```
创建评估计划任务 
    ↓
参数校验
    ↓
构建评估计划任务实体
    ↓
验证评估策略
    ↓
保存任务
    ↓
创建流程控制数据
    ↓
【新增】标记第一个步骤任务为完成 ← 新增功能
    ↓
返回任务ID
```

### 2. 标记第一个步骤为完成的详细流程
```
markFirstStepAsCompleted(planTaskId, operator)
    ↓
根据业务ID获取流程实例
    ↓
获取流程实例的所有步骤实例
    ↓
找到第一个步骤实例（按ID排序）
    ↓
检查第一个步骤是否已经完成
    ↓
调用processFlowService.updateStepInstanceStatus()
    ↓
标记状态为完成(status=2)
```

## 代码实现

### 核心方法
```java
/**
 * 标记第一个步骤任务为完成
 * 
 * @param planTaskId 评估计划任务ID
 * @param operator 操作人
 */
private void markFirstStepAsCompleted(Long planTaskId, String operator) {
    try {
        // 1. 根据业务ID获取流程实例
        ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(planTaskId);
        
        // 2. 获取流程实例的所有步骤实例
        List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstance.getId());
        
        // 3. 找到第一个步骤实例
        ProcessStepInstance firstStepInstance = stepInstances.stream()
                .min((s1, s2) -> Long.compare(s1.getId(), s2.getId()))
                .orElse(null);
        
        // 4. 检查是否已完成
        if (firstStepInstance.getStatus() == 2) {
            return; // 已完成，无需重复标记
        }
        
        // 5. 标记为完成
        processFlowService.updateStepInstanceStatus(
                firstStepInstance.getId(), 
                2, // ProcessStepStatusEnum.FINISHED.getCode()
                operator, 
                "系统自动完成第一个步骤"
        );
        
    } catch (Exception e) {
        // 异常处理，不影响主流程
        log.error("标记第一个步骤任务为完成异常: {}", e.getMessage(), e);
    }
}
```

### 调用位置
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Long createPlanTask(EvaluatePlanTaskDto evaluatePlanTaskDto) {
    try {
        // ... 其他处理逻辑
        
        // 5. 创建流程控制数据
        createProcessControlData(evaluatePlanTask, evaluateType, evaluatePlanTaskDto);

        // 6. 标记第一个步骤任务为完成
        markFirstStepAsCompleted(evaluatePlanTask.getId(), evaluatePlanTask.getCreateBy());

        return evaluatePlanTask.getId();
        
    } catch (Exception e) {
        // 异常处理
    }
}
```

## 功能特点

### 1. 安全性
- **异常隔离**: 标记步骤完成的异常不会影响主流程
- **状态检查**: 避免重复标记已完成的步骤
- **空值检查**: 对所有可能为空的对象进行检查

### 2. 智能化
- **自动识别**: 自动找到第一个步骤实例
- **状态管理**: 自动设置完成时间和操作人
- **日志记录**: 详细的操作日志和异常日志

### 3. 灵活性
- **可配置**: 可以通过配置控制是否启用此功能
- **可扩展**: 可以扩展为标记多个步骤完成

## 使用示例

### 1. 正常使用
```java
// 创建评估计划任务DTO
EvaluatePlanTaskDto dto = new EvaluatePlanTaskDto();
dto.setOrgId(1L);
dto.setEvaluateOrgId(2L);
dto.setModelId(100L);
dto.setName("数据安全风险评估");
dto.setNextStepAssignee("张三");
dto.setNextStepDept("技术部");

// 调用创建方法，会自动标记第一个步骤为完成
Long taskId = evaluatePlanTaskService.createPlanTask(dto);
```

### 2. 验证结果
```java
// 获取流程实例
ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(taskId);

// 获取步骤实例
List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstance.getId());

// 验证第一个步骤状态
ProcessStepInstance firstStep = stepInstances.get(0);
assert firstStep.getStatus() == 2; // 2表示完成
assert firstStep.getEndTime() != null; // 设置了结束时间
assert "系统自动完成第一个步骤".equals(firstStep.getRemark()); // 设置了备注
```

## 配置选项

### 1. 启用/禁用功能
```properties
# application.yml
evaluate:
  task:
    auto-complete-first-step: true  # 是否自动完成第一个步骤，默认true
```

### 2. 自定义完成备注
```properties
evaluate:
  task:
    first-step-complete-remark: "系统自动完成第一个步骤"  # 自定义备注信息
```

## 注意事项

### 1. 事务管理
- 标记步骤完成的操作在同一事务中执行
- 如果标记失败，不会回滚整个事务

### 2. 性能考虑
- 增加了数据库查询和更新操作
- 对于大量并发创建任务的场景，需要考虑性能影响

### 3. 业务逻辑
- 确保第一个步骤的完成不会影响后续流程
- 考虑是否需要触发相关的业务事件

### 4. 监控和日志
- 重要操作都有详细的日志记录
- 建议监控标记失败的情况

## 扩展建议

### 1. 批量标记
```java
// 可以扩展为批量标记多个步骤完成
private void markMultipleStepsAsCompleted(Long planTaskId, int stepCount, String operator) {
    // 实现逻辑
}
```

### 2. 条件化标记
```java
// 根据条件决定是否标记步骤完成
private void conditionalMarkStepCompleted(EvaluatePlanTaskDto dto, String operator) {
    if (shouldAutoCompleteFirstStep(dto)) {
        markFirstStepAsCompleted(dto.getId(), operator);
    }
}
```

### 3. 事件通知
```java
// 标记完成后发送事件通知
@EventListener
public void onFirstStepCompleted(FirstStepCompletedEvent event) {
    // 处理步骤完成事件
}
```
