<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemLogMapper">
    
    <resultMap type="DataItemLog" id="DataItemLogResult">
        <result property="id"    column="id"    />
        <result property="dataItemId"    column="data_item_id"    />
        <result property="orgId"    column="org_id"    />
        <result property="fieldName"    column="field_name"    />
        <result property="beforeValue"    column="before_value"    />
        <result property="afterValue"    column="after_value"    />
        <result property="operationType"    column="operation_type"    />
        <result property="operationUser"    column="operation_user"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataItemLogVo">
        select id, data_item_id, org_id, field_name, before_value, after_value, operation_type, operation_user, operation_time, del_flag from dsa_data_item_log
    </sql>

    <select id="selectDataItemLogList" parameterType="DataItemLog" resultMap="DataItemLogResult">
        <include refid="selectDataItemLogVo"/>
        <where>  
            <if test="dataItemId != null "> and data_item_id = #{dataItemId}</if>
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="beforeValue != null  and beforeValue != ''"> and before_value = #{beforeValue}</if>
            <if test="afterValue != null  and afterValue != ''"> and after_value = #{afterValue}</if>
            <if test="operationType != null "> and operation_type = #{operationType}</if>
            <if test="operationUser != null  and operationUser != ''"> and operation_user = #{operationUser}</if>
            <if test="operationTime != null "> and operation_time = #{operationTime}</if>
        </where>
    </select>
    
    <select id="selectDataItemLogById" parameterType="Long" resultMap="DataItemLogResult">
        <include refid="selectDataItemLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemLog" parameterType="DataItemLog" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataItemId != null">data_item_id,</if>
            <if test="orgId != null">org_id,</if>
            <if test="fieldName != null and fieldName != ''">field_name,</if>
            <if test="beforeValue != null">before_value,</if>
            <if test="afterValue != null">after_value,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="operationUser != null">operation_user,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataItemId != null">#{dataItemId},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="fieldName != null and fieldName != ''">#{fieldName},</if>
            <if test="beforeValue != null">#{beforeValue},</if>
            <if test="afterValue != null">#{afterValue},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="operationUser != null">#{operationUser},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataItemLog" parameterType="DataItemLog">
        update dsa_data_item_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataItemId != null">data_item_id = #{dataItemId},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="fieldName != null and fieldName != ''">field_name = #{fieldName},</if>
            <if test="beforeValue != null">before_value = #{beforeValue},</if>
            <if test="afterValue != null">after_value = #{afterValue},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="operationUser != null">operation_user = #{operationUser},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemLogById" parameterType="Long">
        delete from dsa_data_item_log where id = #{id}
    </delete>

    <delete id="deleteDataItemLogByIds" parameterType="String">
        delete from dsa_data_item_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByDataItemIds" parameterType="java.util.List">
        delete from dsa_data_item_log where data_item_id in
        <foreach collection="dataItemIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>