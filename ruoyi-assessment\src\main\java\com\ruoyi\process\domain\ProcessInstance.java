package com.ruoyi.process.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 流程实例对象 dsa_process_instance
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_process_instance")
public class ProcessInstance extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    @TableField(value = "process_id")
    private Long processId;

    /** 业务主键（如任务ID） */
    @Excel(name = "业务主键" , readConverterExp = "如=任务ID")
    @TableField(value = "business_id")
    private Long businessId;

    /** 业务类型 */
    @Excel(name = "业务类型")
    @TableField(value = "type")
    private String type;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}