package com.ruoyi.common.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.dto.DraftData;
import com.ruoyi.common.dto.DraftMetadata;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.SetOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用暂存服务实现类
 * 基于Redis实现数据暂存功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class DraftStorageServiceImpl implements IDraftStorageService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public <T> boolean saveDraft(String businessType, Long userId, String draftKey, T data) {
        return saveDraft(businessType, userId, draftKey, data, 
                        getDefaultExpireHours(), TimeUnit.HOURS);
    }

    @Override
    public <T> boolean saveDraft(String businessType, Long userId, String draftKey, T data, 
                                long timeout, TimeUnit timeUnit) {
        try {
            // 参数校验
            validateDraftParams(businessType, userId, draftKey, data);
            
            // 检查暂存功能是否启用
            if (!isDraftEnabled()) {
                throw new ServiceException("暂存功能已禁用");
            }
            
            // 检查用户暂存数据条数限制
            checkUserDraftCountLimit(businessType, userId);
            
            // 检查数据大小限制
            checkDataSizeLimit(data);
            
            // 构建暂存数据对象
            Date expireTime = calculateExpireTime(timeout, timeUnit);
            DraftData<T> draftData = DraftData.create(data, businessType, userId, draftKey, expireTime);
            
            // 设置元数据
            DraftMetadata metadata = draftData.getMetadata();
            metadata.setDataType(data.getClass().getSimpleName());
            metadata.setDataSize(calculateDataSize(data));
            metadata.setVersion(1);
            
            // 保存到Redis
            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            String indexKey = DraftConstants.buildDraftIndexKey(businessType, userId);
            String metadataKey = DraftConstants.buildDraftMetadataKey(businessType, userId, draftKey);
            
            // 保存数据
            redisCache.setCacheObject(cacheKey, draftData, (int) timeout, timeUnit);

            // 保存元数据
            redisCache.setCacheObject(metadataKey, metadata, (int) timeout, timeUnit);

            // 更新索引（使用Set操作）
            @SuppressWarnings("unchecked")
            SetOperations<String, Object> setOps = redisCache.redisTemplate.opsForSet();
            setOps.add(indexKey, draftKey);
            redisCache.expire(indexKey, timeout, timeUnit);
            
            log.info("暂存数据成功，businessType: {}, userId: {}, draftKey: {}", 
                    businessType, userId, draftKey);
            
            return true;
            
        } catch (Exception e) {
            log.error("暂存数据失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}", 
                    businessType, userId, draftKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public <T> T getDraft(String businessType, Long userId, String draftKey, Class<T> clazz) {
        try {
            validateBasicParams(businessType, userId, draftKey);
            
            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            DraftData<?> draftData = redisCache.getCacheObject(cacheKey);
            
            if (draftData == null) {
                return null;
            }
            
            // 检查是否过期
            if (draftData.isExpired()) {
                deleteDraft(businessType, userId, draftKey);
                return null;
            }
            
            Object data = draftData.getData();
            if (data == null) {
                return null;
            }
            
            // 类型转换
            if (clazz.isInstance(data)) {
                return clazz.cast(data);
            }
            
            // 尝试JSON转换
            return objectMapper.convertValue(data, clazz);
            
        } catch (Exception e) {
            log.error("获取暂存数据失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}", 
                    businessType, userId, draftKey, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean deleteDraft(String businessType, Long userId, String draftKey) {
        try {
            validateBasicParams(businessType, userId, draftKey);
            
            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            String indexKey = DraftConstants.buildDraftIndexKey(businessType, userId);
            String metadataKey = DraftConstants.buildDraftMetadataKey(businessType, userId, draftKey);
            
            // 删除数据
            redisCache.deleteObject(cacheKey);
            
            // 删除元数据
            redisCache.deleteObject(metadataKey);
            
            // 从索引中移除
            @SuppressWarnings("unchecked")
            SetOperations<String, Object> setOps = redisCache.redisTemplate.opsForSet();
            setOps.remove(indexKey, draftKey);
            
            log.info("删除暂存数据成功，businessType: {}, userId: {}, draftKey: {}", 
                    businessType, userId, draftKey);
            
            return true;
            
        } catch (Exception e) {
            log.error("删除暂存数据失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}", 
                    businessType, userId, draftKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getUserDraftKeys(String businessType, Long userId) {
        try {
            validateBasicParams(businessType, userId, "dummy");
            
            String indexKey = DraftConstants.buildDraftIndexKey(businessType, userId);
            Set<Object> keys = redisCache.getCacheSet(indexKey);
            
            if (keys == null || keys.isEmpty()) {
                return new ArrayList<>();
            }
            
            return keys.stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("获取用户暂存键列表失败，businessType: {}, userId: {}, 错误: {}", 
                    businessType, userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Map<String, Object>> getUserDraftSummary(String businessType, Long userId) {
        try {
            List<String> draftKeys = getUserDraftKeys(businessType, userId);
            Map<String, Map<String, Object>> summary = new HashMap<>();
            
            for (String draftKey : draftKeys) {
                String metadataKey = DraftConstants.buildDraftMetadataKey(businessType, userId, draftKey);
                DraftMetadata metadata = redisCache.getCacheObject(metadataKey);
                
                if (metadata != null) {
                    summary.put(draftKey, metadata.toSummaryMap());
                }
            }
            
            return summary;
            
        } catch (Exception e) {
            log.error("获取用户暂存数据摘要失败，businessType: {}, userId: {}, 错误: {}", 
                    businessType, userId, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public int clearUserDrafts(String businessType, Long userId) {
        try {
            List<String> draftKeys = getUserDraftKeys(businessType, userId);
            int count = 0;
            
            for (String draftKey : draftKeys) {
                if (deleteDraft(businessType, userId, draftKey)) {
                    count++;
                }
            }
            
            log.info("清理用户暂存数据完成，businessType: {}, userId: {}, 清理条数: {}", 
                    businessType, userId, count);
            
            return count;
            
        } catch (Exception e) {
            log.error("清理用户暂存数据失败，businessType: {}, userId: {}, 错误: {}", 
                    businessType, userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int clearExpiredDrafts(String businessType) {
        try {
            String pattern = DraftConstants.buildBusinessTypePattern(businessType);
            Collection<String> keys = redisCache.keys(pattern);
            int count = 0;
            
            for (String key : keys) {
                Long userId = DraftConstants.parseUserIdFromKey(key);
                String draftKey = DraftConstants.parseDraftKeyFromKey(key);
                
                if (userId != null && draftKey != null) {
                    DraftData<?> draftData = redisCache.getCacheObject(key);
                    if (draftData != null && draftData.isExpired()) {
                        if (deleteDraft(businessType, userId, draftKey)) {
                            count++;
                        }
                    }
                }
            }
            
            log.info("清理过期暂存数据完成，businessType: {}, 清理条数: {}", businessType, count);
            
            return count;
            
        } catch (Exception e) {
            log.error("清理过期暂存数据失败，businessType: {}, 错误: {}", businessType, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean existsDraft(String businessType, Long userId, String draftKey) {
        try {
            validateBasicParams(businessType, userId, draftKey);
            
            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            return redisCache.hasKey(cacheKey);
            
        } catch (Exception e) {
            log.error("检查暂存数据是否存在失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}", 
                    businessType, userId, draftKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long getDraftTtl(String businessType, Long userId, String draftKey) {
        try {
            validateBasicParams(businessType, userId, draftKey);
            
            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            return redisCache.getExpire(cacheKey);
            
        } catch (Exception e) {
            log.error("获取暂存数据TTL失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}", 
                    businessType, userId, draftKey, e.getMessage(), e);
            return -2;
        }
    }

    @Override
    public boolean updateDraftTtl(String businessType, Long userId, String draftKey,
                                 long timeout, TimeUnit timeUnit) {
        try {
            validateBasicParams(businessType, userId, draftKey);

            String cacheKey = DraftConstants.buildDraftCacheKey(businessType, userId, draftKey);
            String indexKey = DraftConstants.buildDraftIndexKey(businessType, userId);
            String metadataKey = DraftConstants.buildDraftMetadataKey(businessType, userId, draftKey);

            // 更新数据TTL
            boolean result1 = redisCache.expire(cacheKey, timeout, timeUnit);

            // 更新元数据TTL
            boolean result2 = redisCache.expire(metadataKey, timeout, timeUnit);

            // 更新索引TTL
            boolean result3 = redisCache.expire(indexKey, timeout, timeUnit);

            return result1 && result2 && result3;

        } catch (Exception e) {
            log.error("更新暂存数据TTL失败，businessType: {}, userId: {}, draftKey: {}, 错误: {}",
                    businessType, userId, draftKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int batchSaveDrafts(String businessType, Long userId, Map<String, Object> drafts,
                              long timeout, TimeUnit timeUnit) {
        if (drafts == null || drafts.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (Map.Entry<String, Object> entry : drafts.entrySet()) {
            if (saveDraft(businessType, userId, entry.getKey(), entry.getValue(), timeout, timeUnit)) {
                successCount++;
            }
        }

        return successCount;
    }

    @Override
    public <T> Map<String, T> batchGetDrafts(String businessType, Long userId,
                                            List<String> draftKeys, Class<T> clazz) {
        Map<String, T> result = new HashMap<>();

        if (draftKeys == null || draftKeys.isEmpty()) {
            return result;
        }

        for (String draftKey : draftKeys) {
            T data = getDraft(businessType, userId, draftKey, clazz);
            if (data != null) {
                result.put(draftKey, data);
            }
        }

        return result;
    }

    @Override
    public int batchDeleteDrafts(String businessType, Long userId, List<String> draftKeys) {
        if (draftKeys == null || draftKeys.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (String draftKey : draftKeys) {
            if (deleteDraft(businessType, userId, draftKey)) {
                successCount++;
            }
        }

        return successCount;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验暂存参数
     */
    private <T> void validateDraftParams(String businessType, Long userId, String draftKey, T data) {
        validateBasicParams(businessType, userId, draftKey);

        if (data == null) {
            throw new IllegalArgumentException("暂存数据不能为空");
        }
    }

    /**
     * 校验基本参数
     */
    private void validateBasicParams(String businessType, Long userId, String draftKey) {
        if (StringUtils.isBlank(businessType)) {
            throw new IllegalArgumentException("业务类型不能为空");
        }

        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        if (StringUtils.isBlank(draftKey)) {
            throw new IllegalArgumentException("暂存键不能为空");
        }
    }

    /**
     * 检查暂存功能是否启用
     */
    private boolean isDraftEnabled() {
        String enabled = sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_DRAFT_ENABLED);
        return "1".equals(enabled) || "true".equalsIgnoreCase(enabled);
    }

    /**
     * 获取默认过期时间（小时）
     */
    private int getDefaultExpireHours() {
        String hours = sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_DEFAULT_EXPIRE_HOURS);
        try {
            return StringUtils.isNotBlank(hours) ? Integer.parseInt(hours) : DraftConstants.DEFAULT_EXPIRE_HOURS;
        } catch (NumberFormatException e) {
            return DraftConstants.DEFAULT_EXPIRE_HOURS;
        }
    }

    /**
     * 检查用户暂存数据条数限制
     */
    private void checkUserDraftCountLimit(String businessType, Long userId) {
        List<String> existingKeys = getUserDraftKeys(businessType, userId);
        int maxCount = getMaxCountPerUser();

        if (existingKeys.size() >= maxCount) {
            throw new ServiceException("用户暂存数据条数已达上限：" + maxCount);
        }
    }

    /**
     * 获取单用户最大暂存条数
     */
    private int getMaxCountPerUser() {
        String count = sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_MAX_COUNT_PER_USER);
        try {
            return StringUtils.isNotBlank(count) ? Integer.parseInt(count) : DraftConstants.MAX_DRAFT_COUNT_PER_USER;
        } catch (NumberFormatException e) {
            return DraftConstants.MAX_DRAFT_COUNT_PER_USER;
        }
    }

    /**
     * 检查数据大小限制
     */
    private <T> void checkDataSizeLimit(T data) {
        long dataSize = calculateDataSize(data);
        long maxSize = getMaxDataSizeBytes();

        if (dataSize > maxSize) {
            throw new ServiceException("暂存数据大小超过限制：" + dataSize + " > " + maxSize + " bytes");
        }
    }

    /**
     * 计算数据大小
     */
    private <T> long calculateDataSize(T data) {
        try {
            String json = objectMapper.writeValueAsString(data);
            return json.getBytes("UTF-8").length;
        } catch (Exception e) {
            log.warn("计算数据大小失败，使用默认值：{}", e.getMessage());
            return 1024; // 默认1KB
        }
    }

    /**
     * 获取最大数据大小（字节）
     */
    private long getMaxDataSizeBytes() {
        String sizeMb = sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_MAX_SIZE_MB);
        try {
            int mb = StringUtils.isNotBlank(sizeMb) ? Integer.parseInt(sizeMb) : 1;
            return mb * 1024 * 1024L;
        } catch (NumberFormatException e) {
            return DraftConstants.MAX_DRAFT_SIZE_BYTES;
        }
    }

    /**
     * 计算过期时间
     */
    private Date calculateExpireTime(long timeout, TimeUnit timeUnit) {
        long millis = timeUnit.toMillis(timeout);
        return new Date(System.currentTimeMillis() + millis);
    }
}
