# 项目部署结构说明

## 📁 新的目录结构

```
项目根目录/
├── ruoyi-admin/                 # 应用模块
├── ruoyi-framework/             # 框架模块
├── ruoyi-system/                # 系统模块
├── ruoyi-common/                # 通用模块
├── ruoyi-assessment/            # 评估模块
├── ruoyi-generator/             # 代码生成模块
├── ruoyi-quartz/                # 定时任务模块
├── pom.xml                      # Maven主配置
├── sql/                         # 原始SQL脚本
└── deploy/                      # 🆕 Docker部署目录
    ├── Dockerfile               # Docker镜像构建文件
    ├── docker-compose.yml       # Docker Compose配置
    ├── .env                     # 环境变量配置
    ├── .dockerignore           # Docker忽略文件
    ├── deploy.sh               # Linux/macOS部署脚本
    ├── deploy.bat              # Windows部署脚本
    ├── ruoyi_admin.jar         # 🆕 应用JAR包（需要您提供）
    ├── README.md               # 部署说明
    ├── JAR_DEPLOYMENT_GUIDE.md # JAR部署指南
    ├── MYSQL_5.7_NOTES.md      # MySQL配置说明
    ├── mysql/
    │   └── conf/
    │       └── my.cnf          # MySQL配置文件
    ├── redis/
    │   └── redis.conf          # Redis配置文件
    ├── nginx/
    │   └── conf.d/
    │       └── default.conf    # Nginx配置文件
    └── sql/                    # 数据库初始化脚本目录
        └── README.md           # SQL脚本说明
```

## 🔄 迁移说明

### 1. 所有部署相关文件已移至 `deploy/` 目录
- Docker配置文件
- 部署脚本
- 服务配置文件
- 部署文档

### 2. JAR包命名变更
- **旧命名**: `app.jar`
- **新命名**: `ruoyi_admin.jar`
- **位置**: `deploy/ruoyi_admin.jar`

### 3. 工作目录变更
- **旧方式**: 在项目根目录执行部署命令
- **新方式**: 在 `deploy/` 目录下执行部署命令

## 🚀 新的使用流程

### 1. 准备JAR包
```bash
# 编译项目（在项目根目录）
mvn clean package -DskipTests

# 复制JAR包到部署目录
cp ruoyi-admin/target/ruoyi-admin.jar deploy/ruoyi_admin.jar
```

### 2. 进入部署目录
```bash
cd deploy
```

### 3. 执行部署
```bash
# Linux/macOS
chmod +x deploy.sh
./deploy.sh start

# Windows
deploy.bat start
```

## 📋 主要优势

### 1. 目录结构清晰
- 部署相关文件集中管理
- 避免项目根目录文件混乱
- 便于版本控制和维护

### 2. 部署流程简化
- 所有操作在deploy目录下完成
- JAR包命名规范化
- 脚本功能更完善

### 3. 配置管理优化
- 环境配置集中化
- 服务配置模块化
- 文档说明详细化

## 🔧 配置文件说明

### 核心配置文件
- `docker-compose.yml` - 服务编排配置
- `.env` - 环境变量配置
- `Dockerfile` - 镜像构建配置

### 服务配置文件
- `mysql/conf/my.cnf` - MySQL数据库配置
- `redis/redis.conf` - Redis缓存配置
- `nginx/conf.d/default.conf` - Nginx代理配置

### 部署脚本
- `deploy.sh` - Linux/macOS部署脚本
- `deploy.bat` - Windows部署脚本

## 📖 文档说明

### 部署文档
- `README.md` - 快速开始指南
- `JAR_DEPLOYMENT_GUIDE.md` - JAR包部署详细说明
- `MYSQL_5.7_NOTES.md` - MySQL 5.7配置说明

### 项目文档
- `../DOCKER_DEPLOY.md` - 完整部署指南（项目根目录）
- `sql/README.md` - 数据库脚本说明

## ⚠️ 注意事项

### 1. JAR包要求
- 文件名必须为 `ruoyi_admin.jar`
- 必须是可执行的Spring Boot JAR包
- 文件大小通常在50-200MB之间

### 2. 目录权限
- Linux/macOS需要给脚本执行权限
- 确保当前用户有Docker操作权限

### 3. 端口占用
- 8080 - 应用端口
- 3306 - MySQL端口
- 6379 - Redis端口
- 80/443 - Nginx端口

### 4. 数据持久化
- 数据库数据存储在Docker卷中
- 应用日志和上传文件也会持久化
- 清理数据需要使用 `./deploy.sh clean` 命令

## 🆘 故障排除

如果遇到问题：

1. **检查JAR包**: `./deploy.sh check`
2. **查看服务状态**: `./deploy.sh status`
3. **查看日志**: `./deploy.sh logs`
4. **参考文档**: 查看deploy目录下的详细文档

---

**总结**: 新的部署结构更加规范和易用，所有部署相关的操作都在 `deploy/` 目录下完成，提供了更好的用户体验和维护性。
