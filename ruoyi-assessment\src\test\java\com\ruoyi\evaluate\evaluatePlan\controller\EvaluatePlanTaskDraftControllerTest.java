package com.ruoyi.evaluate.evaluatePlan.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 评估计划任务暂存控制器单元测试
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@WebMvcTest(EvaluatePlanTaskDraftController.class)
class EvaluatePlanTaskDraftControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IEvaluatePlanTaskService evaluatePlanTaskService;

    @MockBean
    private IDraftStorageService draftStorageService;

    @Autowired
    private ObjectMapper objectMapper;

    private EvaluatePlanTaskDto testTaskDto;
    private static final String DRAFT_KEY = "test_draft_001";
    private static final Long USER_ID = 1L;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testTaskDto = new EvaluatePlanTaskDto();
        testTaskDto.setOrgId(1L);
        testTaskDto.setEvaluateOrgId(2L);
        testTaskDto.setModelId(100L);
        testTaskDto.setName("测试评估计划任务");
        testTaskDto.setTaskDescription("这是一个测试任务");
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testSaveDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.saveDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY),
                any(EvaluatePlanTaskDto.class),
                eq(24L),
                eq(TimeUnit.HOURS)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/evaluatePlan/task/draft/save")
                        .with(csrf())
                        .param("draftKey", DRAFT_KEY)
                        .param("expireHours", "24")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("暂存成功"));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).saveDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY),
                any(EvaluatePlanTaskDto.class),
                eq(24L),
                eq(TimeUnit.HOURS)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testGetDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.getDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(EvaluatePlanTaskDto.class)
        )).thenReturn(testTaskDto);

        // 执行测试
        mockMvc.perform(get("/evaluatePlan/task/draft/get/{draftKey}", DRAFT_KEY))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("获取成功"))
                .andExpect(jsonPath("$.data.name").value("测试评估计划任务"))
                .andExpect(jsonPath("$.data.orgId").value(1))
                .andExpect(jsonPath("$.data.modelId").value(100));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).getDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(EvaluatePlanTaskDto.class)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testDeleteDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.deleteDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/evaluatePlan/task/draft/delete/{draftKey}", DRAFT_KEY)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).deleteDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testGetDraftList_Success() throws Exception {
        // 准备测试数据
        Map<String, Map<String, Object>> mockSummary = new HashMap<>();
        Map<String, Object> draftInfo = new HashMap<>();
        draftInfo.put("businessType", DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK);
        draftInfo.put("userId", USER_ID);
        draftInfo.put("draftKey", DRAFT_KEY);
        draftInfo.put("createTime", "2025-07-30 10:00:00");
        mockSummary.put(DRAFT_KEY, draftInfo);

        // 设置Mock行为
        when(draftStorageService.getUserDraftSummary(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID)
        )).thenReturn(mockSummary);

        // 执行测试
        mockMvc.perform(get("/evaluatePlan/task/draft/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("获取成功"))
                .andExpect(jsonPath("$.data").isMap())
                .andExpect(jsonPath("$.data['" + DRAFT_KEY + "'].userId").value(USER_ID));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testClearDrafts_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.clearUserDrafts(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID)
        )).thenReturn(3);

        // 执行测试
        mockMvc.perform(delete("/evaluatePlan/task/draft/clear")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("清理完成"))
                .andExpect(jsonPath("$.data.clearedCount").value(3));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:draft"})
    void testExistsDraft_True() throws Exception {
        // 设置Mock行为
        when(draftStorageService.existsDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(true);
        
        when(draftStorageService.getDraftTtl(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(3600L);

        // 执行测试
        mockMvc.perform(get("/evaluatePlan/task/draft/exists/{draftKey}", DRAFT_KEY))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("检查完成"))
                .andExpect(jsonPath("$.data.exists").value(true))
                .andExpect(jsonPath("$.data.ttl").value(3600));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"evaluatePlan:task:add"})
    void testCreateFromDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.getDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(EvaluatePlanTaskDto.class)
        )).thenReturn(testTaskDto);
        
        when(evaluatePlanTaskService.createPlanTask(any(EvaluatePlanTaskDto.class)))
                .thenReturn(123L);
        
        when(draftStorageService.deleteDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/evaluatePlan/task/draft/create/{draftKey}", DRAFT_KEY)
                        .with(csrf())
                        .param("deleteDraft", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("创建成功"))
                .andExpect(jsonPath("$.data.id").value(123))
                .andExpect(jsonPath("$.data.draftDeleted").value(true));

        // 验证服务方法被调用
        verify(evaluatePlanTaskService, times(1)).createPlanTask(any(EvaluatePlanTaskDto.class));
        verify(draftStorageService, times(1)).deleteDraft(
                eq(DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK),
                eq(USER_ID),
                eq(DRAFT_KEY)
        );
    }

    @Test
    void testSaveDraft_NoPermission() throws Exception {
        // 执行测试（没有权限）
        mockMvc.perform(post("/evaluatePlan/task/draft/save")
                        .with(csrf())
                        .param("draftKey", DRAFT_KEY)
                        .param("expireHours", "24")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDto)))
                .andExpect(status().isForbidden());

        // 验证服务方法没有被调用
        verify(draftStorageService, never()).saveDraft(
                any(String.class),
                any(Long.class),
                any(String.class),
                any(EvaluatePlanTaskDto.class),
                any(Long.class),
                any(TimeUnit.class)
        );
    }
}
