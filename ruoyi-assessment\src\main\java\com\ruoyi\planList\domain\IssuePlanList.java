package com.ruoyi.planList.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 问题整改计划清单对象 dsa_issue_plan_list
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_issue_plan_list")
public class IssuePlanList extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 单位名称 */
    @Excel(name = "单位名称")
    @TableField(value = "org_name")
    private String orgName;

    /** 数据项名称 */
    @Excel(name = "数据项名称")
    @TableField(value = "data_item_name")
    private String dataItemName;

    /** 数据类型 */
    @Excel(name = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    /** 数据处理活动 */
    @Excel(name = "数据处理活动")
    @TableField(value = "data_processing_activity")
    private String dataProcessingActivity;

    /** 数据级别 */
    @Excel(name = "数据级别")
    @TableField(value = "data_level")
    private String dataLevel;

    /** 及信息系统名称 */
    @Excel(name = "及信息系统名称")
    @TableField(value = "info_system_name")
    private String infoSystemName;

    /** 重要系统类别 */
    @Excel(name = "重要系统类别")
    @TableField(value = "important_system_category")
    private String importantSystemCategory;

    /** 业务条线 */
    @Excel(name = "业务条线")
    @TableField(value = "business_line")
    private String businessLine;

    /** 问题类别 */
    @Excel(name = "问题类别")
    @TableField(value = "issue_category")
    private String issueCategory;

    /** 问题描述 */
    @Excel(name = "问题描述")
    @TableField(value = "issue_description")
    private String issueDescription;

    /** 问题发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "问题发现时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "issue_found_time")
    private Date issueFoundTime;

    /** 计划整改完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划整改完成时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "plan_rectify_time")
    private Date planRectifyTime;

    /** 实际整改完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际整改完成时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "actual_rectify_time")
    private Date actualRectifyTime;

    /** 是否完成整改 */
    @Excel(name = "是否完成整改")
    @TableField(value = "is_rectified")
    private Integer isRectified;

    /** 整改措施 */
    @Excel(name = "整改措施")
    @TableField(value = "rectify_measures")
    private String rectifyMeasures;

    /** 整改计划 */
    @Excel(name = "整改计划")
    @TableField(value = "rectify_plan")
    private String rectifyPlan;

    /** 数据处理活动风险 */
    @Excel(name = "数据处理活动风险")
    @TableField(value = "data_processing_risk")
    private String dataProcessingRisk;

    /** 问题整改后风险等级 */
    @Excel(name = "问题整改后风险等级")
    @TableField(value = "risk_after_rectify")
    private String riskAfterRectify;

    /** 在报送工信部考核的企业整改整体报告中 */
    @Excel(name = "在报送工信部考核的企业整改整体报告中")
    @TableField(value = "in_miit_report")
    private Integer inMiitReport;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}