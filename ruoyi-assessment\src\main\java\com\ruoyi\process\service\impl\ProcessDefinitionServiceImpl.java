package com.ruoyi.process.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.process.mapper.ProcessDefinitionMapper;
import com.ruoyi.process.domain.ProcessDefinition;
import com.ruoyi.process.service.IProcessDefinitionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 流程定义Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class ProcessDefinitionServiceImpl extends ServiceImpl<ProcessDefinitionMapper, ProcessDefinition> implements IProcessDefinitionService {

}
