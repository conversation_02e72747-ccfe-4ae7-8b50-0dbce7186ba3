package com.ruoyi.evaluateModel.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluateModel.domain.EvaluateType;
import com.ruoyi.evaluateModel.service.IEvaluateTypeService;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估类型Controller
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/evaluateType/evaluateType")
@Api(value = "评估类型控制器", tags = {"评估类型管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateTypeController extends BaseController
{
    private final IEvaluateTypeService evaluateTypeService;
    private final IEvaluateModelService evaluateModelService;

    /**
     * 查询评估类型列表（包含关联的评估模型）
     */
    @ApiOperation("查询评估类型列表")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluateType evaluateType) {
        startPage();
        List<EvaluateType> list = evaluateTypeService.listWithModel(evaluateType);
        return getDataTable(list);
    }

    /**
     * 获取评估类型详细信息
     */
    @ApiOperation("获取评估类型详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateTypeService.getById(id));
    }

    /**
     * 新增评估类型
     */
    @ApiOperation("新增评估类型")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:add')")
    @Log(title = "评估类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluateType evaluateType) {
        return toAjax(evaluateTypeService.save(evaluateType));
    }

    /**
     * 修改评估类型
     */
    @ApiOperation("修改评估类型")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:edit')")
    @Log(title = "评估类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluateType evaluateType) {
        return toAjax(evaluateTypeService.updateById(evaluateType));
    }

    /**
     * 删除评估类型
     */
    @ApiOperation("删除评估类型")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:remove')")
    @Log(title = "评估类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateTypeService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 启用某个评估模型
     */
    @ApiOperation("启用某个评估模型")
    @PreAuthorize("@ss.hasPermi('evaluateType:evaluateType:edit')")
    @Log(title = "启用评估模型", businessType = BusinessType.UPDATE)
    @PutMapping("/enableModel/{modelId}")
    public AjaxResult enableModel(@PathVariable("modelId") Long modelId) {
        boolean result = evaluateModelService.enableModel(modelId);
        return result ? AjaxResult.success("模型启用成功") : AjaxResult.error("模型启用失败");
    }
}