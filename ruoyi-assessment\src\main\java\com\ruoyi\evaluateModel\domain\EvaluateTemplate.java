package com.ruoyi.evaluateModel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 评估报告模板对象 dsa_evaluate_template
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_template")
public class EvaluateTemplate extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    @TableField(value = "title")
    private String title;

    /** 评估模型ID（关联评估模型表） */
    @NotNull(message = "评估模型不能为空", groups = {AddGroup.class})
    @Excel(name = "评估模型ID")
    @TableField(value = "model_id")
    private Long modelId;

    /** 模板文件路径 */
    @NotNull(message = "模板文件路径不能为空", groups = {AddGroup.class})
    @Excel(name = "模板文件路径")
    @TableField(value = "template_file")
    private String templateFile;

    /** 模板类型，1-系统 2-自定义 */
    @Excel(name = "模板类型")
    @TableField(value = "type")
    private Integer type;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;
}