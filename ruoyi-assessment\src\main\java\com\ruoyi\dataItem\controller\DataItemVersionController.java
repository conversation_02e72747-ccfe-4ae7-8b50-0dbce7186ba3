package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemVersion;
import com.ruoyi.dataItem.service.IDataItemVersionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据项版本Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/version")
@Api(value = "数据项版本控制器", tags = {"数据项版本管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemVersionController extends BaseController {
    private final IDataItemVersionService dataItemVersionService;

    /**
     * 查询数据项版本列表
     */
    @ApiOperation("查询数据项版本列表")
    @PreAuthorize("@ss.hasPermi('dataItem:version:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataItemVersion dataItemVersion) {
        startPage();
        List<DataItemVersion> list = dataItemVersionService.list(new QueryWrapper<DataItemVersion>(dataItemVersion));
        return getDataTable(list);
    }

    /**
     * 获取数据项版本详细信息
     */
    @ApiOperation("获取数据项版本详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:version:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemVersionService.getById(id));
    }

    /**
     * 新增数据项版本
     */
    @ApiOperation("新增数据项版本")
    @PreAuthorize("@ss.hasPermi('dataItem:version:add')")
    @Log(title = "数据项版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DataItemVersion dataItemVersion) {
        return toAjax(dataItemVersionService.add(dataItemVersion));
    }

    /**
     * 修改数据项版本
     */
    @ApiOperation("修改数据项版本")
    @PreAuthorize("@ss.hasPermi('dataItem:version:edit')")
    @Log(title = "数据项版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemVersion dataItemVersion) {
        return toAjax(dataItemVersionService.updateById(dataItemVersion));
    }

    /**
     * 删除数据项版本
     */
    @ApiOperation("删除数据项版本")
    @PreAuthorize("@ss.hasPermi('dataItem:version:remove')")
    @Log(title = "数据项版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemVersionService.removeByIds(Arrays.asList(ids)));
    }
}