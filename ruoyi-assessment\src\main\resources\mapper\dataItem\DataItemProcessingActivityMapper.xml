<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemProcessingActivityMapper">
    
    <resultMap type="DataItemProcessingActivity" id="DataItemProcessingActivityResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="dataSystemId"    column="data_system_id"    />
        <result property="dataItemId"    column="data_item_id"    />
        <result property="activityNo"    column="activity_no"    />
        <result property="processingActivityName"    column="processing_activity_name"    />
        <result property="purpose"    column="purpose"    />
        <result property="handleWay"    column="handle_way"    />
        <result property="frequency"    column="frequency"    />
        <result property="dataSource"    column="data_source"    />
        <result property="dataCrossBorder"    column="data_cross_border"    />
        <result property="relateSystem"    column="relate_system"    />
        <result property="masterPerson"    column="master_person"    />
        <result property="masterDept"    column="master_dept"    />
        <result property="isEvaluate"    column="is_evaluate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataItemProcessingActivityVo">
        select id, org_id, data_system_id, data_item_id, activity_no, processing_activity_name, purpose, handle_way, frequency, data_source, data_cross_border, relate_system, master_person, master_dept, is_evaluate, create_time, update_time, del_flag from dsa_data_item_processing_activity
    </sql>

    <select id="selectDataItemProcessingActivityList" parameterType="DataItemProcessingActivity" resultMap="DataItemProcessingActivityResult">
        <include refid="selectDataItemProcessingActivityVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="dataSystemId != null "> and data_system_id = #{dataSystemId}</if>
            <if test="dataItemId != null "> and data_item_id = #{dataItemId}</if>
            <if test="activityNo != null  and activityNo != ''"> and activity_no = #{activityNo}</if>
            <if test="processingActivityName != null  and processingActivityName != ''"> and processing_activity_name like concat('%', #{processingActivityName}, '%')</if>
            <if test="purpose != null  and purpose != ''"> and purpose = #{purpose}</if>
            <if test="handleWay != null  and handleWay != ''"> and handle_way = #{handleWay}</if>
            <if test="frequency != null  and frequency != ''"> and frequency = #{frequency}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="dataCrossBorder != null  and dataCrossBorder != ''"> and data_cross_border = #{dataCrossBorder}</if>
            <if test="relateSystem != null  and relateSystem != ''"> and relate_system = #{relateSystem}</if>
            <if test="masterPerson != null  and masterPerson != ''"> and master_person = #{masterPerson}</if>
            <if test="masterDept != null  and masterDept != ''"> and master_dept = #{masterDept}</if>
            <if test="isEvaluate != null  and isEvaluate != ''"> and is_evaluate = #{isEvaluate}</if>
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
        </where>
    </select>
    
    <select id="selectDataItemProcessingActivityById" parameterType="Long" resultMap="DataItemProcessingActivityResult">
        <include refid="selectDataItemProcessingActivityVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemProcessingActivity" parameterType="DataItemProcessingActivity" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_processing_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="dataSystemId != null">data_system_id,</if>
            <if test="dataItemId != null">data_item_id,</if>
            <if test="activityNo != null">activity_no,</if>
            <if test="processingActivityName != null">processing_activity_name,</if>
            <if test="purpose != null">purpose,</if>
            <if test="handleWay != null">handle_way,</if>
            <if test="frequency != null">frequency,</if>
            <if test="dataSource != null and dataSource != ''">data_source,</if>
            <if test="dataCrossBorder != null">data_cross_border,</if>
            <if test="relateSystem != null">relate_system,</if>
            <if test="masterPerson != null">master_person,</if>
            <if test="masterDept != null">master_dept,</if>
            <if test="isEvaluate != null">is_evaluate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="uuid != null">uuid,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="dataSystemId != null">#{dataSystemId},</if>
            <if test="dataItemId != null">#{dataItemId},</if>
            <if test="activityNo != null">#{activityNo},</if>
            <if test="processingActivityName != null">#{processingActivityName},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="handleWay != null">#{handleWay},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="dataSource != null and dataSource != ''">#{dataSource},</if>
            <if test="dataCrossBorder != null">#{dataCrossBorder},</if>
            <if test="relateSystem != null">#{relateSystem},</if>
            <if test="masterPerson != null">#{masterPerson},</if>
            <if test="masterDept != null">#{masterDept},</if>
            <if test="isEvaluate != null">#{isEvaluate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="uuid != null">#{uuid},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataItemProcessingActivity" parameterType="DataItemProcessingActivity">
        update dsa_data_item_processing_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="dataSystemId != null">data_system_id = #{dataSystemId},</if>
            <if test="dataItemId != null">data_item_id = #{dataItemId},</if>
            <if test="activityNo != null">activity_no = #{activityNo},</if>
            <if test="processingActivityName != null">processing_activity_name = #{processingActivityName},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="handleWay != null">handle_way = #{handleWay},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="dataSource != null and dataSource != ''">data_source = #{dataSource},</if>
            <if test="dataCrossBorder != null">data_cross_border = #{dataCrossBorder},</if>
            <if test="relateSystem != null">relate_system = #{relateSystem},</if>
            <if test="masterPerson != null">master_person = #{masterPerson},</if>
            <if test="masterDept != null">master_dept = #{masterDept},</if>
            <if test="isEvaluate != null">is_evaluate = #{isEvaluate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemProcessingActivityById" parameterType="Long">
        delete from dsa_data_item_processing_activity where id = #{id}
    </delete>

    <delete id="deleteDataItemProcessingActivityByIds" parameterType="String">
        delete from dsa_data_item_processing_activity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>