package com.ruoyi.dataItem.mapper;

import com.ruoyi.dataItem.domain.DataItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据项管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface DataItemMapper extends BaseMapper<DataItem> {
    int physicalDeleteByOrgId(Long orgId);

    List<DataItem> selectAllWithDeletedByOrgId(Long orgId);
}
