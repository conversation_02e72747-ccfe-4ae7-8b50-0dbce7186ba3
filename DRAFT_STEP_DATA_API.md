# 评估计划任务暂存功能 API 文档

## 功能概述

在 `EvaluatePlanTaskController` 中新增了一个暂存功能 `draftCurrentStepData`，该功能与现有的 `saveCurrentStepData` 方法功能基本相同，但采用更宽松的校验策略，适用于数据暂存场景。

## API 接口

### 暂存当前步骤数据

**接口地址：** `POST /evaluatePlan/task/draftStepData`

**接口描述：** 暂存当前步骤的数据，不进行严格校验

**请求参数：**

请求体为 `SaveStepRequest` 对象：

```json
{
    "planId": 123,
    "step": "step1",
    "stepData": {
        // 步骤相关的数据对象
    },
    "dataDescription": "基本信息填写"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| planId | Long | 是 | 评估计划ID |
| step | String | 是 | 步骤名称 |
| stepData | Object | 否 | 步骤数据内容（暂存时允许为空） |
| dataDescription | String | 否 | 数据描述 |

**请求示例：**
```http
POST /evaluatePlan/task/draftStepData
Content-Type: application/json

{
    "planId": 123,
    "step": "basicInfo",
    "stepData": {
        "companyName": "测试公司",
        "contactPerson": "张三"
    },
    "dataDescription": "基本信息暂存"
}
```

**响应格式：**

成功响应：
```json
{
    "code": 200,
    "msg": "暂存步骤数据成功",
    "data": null
}
```

失败响应：
```json
{
    "code": 500,
    "msg": "评估计划ID不能为空",
    "data": null
}
```

**权限要求：** `evaluatePlan:task:edit`

## 与 saveCurrentStepData 的区别

### 相同点
1. **接口参数**：使用相同的 `SaveStepRequest` 参数结构
2. **基本流程**：都会调用步骤数据处理器进行数据保存
3. **事务处理**：都具有事务性，保证数据一致性
4. **权限控制**：使用相同的权限要求

### 不同点

| 方面 | saveCurrentStepData | draftCurrentStepData |
|------|---------------------|---------------------|
| **校验严格程度** | 严格校验所有参数 | 宽松校验，只校验基本必要参数 |
| **数据完整性要求** | stepData 不能为空 | stepData 允许为空 |
| **任务存在性** | 必须存在评估计划任务 | 任务不存在时记录警告但继续执行 |
| **流程实例** | 必须存在流程实例 | 流程实例不存在时记录警告但继续执行 |
| **评估类型** | 评估类型不能为空 | 评估类型为空时记录警告但继续执行 |
| **处理器失败** | 处理器失败时抛出异常 | 处理器失败时记录警告但继续执行 |
| **使用场景** | 正式保存，要求数据完整 | 临时暂存，允许数据不完整 |

## 实现特点

### 1. 宽松校验策略
```java
private void validateDraftStepDataRequest(SaveStepRequest draftSaveRequest) {
    if (draftSaveRequest == null) {
        throw new ServiceException("暂存步骤数据请求参数不能为空");
    }
    // 对于暂存，只校验基本的必要参数
    if (draftSaveRequest.getPlanId() == null) {
        throw new ServiceException("评估计划ID不能为空");
    }
    if (StringUtils.isEmpty(draftSaveRequest.getStep())) {
        throw new ServiceException("步骤名称不能为空");
    }
    // 暂存时不强制要求stepData不为空，允许保存空数据
}
```

### 2. 容错处理
- **任务不存在**：记录警告日志，但不抛出异常
- **流程实例不存在**：记录警告日志，但不抛出异常
- **评估类型为空**：记录警告日志，但不抛出异常
- **处理器操作失败**：记录警告日志，但不抛出异常

### 3. 日志记录
- 详细记录各种异常情况的警告日志
- 记录暂存操作的成功日志
- 便于问题排查和数据追踪

## 使用场景

1. **表单自动保存**：用户填写表单过程中的自动暂存
2. **草稿保存**：用户主动保存草稿数据
3. **临时数据保存**：在数据不完整时的临时保存
4. **断点续传**：支持用户中断后继续填写

## 注意事项

1. **数据完整性**：暂存的数据可能不完整，使用时需要注意
2. **权限控制**：需要相应的编辑权限才能执行暂存操作
3. **事务处理**：暂存操作具有事务性
4. **日志监控**：建议监控暂存操作的警告日志，及时发现潜在问题

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 500 | 暂存步骤数据请求参数不能为空 | 请求参数为null |
| 500 | 评估计划ID不能为空 | planId参数缺失 |
| 500 | 步骤名称不能为空 | step参数缺失或为空 |
| 500 | 暂存步骤数据失败: [具体错误信息] | 其他系统异常 |
