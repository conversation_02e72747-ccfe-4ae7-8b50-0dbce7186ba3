# 流程控制数据创建功能总结

## 功能概述

在创建评估计划任务后，系统会自动创建相关的流程控制数据，包括流程实例和步骤实例，实现评估任务与流程管理的无缝集成。

## 新增文件

### 1. IProcessFlowService.java
**位置**: `ruoyi-assessment/src/main/java/com/ruoyi/process/service/IProcessFlowService.java`

**功能**: 流程流转服务接口，定义了流程实例和步骤实例的创建、管理方法

**主要方法**:
- `createProcessInstance()` - 创建流程实例
- `createStepInstances()` - 创建步骤实例
- `startProcessInstance()` - 启动流程实例（创建流程实例和步骤实例）
- `getProcessProgress()` - 获取流程进度
- `updateStepInstanceStatus()` - 更新步骤状态
- `executeNextStep()` - 执行下一步骤

### 2. ProcessFlowServiceImpl.java
**位置**: `ruoyi-assessment/src/main/java/com/ruoyi/process/service/impl/ProcessFlowServiceImpl.java`

**功能**: 流程流转服务实现类，负责具体的流程管理逻辑

**核心特性**:
- 事务管理：使用 `@Transactional` 确保数据一致性
- 异常处理：完善的异常处理和日志记录
- 重复检查：避免重复创建流程实例和步骤实例
- 状态管理：自动管理步骤的开始时间、结束时间和执行时长

## 修改文件

### EvaluatePlanTaskServiceImpl.java
**位置**: `ruoyi-assessment/src/main/java/com/ruoyi/evaluate/evaluatePlan/service/impl/EvaluatePlanTaskServiceImpl.java`

**修改内容**:
1. 新增 `IProcessFlowService` 依赖注入
2. 在 `createPlanTask` 方法中添加流程控制数据创建逻辑

## 流程控制数据创建流程

### 1. 数据表关系
```
dsa_evaluate_plan_task (评估计划任务)
    ↓ (business_id)
dsa_process_instance (流程实例)
    ↓ (process_instance_id)
dsa_process_step_instance (步骤实例)
```

### 2. 创建流程
1. **保存评估计划任务** - 调用 `this.save(evaluatePlanTask)`
2. **验证保存结果** - 检查任务是否保存成功
3. **创建流程实例** - 调用 `processFlowService.startProcessInstance()`
   - 根据 `evaluateType` 查找流程定义
   - 创建流程实例，设置 `business_id` 为任务ID
   - 根据流程定义创建所有步骤实例
   - 初始化步骤状态为"待执行"

### 3. 关键代码片段
```java
// 创建流程控制数据
if (StringUtils.isNotEmpty(evaluateType)) {
    try {
        Long processInstanceId = processFlowService.startProcessInstance(
                evaluateType, 
                evaluatePlanTask.getId(), 
                evaluatePlanTask.getCreateBy());
        
        log.info("创建流程控制数据成功，任务ID: {}, 流程实例ID: {}, evaluateType: {}", 
                evaluatePlanTask.getId(), processInstanceId, evaluateType);
                
    } catch (Exception e) {
        log.error("创建流程控制数据异常，任务ID: {}, evaluateType: {}, 异常: {}", 
                evaluatePlanTask.getId(), evaluateType, e.getMessage(), e);
        // 流程控制数据创建失败不影响任务保存，只记录错误日志
    }
}
```

## 核心特性

### 1. 事务安全
- 流程实例和步骤实例的创建使用事务管理
- 确保数据的一致性和完整性
- 异常时自动回滚

### 2. 容错设计
- 流程控制数据创建失败不影响任务保存
- 详细的异常日志记录
- 重复创建检查，避免数据冲突

### 3. 自动化管理
- 根据流程定义自动创建所有步骤实例
- 自动设置步骤顺序和初始状态
- 支持流程进度计算

### 4. 灵活配置
- 支持不同评估类型对应不同流程定义
- 步骤配置可通过数据库动态调整
- 支持步骤的启用/禁用控制

## 数据流转示例

### 创建评估计划任务时的数据流转：

1. **输入**: EvaluatePlanTaskDto
2. **处理**: 
   - 根据 modelId 获取 evaluateType
   - 验证评估策略
   - 保存评估计划任务
3. **输出**: 
   - dsa_evaluate_plan_task 记录
   - dsa_process_instance 记录
   - dsa_process_step_instance 记录（多条）

### 数据示例：
```sql
-- 评估计划任务
INSERT INTO dsa_evaluate_plan_task (id, evaluate_type, model_id, name, ...) 
VALUES (1001, 'data_security_plan', 1, '某公司数据安全评估', ...);

-- 流程实例
INSERT INTO dsa_process_instance (id, process_id, business_id, status, ...) 
VALUES (2001, 1, 1001, 1, ...);

-- 步骤实例
INSERT INTO dsa_process_step_instance (id, plan_task_id, process_instance_id, step_definition_id, step_name, status, ...) 
VALUES 
(3001, 1001, 2001, 1, '风险识别', 0, ...),
(3002, 1001, 2001, 2, '风险分析', 0, ...),
(3003, 1001, 2001, 3, '风险评估', 0, ...);
```

## 使用场景

1. **评估任务创建** - 自动创建对应的流程控制数据
2. **流程跟踪** - 通过流程实例跟踪评估进度
3. **步骤管理** - 管理评估过程中的各个步骤
4. **进度监控** - 实时计算和显示评估进度
5. **流程回退** - 支持评估流程的回退操作

## 注意事项

1. **数据一致性** - 确保流程定义数据的完整性
2. **性能考虑** - 批量创建步骤实例时注意性能
3. **异常处理** - 流程创建失败不应影响主业务
4. **日志记录** - 详细记录流程创建过程便于排查问题
5. **权限控制** - 确保操作人员有相应的权限

## 扩展性

该设计支持以下扩展：
1. **新增评估类型** - 通过配置流程定义支持新的评估类型
2. **自定义步骤** - 通过步骤定义配置自定义评估步骤
3. **流程监听** - 可添加流程事件监听器
4. **状态通知** - 可集成消息通知机制
5. **报表统计** - 基于流程数据生成统计报表
