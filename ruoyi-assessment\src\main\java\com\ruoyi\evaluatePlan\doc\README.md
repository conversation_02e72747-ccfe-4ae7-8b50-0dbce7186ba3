# 风险评估分发系统

## 概述

本系统采用策略模式设计，实现了一个灵活的风险评估分发框架，支持多种不同类型的评估流程。
**增强功能**：支持在某种类型的某个评估计划任务下执行不同的任务内容，提供任务内容参数化和步骤级别的精细控制。

## 架构设计

### 核心组件

1. **EvaluateStrategy** - 评估策略接口
   - 定义了所有评估策略必须实现的方法
   - 包括参数验证、执行评估、生成报告、获取进度等

2. **ProcessAwareEvaluateStrategy** - 流程感知评估策略接口
   - 扩展基础策略接口，增加流程管理功能
   - 支持步骤级别执行、依赖管理、回退处理等

3. **EvaluatePlanDispatcher** - 评估计划分发器
   - 负责根据评估类型分发到对应的策略处理器
   - 自动注册所有策略实现
   - 提供统一的调用入口
   - **新增**：支持任务内容执行和步骤级别控制

4. **EvaluatePlanRequest** - 评估请求对象
   - 封装评估所需的所有参数
   - 支持扩展参数

5. **EvaluateTaskRequest** - 任务内容执行请求对象
   - 支持任务内容参数化配置
   - 支持不同任务类型的执行

6. **EvaluateStepRequest** - 步骤任务执行请求对象
   - 支持步骤级别的精细控制
   - 支持流程实例管理

7. **EvaluatePlanResponse** - 评估响应对象
   - 封装评估结果和执行状态
   - 包含详细的执行步骤信息

## 支持的评估类型

目前已实现的评估类型：

1. **数据安全风险评估方案** (`data_security_plan`)
   - 数据资产识别与分类
   - 数据安全风险识别
   - 风险等级评定
   - 安全控制措施建议
   - **增强功能**：
     - 支持任务内容参数化执行
     - 支持步骤级别精细控制
     - 支持流程回退和依赖管理
     - 支持任务类型区分

## 支持的所有评估类型

| 流程标识 | 评估类型名称 | 实现状态 | 支持功能 |
|---------|-------------|----------|----------|
| `data_security_plan` | 数据安全风险评估方案 | ✅ 已实现 | 基础评估流程 + 任务内容定制 + 步骤级控制 |
| `basic_info_survey` | 基本信息调研表 | ⏳ 待实现 | - |
| `compliance_assessment` | 企业管理咨询合规性评估 | ⏳ 待实现 | - |
| `data_risk_level` | 重要/核心/一般数据风险评估 | ⏳ 待实现 | - |
| `enterprise_risk` | 企业整体风险评估 | ⏳ 待实现 | - |

## 使用方法

### 1. 执行完整评估

```java
// 创建评估请求
EvaluatePlanRequest request = new EvaluatePlanRequest()
    .setPlanId(1001L)
    .setEvaluateType("data_security_plan")
    .setTitle("某公司数据安全风险评估")
    .setTargetCompanyId(1L)
    .setEvaluateCompanyId(1L);

// 执行评估
EvaluatePlanResponse response = evaluatePlanDispatcher.dispatch(request);
```

### 2. 执行指定任务内容

```java
// 创建任务执行请求
EvaluateTaskRequest taskRequest = new EvaluateTaskRequest()
    .setPlanId(1001L)
    .setEvaluateType("data_security_plan")
    .setTaskType("data_collection")
    .setTaskParams(Map.of("dataScope", "sensitive_only", "includeBackup", true));

// 执行任务
EvaluatePlanResponse response = evaluatePlanDispatcher.executeTask(taskRequest);
```

### 3. 执行指定步骤

```java
// 创建步骤执行请求
EvaluateStepRequest stepRequest = new EvaluateStepRequest()
    .setPlanId(1001L)
    .setEvaluateType("data_security_plan")
    .setStepCode("risk_identify")
    .setProcessInstanceId(2001L)
    .setStepParams(Map.of("analysisDepth", "detailed"));

// 执行步骤
EvaluatePlanResponse response = evaluatePlanDispatcher.executeStep(stepRequest);
```

### 4. 获取评估进度

```java
Integer progress = evaluatePlanDispatcher.getProgress("data_security_plan", 1001L);
```

### 5. 生成评估报告

```java
String report = evaluatePlanDispatcher.generateReport(request);
```

### 6. 获取任务列表

```java
Map<String, Object> taskList = evaluatePlanDispatcher.getAvailableTasks("data_security_plan", 1001L);
```

### 7. 获取步骤状态

```java
Map<String, Object> stepStatus = evaluatePlanDispatcher.getStepStatus("data_security_plan", 1001L, "risk_identify");
```

### 8. REST API 调用

#### 执行评估计划
```
POST /evaluatePlan/execute
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "enhanced_data_security_plan",
    "title": "某公司数据安全风险评估",
    "targetCompanyId": 1,
    "evaluateCompanyId": 1
}
```

#### 执行指定任务内容
```
POST /evaluatePlan/executeTask
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "enhanced_data_security_plan",
    "taskType": "data_collection",
    "taskParams": {
        "dataScope": "sensitive_only",
        "includeBackup": true
    }
}
```

#### 执行指定步骤任务
```
POST /evaluatePlan/executeStep
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "enhanced_data_security_plan",
    "stepCode": "risk_identify",
    "processInstanceId": 2001,
    "stepParams": {
        "analysisDepth": "detailed"
    }
}
```

#### 获取任务内容列表
```
GET /evaluatePlan/taskList?evaluateType=enhanced_data_security_plan&planId=1001
```

#### 获取步骤任务状态
```
GET /evaluatePlan/stepStatus?evaluateType=enhanced_data_security_plan&planId=1001&stepCode=risk_identify
```

#### 获取评估进度
```
GET /evaluatePlan/progress?evaluateType=enhanced_data_security_plan&planId=1001
```

#### 生成评估报告
```
POST /evaluatePlan/report
Content-Type: application/json

{
    "planId": 1001,
    "evaluateType": "enhanced_data_security_plan",
    "title": "某公司数据安全风险评估"
}
```

#### 获取支持的评估类型
```
GET /evaluatePlan/types
```

## 扩展新的评估类型

要添加新的评估类型，只需要：

1. 实现 `EvaluateStrategy` 接口
2. 使用 `@Component` 注解标记为Spring组件
3. 系统会自动注册新的策略

示例：

```java
@Component
public class BasicInfoSurveyStrategy implements EvaluateStrategy {
    
    @Override
    public String getEvaluateType() {
        return "basic_info_survey";
    }
    
    @Override
    public String getStrategyName() {
        return "基本信息调研表";
    }
    
    // 实现其他方法...
}
```

## 设计模式应用

1. **策略模式** - 不同的评估类型使用不同的策略实现
2. **工厂模式** - 分发器根据类型创建对应的策略实例
3. **模板方法模式** - 评估流程的通用步骤在接口中定义

## 优势

1. **可扩展性** - 新增评估类型无需修改现有代码
2. **可维护性** - 每种评估类型的逻辑独立封装
3. **可测试性** - 每个策略可以独立测试
4. **统一接口** - 所有评估类型使用相同的调用方式

## 注意事项

1. 确保每个策略的 `getEvaluateType()` 返回唯一的类型编码
2. 策略实现类必须使用 `@Component` 注解
3. 参数验证要充分，避免运行时异常
4. 异常处理要完善，确保系统稳定性
