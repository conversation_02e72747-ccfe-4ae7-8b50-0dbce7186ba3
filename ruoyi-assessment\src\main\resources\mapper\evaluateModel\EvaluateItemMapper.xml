<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluateModel.mapper.EvaluateItemMapper">
    
    <resultMap type="EvaluateItem" id="EvaluateItemResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="itemType"    column="item_type"    />
        <result property="isRequired"    column="is_required"    />
        <result property="itemContent"    column="item_content"    />
        <result property="requirement"    column="requirement"    />
        <result property="guidance"    column="guidance"    />
        <result property="docFull"    column="doc_full"    />
        <result property="docPartial"    column="doc_partial"    />
        <result property="docFail"    column="doc_fail"    />
        <result property="docNotSuit"    column="doc_not_suit"    />
        <result property="docAdvice"    column="doc_advice"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateItemVo">
        select id, model_id, category_id, item_type, is_required, item_content, requirement, guidance, doc_full, doc_partial, doc_fail, doc_not_suit, doc_advice, sort, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_item
    </sql>

    <select id="selectEvaluateItemList" parameterType="EvaluateItem" resultMap="EvaluateItemResult">
        <include refid="selectEvaluateItemVo"/>
        <where>  
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="itemType != null  and itemType != ''"> and item_type = #{itemType}</if>
            <if test="isRequired != null "> and is_required = #{isRequired}</if>
            <if test="itemContent != null  and itemContent != ''"> and item_content = #{itemContent}</if>
            <if test="requirement != null  and requirement != ''"> and requirement = #{requirement}</if>
            <if test="guidance != null  and guidance != ''"> and guidance = #{guidance}</if>
            <if test="docFull != null  and docFull != ''"> and doc_full = #{docFull}</if>
            <if test="docPartial != null  and docPartial != ''"> and doc_partial = #{docPartial}</if>
            <if test="docFail != null  and docFail != ''"> and doc_fail = #{docFail}</if>
            <if test="docNotSuit != null  and docNotSuit != ''"> and doc_not_suit = #{docNotSuit}</if>
            <if test="docAdvice != null  and docAdvice != ''"> and doc_advice = #{docAdvice}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateItemById" parameterType="Long" resultMap="EvaluateItemResult">
        <include refid="selectEvaluateItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateItem" parameterType="EvaluateItem" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelId != null">model_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="itemType != null">item_type,</if>
            <if test="isRequired != null">is_required,</if>
            <if test="itemContent != null">item_content,</if>
            <if test="requirement != null">requirement,</if>
            <if test="guidance != null">guidance,</if>
            <if test="docFull != null">doc_full,</if>
            <if test="docPartial != null">doc_partial,</if>
            <if test="docFail != null">doc_fail,</if>
            <if test="docNotSuit != null">doc_not_suit,</if>
            <if test="docAdvice != null">doc_advice,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelId != null">#{modelId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="isRequired != null">#{isRequired},</if>
            <if test="itemContent != null">#{itemContent},</if>
            <if test="requirement != null">#{requirement},</if>
            <if test="guidance != null">#{guidance},</if>
            <if test="docFull != null">#{docFull},</if>
            <if test="docPartial != null">#{docPartial},</if>
            <if test="docFail != null">#{docFail},</if>
            <if test="docNotSuit != null">#{docNotSuit},</if>
            <if test="docAdvice != null">#{docAdvice},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateItem" parameterType="EvaluateItem">
        update dsa_evaluate_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="isRequired != null">is_required = #{isRequired},</if>
            <if test="itemContent != null">item_content = #{itemContent},</if>
            <if test="requirement != null">requirement = #{requirement},</if>
            <if test="guidance != null">guidance = #{guidance},</if>
            <if test="element != null">element = #{element},</if>
            <if test="method != null">method = #{method},</if>
            <if test="docFull != null">doc_full = #{docFull},</if>
            <if test="docPartial != null">doc_partial = #{docPartial},</if>
            <if test="docFail != null">doc_fail = #{docFail},</if>
            <if test="docNotSuit != null">doc_not_suit = #{docNotSuit},</if>
            <if test="docAdvice != null">doc_advice = #{docAdvice},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateItemById" parameterType="Long">
        delete from dsa_evaluate_item where id = #{id}
    </delete>

    <delete id="deleteEvaluateItemByIds" parameterType="String">
        delete from dsa_evaluate_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>