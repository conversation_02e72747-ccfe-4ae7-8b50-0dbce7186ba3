<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataSystemMemberMapper">
    
    <resultMap type="DataSystemMember" id="DataSystemMemberResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="dataSystemId"    column="data_system_id"    />
        <result property="dataItemId"    column="data_item_id"    />
        <result property="name"    column="name"    />
        <result property="job"    column="job"    />
        <result property="duty"    column="duty"    />
        <result property="dept"    column="dept"    />
        <result property="processingActivity"    column="processing_activity"    />
        <result property="dataProcessActivity"    column="data_process_activity"    />
        <result property="fullTime"    column="full_time"    />
        <result property="nationality"    column="nationality"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataSystemMemberVo">
        select id, org_id, data_system_id, data_item_id, name, job, duty, dept, processing_activity, data_process_activity, full_time, nationality, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_data_system_member
    </sql>

    <select id="selectDataSystemMemberList" parameterType="DataSystemMember" resultMap="DataSystemMemberResult">
        <include refid="selectDataSystemMemberVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="dataSystemId != null "> and data_system_id = #{dataSystemId}</if>
            <if test="dataItemId != null "> and data_item_id = #{dataItemId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="duty != null  and duty != ''"> and duty = #{duty}</if>
            <if test="dept != null  and dept != ''"> and dept = #{dept}</if>
            <if test="processingActivity != null  and processingActivity != ''"> and processing_activity = #{processingActivity}</if>
            <if test="dataProcessActivity != null  and dataProcessActivity != ''"> and data_process_activity = #{dataProcessActivity}</if>
            <if test="fullTime != null  and fullTime != ''"> and full_time = #{fullTime}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDataSystemMemberById" parameterType="Long" resultMap="DataSystemMemberResult">
        <include refid="selectDataSystemMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataSystemMember" parameterType="DataSystemMember" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_system_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="dataSystemId != null">data_system_id,</if>
            <if test="dataItemId != null">data_item_id,</if>
            <if test="name != null">name,</if>
            <if test="job != null">job,</if>
            <if test="duty != null">duty,</if>
            <if test="dept != null">dept,</if>
            <if test="processingActivity != null">processing_activity,</if>
            <if test="dataProcessActivity != null">data_process_activity,</if>
            <if test="fullTime != null">full_time,</if>
            <if test="nationality != null">nationality,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="dataSystemId != null">#{dataSystemId},</if>
            <if test="dataItemId != null">#{dataItemId},</if>
            <if test="name != null">#{name},</if>
            <if test="job != null">#{job},</if>
            <if test="duty != null">#{duty},</if>
            <if test="dept != null">#{dept},</if>
            <if test="processingActivity != null">#{processingActivity},</if>
            <if test="dataProcessActivity != null">#{dataProcessActivity},</if>
            <if test="fullTime != null">#{fullTime},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataSystemMember" parameterType="DataSystemMember">
        update dsa_data_system_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="dataSystemId != null">data_system_id = #{dataSystemId},</if>
            <if test="dataItemId != null">data_item_id = #{dataItemId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="job != null">job = #{job},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="dept != null">dept = #{dept},</if>
            <if test="processingActivity != null">processing_activity = #{processingActivity},</if>
            <if test="dataProcessActivity != null">data_process_activity = #{dataProcessActivity},</if>
            <if test="fullTime != null">full_time = #{fullTime},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSystemMemberById" parameterType="Long">
        delete from dsa_data_system_member where id = #{id}
    </delete>

    <delete id="deleteDataSystemMemberByIds" parameterType="String">
        delete from dsa_data_system_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>