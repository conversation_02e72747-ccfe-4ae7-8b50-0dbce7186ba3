# 处理器架构使用示例

## 概述

`ProcessorArchitectureTest` 类提供了完整的处理器架构测试和监控功能，可以帮助开发者验证处理器是否正常工作。

## 使用方式

### 1. 自动测试（推荐）

系统启动时会自动执行测试：

```java
@Component
public class ProcessorArchitectureTest {
    
    @PostConstruct
    public void testProcessorArchitecture() {
        // 系统启动时自动执行
        log.info("=== 开始处理器架构测试 ===");
        // ... 测试逻辑
    }
}
```

### 2. 手动触发测试

在需要时手动触发测试：

```java
@Autowired
private ProcessorArchitectureTest architectureTest;

public void checkProcessorStatus() {
    // 手动触发测试
    architectureTest.runManualTest();
}
```

### 3. 获取架构状态报告

获取详细的架构状态报告：

```java
@Autowired
private ProcessorArchitectureTest architectureTest;

public void printArchitectureReport() {
    String report = architectureTest.getArchitectureReport();
    System.out.println(report);
}
```

## 测试内容

### 1. 处理器注册状态测试

验证所有处理器是否正确注册：

```
--- 测试处理器注册状态 ---
注册表状态: {totalProcessors=8, specificProcessors=2, commonProcessors=0, hasDefaultProcessor=true}
总处理器数: 8
是否有默认处理器: true
✅ 处理器注册正常
```

### 2. 处理器匹配逻辑测试

测试不同场景下的处理器匹配：

```
--- 测试处理器匹配逻辑 ---
✅ 数据安全-创建计划 - 找到处理器: CreatePlanProcessor
✅ 数据安全-评估范围 - 找到处理器: EvaluateScopeProcessor
✅ 数据安全-系统检查 - 找到处理器: SystemCheckProcessor
✅ 基础信息-现状分析 - 找到处理器: AnalysisProcessor
✅ 未知类型-未知步骤 - 找到处理器: DefaultStepDataProcessor
```

### 3. 支持的评估类型测试

查看系统支持的评估类型和步骤：

```
--- 测试支持的评估类型 ---
支持的评估类型: [data_security_plan, basic_info]
评估类型 data_security_plan 支持的步骤: [create_plan, evaluate_scope, system_check, sign, generate_report, evaluate_result]
评估类型 basic_info 支持的步骤: [current_analysis]
✅ 评估类型支持正常
```

## 架构状态报告示例

```
=== 处理器架构状态报告 ===

📊 注册表状态:
  总处理器数: 8
  特定处理器: 2
  通用处理器: 0
  默认处理器: 已配置

🎯 支持的评估类型:
  data_security_plan: [create_plan, evaluate_scope, system_check, sign, generate_report, evaluate_result]
  basic_info: [current_analysis]
```

## 在控制器中使用

可以创建一个管理接口来查看处理器状态：

```java
@RestController
@RequestMapping("/admin/processor")
public class ProcessorManagementController {
    
    @Autowired
    private ProcessorArchitectureTest architectureTest;
    
    @GetMapping("/status")
    public AjaxResult getProcessorStatus() {
        try {
            String report = architectureTest.getArchitectureReport();
            return AjaxResult.success("获取处理器状态成功", report);
        } catch (Exception e) {
            return AjaxResult.error("获取处理器状态失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/test")
    public AjaxResult runProcessorTest() {
        try {
            architectureTest.runManualTest();
            return AjaxResult.success("处理器测试执行完成，请查看日志");
        } catch (Exception e) {
            return AjaxResult.error("处理器测试执行失败: " + e.getMessage());
        }
    }
}
```

## 故障排查

### 常见问题

1. **未发现任何处理器**
   - 检查处理器类是否添加了 `@Component` 注解
   - 确认处理器类在Spring扫描路径下
   - 查看启动日志中的注册信息

2. **处理器匹配失败**
   - 检查 `getStepCode()` 和 `getEvaluateType()` 返回值
   - 确认处理器继承了 `AbstractStepDataProcessor`
   - 验证包路径是否正确

3. **默认处理器未配置**
   - 确认 `DefaultStepDataProcessor` 类存在
   - 检查该类的 `getStepCode()` 和 `getEvaluateType()` 都返回 `null`

### 调试技巧

1. **启用调试日志**：
   ```properties
   logging.level.com.ruoyi.evaluate.evaluatePlan.service.processor=DEBUG
   ```

2. **查看详细注册信息**：
   ```java
   Map<String, Object> status = processorRegistry.getRegistryStatus();
   log.info("详细状态: {}", status);
   ```

3. **手动测试特定处理器**：
   ```java
   IStepDataProcessor processor = processorFactory.getProcessor("data_security_plan", "create_plan");
   log.info("找到处理器: {}", processor.getClass().getSimpleName());
   ```

## 最佳实践

1. **定期检查**：在系统部署后定期检查处理器状态
2. **监控日志**：关注处理器注册和匹配的日志信息
3. **自动化测试**：将处理器测试集成到CI/CD流程中
4. **文档更新**：新增处理器时及时更新相关文档
