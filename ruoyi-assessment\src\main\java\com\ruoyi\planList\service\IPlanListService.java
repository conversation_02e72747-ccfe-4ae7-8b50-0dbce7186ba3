package com.ruoyi.planList.service;

import com.ruoyi.planList.domain.PlanList;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.planList.domain.excel.ExcelPlanList;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import com.ruoyi.common.core.domain.AjaxResult;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 评估计划清单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IPlanListService extends IService<PlanList> {

    int batchImport(List<PlanList> planList);

    Map<String, Object> importPlanList(MultipartFile file, Long orgId) throws IOException;

    PlanList convertToPlanList(ExcelPlanList excel, Long orgId);

    void exportPlanListWithTemplate(HttpServletResponse response, PlanList planList, String templatePath);

    /**
     * 统计每个versionId下PlanList数量
     */
    List<Map<String, Object>> countPlanListGroupByVersionId();
}
