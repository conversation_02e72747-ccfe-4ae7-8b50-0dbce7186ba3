package com.ruoyi.dataItem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据处理活动对象 dsa_data_item_processing_activity
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_item_processing_activity")
public class DataItemProcessingActivity extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /**  */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 信息系统id */
    @Excel(name = "信息系统id")
    @TableField(value = "data_system_id")
    private Long dataSystemId;

    /** 数据项id */
    @NotNull(message = "数据项ID（dataItemId）不能为空", groups = {AddGroup.class, ListGroup.class})
    @Excel(name = "数据项id")
    @TableField(value = "data_item_id")
    private Long dataItemId;

    /** 数据处理活动编号 */
    @Excel(name = "数据处理活动编号")
    @TableField(value = "activity_no")
    private String activityNo;

    /** 数据处理活动 */
    @Excel(name = "数据处理活动")
    @TableField(value = "processing_activity_name")
    private String processingActivityName;

    /** 数据处理目的 */
    @Excel(name = "数据处理目的")
    @TableField(value = "purpose")
    private String purpose;

    /** 数据处理方式 */
    @Excel(name = "数据处理方式")
    @TableField(value = "handle_way")
    private String handleWay;

    /** 数据处理频率 */
    @Excel(name = "数据处理频率")
    @TableField(value = "frequency")
    private String frequency;

    /** 数据来源 */
    @Excel(name = "数据来源")
    @TableField(value = "data_source")
    private String dataSource;

    /** 是否涉及数据出境 */
    @Excel(name = "是否涉及数据出境")
    @TableField(value = "data_cross_border")
    private String dataCrossBorder;

    /** 涉及信息系统名称（数据载体） */
    @Excel(name = "涉及信息系统名称")
    @TableField(value = "relate_system")
    private String relateSystem;

    /** 负责人 */
    @Excel(name = "负责人")
    @TableField(value = "master_person")
    private String masterPerson;

    /** 责任部门 */
    @Excel(name = "责任部门")
    @TableField(value = "master_dept")
    private String masterDept;

    /** 是否评估 */
    @Excel(name = "是否评估")
    @TableField(value = "is_evaluate")
    private String isEvaluate;
}