# 评估计划流程管理测试数据说明

## 📋 概述

本文档说明了评估计划流程管理系统的测试数据结构和使用方法。测试数据包含了完整的流程定义、步骤配置、实例数据等，用于验证系统的各项功能。

## 🗃️ 数据表结构

### 1. 流程定义表 (dsa_process_definition)
存储评估流程的基本定义信息
- **主要字段**: name, code, description, status
- **数据量**: 5条记录（对应5种评估类型）

### 2. 流程步骤定义表 (dsa_process_step_definition)
存储每个流程包含的步骤定义
- **主要字段**: process_id, step_order, step_name, step_code, handler_type
- **数据量**: 30条记录（每个流程5-6个步骤）

### 3. 流程实例表 (dsa_process_instance)
存储具体的流程执行实例
- **主要字段**: process_id, business_id, status, current_step_id
- **数据量**: 5条记录（每种流程类型一个实例）

### 4. 流程步骤实例表 (dsa_process_step_instance)
存储流程实例中每个步骤的执行状态
- **主要字段**: process_instance_id, step_definition_id, status, execute_time
- **数据量**: 30条记录（对应所有步骤实例）

### 5. 流程回退记录表 (dsa_process_step_rollback)
记录流程回退操作的历史
- **主要字段**: process_instance_id, from_step_instance_id, to_step_definition_id, rollback_reason
- **数据量**: 1条记录（演示回退功能）

## 🎯 评估类型与流程映射

| 流程编码 | 评估类型名称 | 步骤数量 | 特点 |
|---------|-------------|----------|------|
| `data_security_plan` | 数据安全风险评估方案 | 6步 | 包含自动风险识别和评定 |
| `basic_info_survey` | 基本信息调研表 | 5步 | 重点在信息收集和验证 |
| `compliance_assessment` | 企业管理咨询合规性评估 | 6步 | 强调合规要求和差距分析 |
| `data_risk_level` | 重要/核心/一般数据风险评估 | 5步 | 数据分级和差异化保护 |
| `enterprise_risk` | 企业整体风险评估 | 6步 | 全面的企业风险管理 |

## 🔄 流程步骤类型

### 手动步骤 (handler_type = 'manual')
需要人工操作的步骤，如：
- 数据资产识别
- 调研表发放
- 合规要求梳理
- 控制措施建议

### 自动步骤 (handler_type = 'auto')
系统自动执行的步骤，如：
- 风险识别分析
- 风险等级评定
- 报告生成
- 信息整理

## 📊 测试场景

### 1. 基础流程测试
```sql
-- 查看所有流程定义
SELECT * FROM dsa_process_definition WHERE status = 1;

-- 查看数据安全评估流程的步骤
SELECT psd.* FROM dsa_process_step_definition psd
JOIN dsa_process_definition pd ON psd.process_id = pd.id
WHERE pd.code = 'data_security_plan'
ORDER BY psd.step_order;
```

### 2. 流程实例测试
```sql
-- 查看流程实例及其当前状态
SELECT pi.*, pd.name as process_name, 
       psd.step_name as current_step_name
FROM dsa_process_instance pi
JOIN dsa_process_definition pd ON pi.process_id = pd.id
LEFT JOIN dsa_process_step_definition psd ON pi.current_step_id = psd.id;

-- 检查流程实例状态
SELECT pi.id, pd.name, pi.business_id, pi.status 
FROM dsa_process_instance pi 
JOIN dsa_process_definition pd ON pi.process_id = pd.id;
```

### 3. 测试流程执行
```java
// 启动新的流程实例
Long processInstanceId = processFlowService.startProcessInstance("data_security_plan", 1006L, "admin");

// 执行下一步骤
boolean success = processFlowService.executeNextStep(processInstanceId, "evaluator1", "开始执行");

// 获取流程进度
Integer progress = processFlowService.getProcessProgress(processInstanceId);
```

### 4. 测试策略模式集成
```java
// 通过分发器执行评估
EvaluatePlanRequest request = new EvaluatePlanRequest()
    .setEvaluateType("data_security_plan")
    .setPlanId(1006L);

EvaluatePlanResponse response = evaluatePlanDispatcher.dispatch(request);
```

### 5. 测试增强功能
```java
// 测试任务内容执行
EvaluateTaskRequest taskRequest = new EvaluateTaskRequest()
    .setEvaluateType("enhanced_data_security_plan")
    .setTaskType("data_collection")
    .setPlanId(1006L);

EvaluatePlanResponse taskResponse = evaluatePlanDispatcher.executeTask(taskRequest);

// 测试步骤级别执行
EvaluateStepRequest stepRequest = new EvaluateStepRequest()
    .setEvaluateType("enhanced_data_security_plan")
    .setStepCode("risk_identify")
    .setProcessInstanceId(1001L)
    .setPlanId(1006L);

EvaluatePlanResponse stepResponse = evaluatePlanDispatcher.executeStep(stepRequest);
```

## 🔧 数据初始化

### 1. 清理现有数据
```sql
DELETE FROM dsa_process_step_rollback WHERE id > 0;
DELETE FROM dsa_process_step_instance WHERE id > 0;
DELETE FROM dsa_process_instance WHERE id > 0;
DELETE FROM dsa_process_step_definition WHERE id > 0;
DELETE FROM dsa_process_definition WHERE id > 0;
```

### 2. 插入基础数据
执行 `test_data.sql` 文件中的INSERT语句，包括：
- 5个流程定义
- 30个步骤定义
- 5个流程实例
- 30个步骤实例
- 1个回退记录

### 3. 验证数据完整性
```sql
-- 检查数据统计
SELECT 
    (SELECT COUNT(*) FROM dsa_process_definition) as process_count,
    (SELECT COUNT(*) FROM dsa_process_step_definition) as step_def_count,
    (SELECT COUNT(*) FROM dsa_process_instance) as instance_count,
    (SELECT COUNT(*) FROM dsa_process_step_instance) as step_instance_count;
```

## 🧪 测试用例

### 1. 流程完整性测试
- 验证每个流程定义都有对应的步骤
- 验证步骤顺序的连续性
- 验证步骤依赖关系

### 2. 实例状态测试
- 验证流程实例的状态转换
- 验证步骤实例的执行顺序
- 验证当前步骤指针的正确性

### 3. 回退功能测试
- 验证步骤回退的逻辑
- 验证回退后的状态恢复
- 验证回退记录的完整性

### 4. 策略集成测试
- 验证策略模式的正确分发
- 验证不同评估类型的执行
- 验证增强功能的兼容性

## 📝 注意事项

1. **数据依赖关系**：删除数据时需要按照外键依赖的逆序进行
2. **ID自增**：测试数据使用固定ID，生产环境应使用自增ID
3. **时间字段**：测试数据中的时间使用NOW()函数，确保时间的一致性
4. **状态值**：注意各种状态字段的取值范围和含义
5. **字符编码**：确保数据库和文件的字符编码一致，避免中文乱码

## 🚀 快速开始

1. 执行数据库脚本创建表结构
2. 运行 `test_data.sql` 插入测试数据
3. 启动应用程序
4. 使用API或Java代码进行功能测试
5. 查看数据库中的状态变化

通过这些测试数据，可以全面验证评估计划流程管理系统的各项功能，包括基础流程执行、增强任务内容执行、步骤级别控制等特性。
