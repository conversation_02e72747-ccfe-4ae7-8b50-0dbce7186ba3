package com.ruoyi.dataItem.service.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataItemBaseMapper;
import com.ruoyi.dataItem.domain.DataItemBase;
import com.ruoyi.dataItem.service.IDataItemBaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.dataItem.domain.DataItem;
import com.ruoyi.dataItem.service.IDataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.common.exception.ServiceException;

/**
 * 数据项基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class DataItemBaseServiceImpl extends ServiceImpl<DataItemBaseMapper, DataItemBase> implements IDataItemBaseService {

    @Autowired
    private IDataItemService dataItemService;

    @Override
    public boolean saveWithValidation(DataItemBase dataItemBase) {
        if (dataItemBase.getDataItemId() == null) {
            throw new ServiceException("数据项ID（dataItemId）不能为空");
        }
        DataItem dataItem = dataItemService.getById(dataItemBase.getDataItemId());
        if (dataItem == null) {
            throw new ServiceException("数据项ID不存在");
        }
        if (!dataItemBase.getOrgId().equals(dataItem.getOrgId())) {
            throw new ServiceException("数据项不属于当前单位，禁止操作");
        }
        return this.save(dataItemBase);
    }
}
