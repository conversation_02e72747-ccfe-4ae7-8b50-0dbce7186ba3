package com.ruoyi.evaluatePlan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

/**
 * 评估计划请求对象
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class EvaluatePlanRequest {

    /** 计划ID */
    private Long planId;

    /** 评估类型编码 */
    @NotBlank(message = "评估类型不能为空")
    private String evaluateType;

    /** 评估标题 */
    @NotBlank(message = "评估标题不能为空")
    private String title;

    /** 评估描述 */
    private String description;

    /** 被评估单位ID */
    @NotNull(message = "被评估单位不能为空")
    private Long targetCompanyId;

    /** 评估单位ID */
    @NotNull(message = "评估单位不能为空")
    private Long evaluateCompanyId;

    /** 评估模型ID */
    private Long modelId;

    /** 计划开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 计划结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 评估状态 0-待开始 1-进行中 2-已完成 3-已暂停 */
    private Integer status;

    /** 优先级 1-低 2-中 3-高 */
    private Integer priority;

    /** 扩展参数 */
    private Map<String, Object> extParams;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
