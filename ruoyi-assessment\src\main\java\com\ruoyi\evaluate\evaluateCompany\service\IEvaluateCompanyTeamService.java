package com.ruoyi.evaluate.evaluateCompany.service;

import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeam;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 评估团队Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IEvaluateCompanyTeamService extends IService<EvaluateCompanyTeam> {

    /**
     * 导入团队成员Excel文件
     *
     * @param teamName 团队名称
     * @param orgId 所属单位ID
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> importTeamMembers(String teamName, Long orgId, MultipartFile file) throws Exception;
}
