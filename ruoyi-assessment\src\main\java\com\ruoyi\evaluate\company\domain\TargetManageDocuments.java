package com.ruoyi.evaluate.company.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 被评估单位文档信息对象 dsa_target_manage_documents
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_target_manage_documents")
public class TargetManageDocuments extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 文档名称 */
    @Excel(name = "文档名称")
    @TableField(value = "document_name")
    private String documentName;

    /** 主要内容 */
    @Excel(name = "主要内容")
    @TableField(value = "content")
    private String content;

    /** 使用范围 */
    @Excel(name = "使用范围")
    @TableField(value = "scope")
    private String scope;

    /** 数据处理活动（多选，逗号分隔） */
    @Excel(name = "数据处理活动")
    @TableField(value = "process_activity_id")
    private String processActivityId;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;







}