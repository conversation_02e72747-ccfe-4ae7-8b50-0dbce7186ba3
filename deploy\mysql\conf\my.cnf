[mysqld]
# 基本设置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# SQL模式设置（MySQL 5.7兼容）
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# 连接设置
max_connections=1000
max_connect_errors=6000
open_files_limit=65535
table_open_cache=128
max_allowed_packet=4M
binlog_cache_size=1M
max_heap_table_size=8M
tmp_table_size=16M

# 查询缓存（MySQL 5.7支持）
query_cache_size=8M
query_cache_type=1
query_cache_limit=2M

# InnoDB设置（MySQL 5.7兼容）
innodb_buffer_pool_size=128M
innodb_data_file_path=ibdata1:10M:autoextend
innodb_thread_concurrency=8
innodb_flush_log_at_trx_commit=2
innodb_log_buffer_size=2M
innodb_log_file_size=4M
innodb_log_files_in_group=3
innodb_max_dirty_pages_pct=90
innodb_lock_wait_timeout=120

# 日志设置
log-error=/var/log/mysql/error.log
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=3

# 时区设置
default-time-zone='+8:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
