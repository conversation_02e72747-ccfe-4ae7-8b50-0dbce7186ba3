package com.ruoyi.evaluate.evaluatePlan.service.processor;

import java.util.*;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ruoyi.common.exception.ServiceException;

/**
 * 步骤数据处理器工厂类
 * <p>
 * 负责管理和分发不同的步骤数据处理器
 * 支持根据步骤编码和评估类型获取对应的处理器
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class StepDataProcessorFactory {

    @Autowired
    private ProcessorRegistry processorRegistry;

    @Autowired
    private ProcessorPackageManager packageManager;

    /**
     * 获取步骤数据处理器
     *
     * @param evaluateType 评估类型
     * @param stepCode     步骤编码
     * @return 步骤数据处理器
     */
    public IStepDataProcessor getProcessor(String evaluateType, String stepCode) {
        if (!StringUtils.hasText(stepCode)) {
            throw new ServiceException("步骤编码不能为空");
        }

        // 使用注册表查找处理器
        IStepDataProcessor processor = processorRegistry.findProcessor(evaluateType, stepCode);
        if (processor != null) {
            log.debug("找到匹配的处理器: {}, 评估类型: {}, 步骤编码: {}",
                    processor.getClass().getSimpleName(), evaluateType, stepCode);
            return processor;
        }

        throw new ServiceException("未找到支持的步骤数据处理器，评估类型: " + evaluateType + ", 步骤编码: " + stepCode);
    }

    /**
     * 获取所有已注册的处理器
     *
     * @return 处理器列表
     */
    public List<IStepDataProcessor> getAllProcessors() {
        // 从注册表获取所有处理器信息
        Map<String, Object> status = processorRegistry.getRegistryStatus();
        log.info("当前注册表状态: {}", status);
        return new ArrayList<>(); // 实际实现可以从注册表获取
    }

    /**
     * 获取支持的评估类型
     */
    public Set<String> getSupportedEvaluateTypes() {
        return processorRegistry.getSupportedEvaluateTypes();
    }

    /**
     * 获取指定评估类型支持的步骤
     */
    public Set<String> getSupportedStepCodes(String evaluateType) {
        return processorRegistry.getSupportedStepCodes(evaluateType);
    }

    /**
     * 获取处理器注册表状态
     */
    public Map<String, Object> getRegistryStatus() {
        return processorRegistry.getRegistryStatus();
    }

    /**
     * 生成处理器包结构报告
     */
    public String generatePackageReport() {
        return packageManager.generatePackageStructureReport(
            processorRegistry.getRegistryStatus().get("totalProcessors") != null ?
                new ArrayList<>() : new ArrayList<>()
        );
    }
}
