
-- =====================================================
-- 评估计划流程管理数据库表结构
-- 支持多种评估类型的流程定义和实例管理
-- 生成时间: 2025-07-28
-- =====================================================

-- =====================================================
-- 流程定义表
-- 存储不同评估类型的流程定义
-- =====================================================

CREATE TABLE `dsa_process_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '流程名称',
  `code` varchar(32) NOT NULL COMMENT '流程编码，唯一',
  `description` varchar(255) DEFAULT NULL COMMENT '流程描述',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(1) DEFAULT '1' COMMENT '状态，1-正常 0-禁用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='流程定义表';

CREATE TABLE `dsa_process_instance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `process_id` bigint(20) NOT NULL COMMENT '流程定义ID',
  `business_id` bigint(20) NOT NULL COMMENT '业务主键（如任务ID）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(1) DEFAULT '1' COMMENT '状态，1-正常 0-禁用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='流程实例表';

CREATE TABLE `dsa_process_step_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `process_id` bigint(20) NOT NULL COMMENT '流程ID',
  `step_order` int(11) NOT NULL COMMENT '步骤顺序',
  `step_name` varchar(64) NOT NULL COMMENT '步骤名称',
  `step_code` varchar(32) NOT NULL COMMENT '步骤编码',
  `handler_type` varchar(16) DEFAULT 'manual' COMMENT '处理类型：manual-人工，auto-系统自动',
  `description` varchar(255) DEFAULT NULL COMMENT '步骤描述',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(1) DEFAULT '1' COMMENT '状态，1-正常 0-禁用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='流程步骤定义表';

CREATE TABLE `dsa_process_step_instance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `process_instance_id` bigint(20) NOT NULL COMMENT '流程实例ID',
  `step_definition_id` bigint(20) NOT NULL COMMENT '步骤定义ID',
  `step_name` varchar(64) NOT NULL COMMENT '步骤名称',
  `status` int(1) DEFAULT '1' COMMENT '状态：1-处理中 2-完成',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration_ms` bigint(20) DEFAULT NULL COMMENT '耗时（毫秒）',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='流程步骤实例表';

CREATE TABLE `dsa_process_step_rollback` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `process_instance_id` bigint(20) NOT NULL COMMENT '流程实例ID',
  `from_step_instance_id` bigint(20) NOT NULL COMMENT '回退前步骤实例ID',
  `to_step_definition_id` bigint(20) NOT NULL COMMENT '回退到的步骤定义ID',
  `rollback_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '回退时间',
  `operator` varchar(64) DEFAULT NULL COMMENT '回退操作人',
  `rollback_reason` varchar(255) DEFAULT NULL COMMENT '回退意见/原因',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(1) DEFAULT '1' COMMENT '状态，1-正常 0-禁用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志（0 正常，1 删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='流程回退记录表';

