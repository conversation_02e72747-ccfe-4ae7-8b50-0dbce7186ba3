package com.ruoyi.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2025/7/14 11:26
 * @description
 */

public class WrapperUtils {
    /**
     * 根据 DTO 构造 QueryWrapper（只做等值匹配）
     * @param dto DTO 参数对象
     * @param entityClass 实体类 class 类型
     * @param <T> 实体类泛型
     * @return QueryWrapper<T>
     */
    public static <T> QueryWrapper<T> build(Object dto, Class<T> entityClass) {
        T entity = BeanUtils.instantiateClass(entityClass);
        BeanUtils.copyProperties(dto, entity);
        return new QueryWrapper<>(entity);
    }

    /**
     * 支持 Lambda 表达式时使用的 LambdaQueryWrapper，可以按需扩展
     */
    // public static <T> LambdaQueryWrapper<T> buildLambda(Object dto, Class<T> entityClass) {
    //     T entity = BeanUtils.instantiateClass(entityClass);
    //     BeanUtils.copyProperties(dto, entity);
    //     return new QueryWrapper<>(entity).lambda();
    // }
}
