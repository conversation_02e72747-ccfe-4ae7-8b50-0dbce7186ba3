package com.ruoyi.evaluate.company.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 被评估单位部门信息Excel导入对象 - Sheet2
 * 数据从第5行开始读取（跳过表头和示例数据）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ExcelTargetOrgDeptInfo {

    /** 序号 - A列 */
    @ExcelProperty(index = 0)
    private String serialNumber;

    /** 部门名称 - B列 */
    @ExcelProperty(index = 1)
    private String departmentName;

    /** 部门职责 - C列 */
    @ExcelProperty(index = 2)
    private String departmentDuty;

    /** 部门负责人 - D列 */
    @ExcelProperty(index = 3)
    private String departmentLeader;
}
