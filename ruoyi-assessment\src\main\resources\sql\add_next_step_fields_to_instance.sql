-- 为流程步骤实例表添加下一步配置字段
-- 执行时间：2025-07-29
-- 作者：Mario

-- 添加下一步负责人字段
ALTER TABLE `dsa_process_step_instance` 
ADD COLUMN `next_step_assignee` varchar(64) DEFAULT NULL COMMENT '下一步负责人' AFTER `operator`;

-- 添加下一步负责部门字段
ALTER TABLE `dsa_process_step_instance` 
ADD COLUMN `next_step_dept` varchar(64) DEFAULT NULL COMMENT '下一步负责部门' AFTER `next_step_assignee`;

-- 添加下一步截止日期（相对天数）字段
ALTER TABLE `dsa_process_step_instance` 
ADD COLUMN `next_step_deadline_days` int(11) DEFAULT NULL COMMENT '下一步截止日期（相对天数）' AFTER `next_step_dept`;

-- 添加是否设置任务截止日期字段
ALTER TABLE `dsa_process_step_instance` 
ADD COLUMN `set_task_deadline` int(1) DEFAULT NULL COMMENT '是否设置任务截止日期（0-否，1-是）' AFTER `next_step_deadline_days`;

-- 为新字段添加索引
ALTER TABLE `dsa_process_step_instance` 
ADD INDEX `idx_next_step_assignee` (`next_step_assignee`);

ALTER TABLE `dsa_process_step_instance` 
ADD INDEX `idx_next_step_dept` (`next_step_dept`);

ALTER TABLE `dsa_process_step_instance` 
ADD INDEX `idx_set_task_deadline` (`set_task_deadline`);

-- 添加表注释
ALTER TABLE `dsa_process_step_instance` 
COMMENT = '流程步骤实例表，支持在执行过程中配置下一步信息';

-- 创建步骤实例下一步配置视图，方便查询
CREATE OR REPLACE VIEW `v_step_instance_next_config` AS
SELECT 
    psi.id AS step_instance_id,
    psi.process_instance_id,
    psi.step_definition_id,
    psi.step_name,
    psi.status,
    psi.start_time,
    psi.end_time,
    psi.operator,
    psi.next_step_assignee,
    psi.next_step_dept,
    psi.next_step_deadline_days,
    psi.set_task_deadline,
    pi.business_id,
    pi.current_step_id,
    pd.code AS process_code,
    pd.name AS process_name
FROM `dsa_process_step_instance` psi
JOIN `dsa_process_instance` pi ON psi.process_instance_id = pi.id
JOIN `dsa_process_definition` pd ON pi.process_id = pd.id
WHERE psi.next_step_assignee IS NOT NULL 
   OR psi.next_step_dept IS NOT NULL 
   OR psi.next_step_deadline_days IS NOT NULL 
   OR psi.set_task_deadline IS NOT NULL
ORDER BY psi.process_instance_id, psi.id;

-- 验证表结构
DESCRIBE `dsa_process_step_instance`;

COMMIT;
