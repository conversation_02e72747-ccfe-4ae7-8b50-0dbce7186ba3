package ${packageName}.mapper;

import ${packageName}.domain.${ClassName};
    #if($table.sub)
    import ${packageName}.domain.${subClassName};
    #end
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * ${functionName}Mapper接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Mapper
public interface ${ClassName}Mapper extends BaseMapper<${ClassName}> {

}
