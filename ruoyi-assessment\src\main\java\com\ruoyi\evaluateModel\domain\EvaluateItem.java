package com.ruoyi.evaluateModel.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 评估项对象 dsa_evaluate_item
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_item")
public class EvaluateItem extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    @TableField(exist = false)
    private String categoryLevel1;

    @TableField(exist = false)
    private String categoryLevel2;

    /** 所属评估模型ID */
    @NotNull(message = "评估模型不能为空", groups = {AddGroup.class})
    @Excel(name = "所属评估模型ID")
    @TableField(value = "model_id")
    private Long modelId;

    /** 所属分类ID（关联评估项分类表） */
    @NotNull(message = "所属分类", groups = {AddGroup.class})
    @Excel(name = "所属分类ID")
    @TableField(value = "category_id")
    private Long categoryId;

    /** 评估项类型，1-管理类 2-技术类） */
    @Excel(name = "评估项类型")
    @TableField(value = "item_type")
    private String itemType;

    /** 是否必填，1-是 0-否 */
    @Excel(name = "是否必填，1-是 0-否")
    @TableField(value = "is_required")
    private Integer isRequired;

    /** 评估项内容 */
    @Excel(name = "评估项内容")
    @TableField(value = "item_content")
    private String itemContent;

    /** 评估要求 */
    @Excel(name = "评估要求")
    @TableField(value = "requirement")
    private String requirement;

    /** 评估指导 */
    @Excel(name = "评估指导")
    @TableField(value = "guidance")
    private String guidance;

    /** 符合评估记录文案 */
    @Excel(name = "符合评估记录文案")
    @TableField(value = "doc_full")
    private String docFull;

    /** 部分符合评估记录文案 */
    @Excel(name = "部分符合评估记录文案")
    @TableField(value = "doc_partial")
    private String docPartial;

    /** 不符合评估记录文案 */
    @Excel(name = "不符合评估记录文案")
    @TableField(value = "doc_fail")
    private String docFail;

    /** 不适用评估记录文案 */
    @Excel(name = "不适用评估记录文案")
    @TableField(value = "doc_not_suit")
    private String docNotSuit;

    /** 整改建议文案 */
    @Excel(name = "整改建议文案")
    @TableField(value = "doc_advice")
    private String docAdvice;

    /** 排序值，越大越靠前 */
    @Excel(name = "排序值，越大越靠前")
    @TableField(value = "sort")
    private Long sort;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}