package com.ruoyi.common.utils.poi;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 */
public class ExcelImportHelper<T> {
    @Data
    public static class ImportError {
        private int rowNum;
        private String column;
        private String reason;
    }

    @Data
    public static class ImportResult<T> {
        private int successCount;
        private int failureCount;
        private List<T> successList = new ArrayList<>();
        private List<RowError<T>> errorList = new ArrayList<>();
    }

    @Data
    public static class RowError<T> {
        private int rowNum;
        private T rowData;
        private List<ColumnError> errors = new ArrayList<>();

        @Data
        public static class ColumnError {
            private String column;
            private String reason;
        }
    }

    /**
     * 通用Excel导入
     *
     * @param file         上传的文件
     * @param clazz        实体类Class
     * @param validator    校验器（可为null）
     * @param afterSuccess 成功后处理（如入库，可为null）
     * @param titleNum     标题占用行数
     * @return 导入结果，包含成功和每行的错误信息
     */
    public ImportResult<T> importExcel(MultipartFile file, Class<T> clazz,
                                       BiConsumer<T, List<RowError.ColumnError>> validator,
                                       BiConsumer<T, List<RowError.ColumnError>> afterSuccess,
                                       int titleNum) throws Exception {
        ExcelUtil<T> util = new ExcelUtil<>(clazz);
        List<T> dataList;
        try (InputStream is = file.getInputStream()) {
            dataList = util.importExcel(is, titleNum);
        }
        ImportResult<T> result = new ImportResult<>();
        int rowNum = 1;
        for (T data : dataList) {
            rowNum++;
            List<RowError.ColumnError> rowErrors = new ArrayList<>();
            if (validator != null) {
                validator.accept(data, rowErrors);
            }
            if (rowErrors.isEmpty()) {
                if (afterSuccess != null) {
                    afterSuccess.accept(data, rowErrors);
                }
                if (rowErrors.isEmpty()) {
                    result.getSuccessList().add(data);
                } else {
                    RowError<T> rowError = new RowError<>();
                    rowError.setRowNum(rowNum);
                    rowError.setRowData(data);
                    rowError.setErrors(rowErrors);
                    result.getErrorList().add(rowError);
                }
            } else {
                RowError<T> rowError = new RowError<>();
                rowError.setRowNum(rowNum);
                rowError.setRowData(data);
                rowError.setErrors(rowErrors);
                result.getErrorList().add(rowError);
            }
        }
        result.setSuccessCount(result.getSuccessList().size());
        result.setFailureCount(result.getErrorList().size());
        return result;
    }

    /**
     * 兼容老用法
     */
    public ImportResult<T> importExcel(MultipartFile file, Class<T> clazz,
                                       BiConsumer<T, List<RowError.ColumnError>> validator,
                                       BiConsumer<T, List<RowError.ColumnError>> afterSuccess) throws Exception {
        return importExcel(file, clazz, validator, afterSuccess, 1);
    }
}
