package com.ruoyi.evaluateModel.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 评估类型对象 dsa_evaluate_type
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class ExcelEvaluateItemVo {
    /**
     *  ExcelProperty -> index 从0开始计数
     */
    @ExcelProperty(index = 0)
    private String no;

    @ExcelProperty(index = 1)
    private String categoryLevel1;

    @ExcelProperty(index = 2)
    private String categoryLevel2;

    @ExcelProperty(index = 3)
    private String serialNo;

    @ExcelProperty(index = 4)
    private String itemType;

    @ExcelProperty(index = 5)
    private String isRequired;

    @ExcelProperty(index = 6)
    private String itemContent;

    @ExcelProperty(index = 7)
    private String requirement;

    @ExcelProperty(index = 8)
    private String guidance;

    @ExcelProperty(index = 9)
    private String docFull;

    @ExcelProperty(index = 10)
    private String docPartial;

    @ExcelProperty(index = 11)
    private String docFail;

    @ExcelProperty(index = 12)
    private String docNotSuit;

    @ExcelProperty(index = 13)
    private String docAdvice;
}