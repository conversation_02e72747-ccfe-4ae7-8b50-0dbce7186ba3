<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.company.mapper.TargetOrgDeptInfoMapper">
    
    <resultMap type="TargetOrgDeptInfo" id="TargetOrgDeptInfoResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="departmentName"    column="department_name"    />
        <result property="departmentDuty"    column="department_duty"    />
        <result property="departmentLeader"    column="department_leader"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTargetOrgDeptInfoVo">
        select id, org_id, department_name, department_duty, department_leader, status, create_by, create_time, update_by, update_time, remark, del_flag from dsa_target_org_dept_info
    </sql>

    <select id="selectTargetOrgDeptInfoList" parameterType="TargetOrgDeptInfo" resultMap="TargetOrgDeptInfoResult">
        <include refid="selectTargetOrgDeptInfoVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="departmentName != null  and departmentName != ''"> and department_name like concat('%', #{departmentName}, '%')</if>
            <if test="departmentDuty != null  and departmentDuty != ''"> and department_duty = #{departmentDuty}</if>
            <if test="departmentLeader != null  and departmentLeader != ''"> and department_leader = #{departmentLeader}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTargetOrgDeptInfoById" parameterType="Long" resultMap="TargetOrgDeptInfoResult">
        <include refid="selectTargetOrgDeptInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTargetOrgDeptInfo" parameterType="TargetOrgDeptInfo" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_target_org_dept_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="departmentDuty != null">department_duty,</if>
            <if test="departmentLeader != null">department_leader,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="departmentDuty != null">#{departmentDuty},</if>
            <if test="departmentLeader != null">#{departmentLeader},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTargetOrgDeptInfo" parameterType="TargetOrgDeptInfo">
        update dsa_target_org_dept_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="departmentDuty != null">department_duty = #{departmentDuty},</if>
            <if test="departmentLeader != null">department_leader = #{departmentLeader},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTargetOrgDeptInfoById" parameterType="Long">
        delete from dsa_target_org_dept_info where id = #{id}
    </delete>

    <delete id="deleteTargetOrgDeptInfoByIds" parameterType="String">
        delete from dsa_target_org_dept_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>