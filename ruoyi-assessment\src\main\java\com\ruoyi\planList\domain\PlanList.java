package com.ruoyi.planList.domain;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.annotation.ExcelFieldValidate;


/**
 * 评估计划清单对象 dsa_plan_list
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_plan_list")
public class PlanList extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 单位id
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 版本id
     */
    @TableField(value = "version_id")
    private Long versionId;

    /**
     * 单位名称
     */
    @ExcelFieldValidate(name = "单位名称", required = true)
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 数据项名称
     */
    @ExcelFieldValidate(name = "数据项名称", required = true)
    @TableField(value = "data_item_name")
    private String dataItemName;

    /**
     * 数据类型
     */
    @ExcelFieldValidate(name = "数据类型", required = true)
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 数据处理活动
     */
    @ExcelFieldValidate(name = "数据处理活动", required = true)
    @TableField(value = "data_processing_activity")
    private String dataProcessingActivity;

    /**
     * 数据级别
     */
    @ExcelFieldValidate(name = "数据级别", required = true, dictType = "business_data_level")
    @TableField(value = "data_level")
    private String dataLevel;

    /**
     * 涉及信息系统名称
     */
    @ExcelFieldValidate(name = "涉及信息系统名称", required = true)
    @TableField(value = "info_system_name")
    private String infoSystemName;

    /**
     * 重要系统类别
     */
    @ExcelFieldValidate(name = "重要系统类别", required = true)
    @TableField(value = "important_system_category")
    private String importantSystemCategory;

    /**
     * 业务条线
     */
    @ExcelFieldValidate(name = "业务条线", required = true)
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 计划评估完成时间
     */
    @ExcelFieldValidate(name = "计划评估完成时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "planned_eval_time")
    private Date plannedEvalTime;

    /**
     * 实际评估完成时间
     */
    @ExcelFieldValidate(name = "实际评估完成时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "actual_eval_time")
    private Date actualEvalTime;

    /**
     * 是否评估完成
     */
    @ExcelFieldValidate(name = "是否评估完成", required = true)
    @TableField(value = "is_eval_completed")
    private String isEvalCompleted;

    /**
     * 不合规数量
     */
    @TableField(value = "non_compliance_count")
    private Integer nonComplianceCount;

    /**
     * 可能性等级
     */
    @TableField(value = "possibility_level")
    private String possibilityLevel;

    /**
     * 安全影响度
     */
    @TableField(value = "security_impact")
    private String securityImpact;

    /**
     * 数据处理活动风险等级
     */
    @TableField(value = "risk_level")
    private String riskLevel;

    /**
     * 在报送工信部综合的企业鉴定整体报告中
     */
    @ExcelFieldValidate(name = "在报送工信部综合的企业鉴定整体报告中", required = true)
    @TableField(value = "in_miit_report")
    private String inMiitReport;

    /**
     * 评估方式
     */
    @ExcelFieldValidate(name = "评估方式", required = true)
    @TableField(value = "eval_method")
    private String evalMethod;

    /**
     * 评估机构
     */
    @ExcelFieldValidate(name = "评估机构", required = true)
    @TableField(value = "eval_agency")
    private String evalAgency;

    /**
     * 评估负责人
     */
    @ExcelFieldValidate(name = "评估负责人", required = true)
    @TableField(value = "eval_responsible")
    private String evalResponsible;


    /**
     * 状态，1-正常 0-禁用
     */
    @ExcelIgnore
    @TableField(value = "status")
    private Integer status;

}
