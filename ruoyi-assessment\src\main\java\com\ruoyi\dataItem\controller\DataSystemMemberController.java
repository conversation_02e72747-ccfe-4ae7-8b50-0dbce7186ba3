package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataSystemMember;
import com.ruoyi.dataItem.service.IDataSystemMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B2 部门及人员信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/dataItem/member")
@Api(value = "B2 部门及人员信息控制器", tags = {"B2 部门及人员信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataSystemMemberController extends BaseController
{
    private final IDataSystemMemberService dataSystemMemberService;

    /**
     * 查询B2 部门及人员信息列表
     */
    @ApiOperation("查询B2 部门及人员信息列表")
    @PreAuthorize("@ss.hasPermi('dataItem:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Valid DataSystemMember dataSystemMember) {
        startPage();
        List<DataSystemMember> list = dataSystemMemberService.list(new QueryWrapper<DataSystemMember>(dataSystemMember));
        return getDataTable(list);
    }

    /**
     * 获取B2 部门及人员信息详细信息
     */
    @ApiOperation("获取B2 部门及人员信息详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataSystemMemberService.getById(id));
    }

    /**
     * 新增B2 部门及人员信息
     */
    @ApiOperation("新增B2 部门及人员信息")
    @PreAuthorize("@ss.hasPermi('dataItem:member:add')")
    @Log(title = "B2 部门及人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody DataSystemMember dataSystemMember) {
        return toAjax(dataSystemMemberService.save(dataSystemMember));
    }

    /**
     * 修改B2 部门及人员信息
     */
    @ApiOperation("修改B2 部门及人员信息")
    @PreAuthorize("@ss.hasPermi('dataItem:member:edit')")
    @Log(title = "B2 部门及人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataSystemMember dataSystemMember) {
        return toAjax(dataSystemMemberService.updateById(dataSystemMember));
    }

    /**
     * 删除B2 部门及人员信息
     */
    @ApiOperation("删除B2 部门及人员信息")
    @PreAuthorize("@ss.hasPermi('dataItem:member:remove')")
    @Log(title = "B2 部门及人员信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataSystemMemberService.removeByIds(Arrays.asList(ids)));
    }
}