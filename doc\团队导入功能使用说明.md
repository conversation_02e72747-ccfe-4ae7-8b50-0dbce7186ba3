# 评估团队导入功能使用说明

## 功能概述

新增的团队导入功能允许通过Excel文件自动创建评估团队并导入团队成员信息。

## API接口

### 导入团队成员

**接口地址：** `POST /evaluateCompany/team/import`

**请求参数：**
- `teamName` (String): 团队名称
- `orgId` (Long): 所属单位ID  
- `file` (MultipartFile): Excel文件

**权限要求：** `evaluateCompany:team:import`

## Excel文件格式要求

### 表头格式
Excel文件应包含以下列：
- 序号
- 角色  
- 职责
- 姓名
- 单位
- 岗位
- 能力资质
- 评估工作经验

### 分组标识
系统会自动识别以下分组名称（在职责列中）：
- 项目管理组
- 评估组
- 技术支持组
- 质量保证组
- 配置管理组

### Excel示例格式

```
序号 | 角色     | 职责           | 姓名 | 单位   | 岗位     | 能力资质 | 评估工作经验
-----|----------|----------------|------|--------|----------|----------|-------------
     |          | 项目管理组     |      |        |          |          |
1    | 项目经理 | 负责项目管理   | 张三 | XX公司 | 项目经理 | 高级     | 5年
     |          | 评估组         |      |        |          |          |
1    | 组长     | 主持评估工作   | 李四 | XX公司 | 评估员   | 中级     | 3年
2    | 成员     | 参与评估工作   | 王五 | XX公司 | 评估员   | 初级     | 2年
```

## 实现逻辑

1. **团队创建**：根据传入的团队名称和orgId创建新的评估团队
2. **Excel解析**：使用EasyExcel解析上传的文件
3. **分组识别**：自动识别Excel中的分组标题行，为后续成员分配正确的groupId
4. **数据转换**：将Excel数据转换为团队成员实体
5. **批量保存**：将所有成员数据批量保存到数据库

## 返回结果

**成功响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "teamId": 123,
    "teamName": "测试团队",
    "memberCount": 5,
    "message": "成功导入团队 '测试团队'，包含 5 名成员"
  }
}
```

**失败响应：**
```json
{
  "code": 500,
  "msg": "导入失败: 具体错误信息"
}
```

## 注意事项

1. Excel文件必须包含有效的成员数据（至少需要姓名）
2. 分组名称必须与系统预定义的分组名称完全匹配
3. 导入过程中如果出现错误，整个事务会回滚
4. 建议在导入前检查Excel文件格式是否正确

## 相关文件

- 控制器：`EvaluateCompanyTeamController.java`
- 服务接口：`IEvaluateCompanyTeamService.java`
- 服务实现：`EvaluateCompanyTeamServiceImpl.java`
- Excel VO：`ExcelTeamMemberVo.java`
- Excel监听器：`TeamMemberImportListener.java`
- 分组枚举：`TeamGroupEnum.java`
