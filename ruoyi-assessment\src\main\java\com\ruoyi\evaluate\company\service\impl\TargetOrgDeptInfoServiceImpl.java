package com.ruoyi.evaluate.company.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.company.mapper.TargetOrgDeptInfoMapper;
import com.ruoyi.evaluate.company.domain.TargetOrgDeptInfo;
import com.ruoyi.evaluate.company.service.ITargetOrgDeptInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 被评估单位 部门信息管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class TargetOrgDeptInfoServiceImpl extends ServiceImpl<TargetOrgDeptInfoMapper, TargetOrgDeptInfo> implements ITargetOrgDeptInfoService {

}
