package com.ruoyi.evaluate.evaluateData.service.impl;

import java.util.List;
import java.util.ArrayList;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateData.mapper.EvaluatePlanTeamMemberMapper;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanTeamMember;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanTeamMemberService;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeamMember;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * 评估单位评估团队成员Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
public class EvaluatePlanTeamMemberServiceImpl extends ServiceImpl<EvaluatePlanTeamMemberMapper, EvaluatePlanTeamMember> implements IEvaluatePlanTeamMemberService {

    @Autowired
    private IEvaluateCompanyTeamMemberService evaluateCompanyTeamMemberService;

    @Override
    public String importFromTeam(Long teamId, Long planId) {
        // 参数校验
        if (teamId == null) {
            throw new ServiceException("团队ID不能为空");
        }
        if (planId == null) {
            throw new ServiceException("计划ID不能为空");
        }

        try {
            // 使用lambda表达式查询指定团队的所有成员
            List<EvaluateCompanyTeamMember> companyTeamMembers = evaluateCompanyTeamMemberService
                    .lambdaQuery()
                    .eq(EvaluateCompanyTeamMember::getTeamId, teamId)
                    .eq(EvaluateCompanyTeamMember::getDelFlag, "0")
                    .list();

            if (companyTeamMembers.isEmpty()) {
                throw new ServiceException("指定团队中没有找到成员数据");
            }

            // 转换为评估计划团队成员
            List<EvaluatePlanTeamMember> planTeamMembers = new ArrayList<>();
            for (EvaluateCompanyTeamMember companyMember : companyTeamMembers) {
                EvaluatePlanTeamMember planMember = new EvaluatePlanTeamMember();

                // 使用BeanUtils复制属性
                BeanUtils.copyBeanProp(planMember, companyMember);

                // 设置特殊字段
                planMember.setId(null);
                planMember.setPlanId(planId);
                planMember.setGroupId(companyMember.getGroupId().intValue());
                planMember.setCreateBy(SecurityUtils.getUsername());
                planMember.setCreateTime(null);
                planMember.setUpdateBy(null);
                planMember.setUpdateTime(null);

                planTeamMembers.add(planMember);
            }

            // 批量保存
            boolean success = this.saveBatch(planTeamMembers);

            if (!success) {
                throw new ServiceException("保存团队成员失败");
            }

            log.info("成功从团队[{}]导入{}名成员到计划[{}]", teamId, planTeamMembers.size(), planId);
            return String.format("成功导入 %d 名团队成员", planTeamMembers.size());

        } catch (ServiceException e) {
            log.error("导入团队成员失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("导入团队成员时发生异常", e);
            throw new ServiceException("导入失败: " + e.getMessage());
        }
    }
}
