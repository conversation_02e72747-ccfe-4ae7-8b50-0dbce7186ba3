package com.ruoyi.evaluateModel.service.impl;

import java.util.List;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluateModel.mapper.EvaluateTypeMapper;
import com.ruoyi.evaluateModel.domain.EvaluateType;
import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.service.IEvaluateTypeService;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * 评估类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class EvaluateTypeServiceImpl extends ServiceImpl<EvaluateTypeMapper, EvaluateType> implements IEvaluateTypeService {

    @Autowired
    private IEvaluateModelService evaluateModelService;

    @Override
    public List<EvaluateType> listWithModel(EvaluateType evaluateType) {
        // 1. 查询评估类型列表
        List<EvaluateType> typeList = this.list(new QueryWrapper<>(evaluateType));

        // 2. 为每个类型查询关联的启用模型
        for (EvaluateType type : typeList) {
            LambdaQueryWrapper<EvaluateModel> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(EvaluateModel::getTypeId, type.getId());
            modelWrapper.eq(EvaluateModel::getStatus, 1);
            modelWrapper.eq(EvaluateModel::getIsEnabled, 1); // 只查询启用的模型
            modelWrapper.orderByDesc(EvaluateModel::getId);
            modelWrapper.last("LIMIT 1");

            List<EvaluateModel> models = evaluateModelService.list(modelWrapper);
            if (!models.isEmpty()) {
                type.setModel(models.get(0));
            }
        }

        return typeList;
    }

}
