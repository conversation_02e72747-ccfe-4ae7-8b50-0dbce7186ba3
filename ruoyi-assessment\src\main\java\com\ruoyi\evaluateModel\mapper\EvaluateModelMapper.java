package com.ruoyi.evaluateModel.mapper;

import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 评估模型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface EvaluateModelMapper extends BaseMapper<EvaluateModel> {

    /**
     * 批量更新同type_id下的模型启用状态
     * @param typeId 评估类型ID
     * @param isEnabled 启用状态
     * @param excludeId 排除的模型ID（可为null）
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 更新记录数
     */
    int updateEnabledStatusByTypeId(@Param("typeId") Long typeId,
                                   @Param("isEnabled") Integer isEnabled,
                                   @Param("excludeId") Long excludeId,
                                   @Param("updateBy") String updateBy,
                                   @Param("updateTime") Date updateTime);

}
