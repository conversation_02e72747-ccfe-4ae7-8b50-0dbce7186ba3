package com.ruoyi.evaluate.company.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.ListGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.company.domain.TargetMemberInfo;
import com.ruoyi.evaluate.company.service.ITargetMemberInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 被评估单位人员信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/targetMember/info")
@Api(value = "被评估单位人员信息控制器", tags = {"被评估单位人员信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TargetMemberInfoController extends BaseController {
    private final ITargetMemberInfoService targetMemberInfoService;

    /**
     * 查询被评估单位人员信息列表
     */
    @ApiOperation("查询被评估单位人员信息列表")
    @PreAuthorize("@ss.hasPermi('targetMember:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) TargetMemberInfo targetMemberInfo) {
        startPage();
        List<TargetMemberInfo> list = targetMemberInfoService.list(new QueryWrapper<TargetMemberInfo>(targetMemberInfo));
        return getDataTable(list);
    }

    /**
     * 导出被评估单位人员信息列表
     */
    // @ApiOperation("导出被评估单位人员信息列表")
    // @PreAuthorize("@ss.hasPermi('targetMember:info:export')")
    // @Log(title = "被评估单位人员信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response,TargetMemberInfo targetMemberInfo) {
    //     List<TargetMemberInfo> list = targetMemberInfoService.list(new QueryWrapper<TargetMemberInfo>(targetMemberInfo));
    //     ExcelUtil<TargetMemberInfo> util = new ExcelUtil<TargetMemberInfo>(TargetMemberInfo.class);
    //     util.exportExcel(response,list, "被评估单位人员信息数据");
    // }

    /**
     * 获取被评估单位人员信息详细信息
     */
    @ApiOperation("获取被评估单位人员信息详细信息")
    @PreAuthorize("@ss.hasPermi('targetMember:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(targetMemberInfoService.getById(id));
    }

    /**
     * 新增被评估单位人员信息
     */
    @ApiOperation("新增被评估单位人员信息")
    @PreAuthorize("@ss.hasPermi('targetMember:info:add')")
    @Log(title = "被评估单位人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody TargetMemberInfo targetMemberInfo) {
        return toAjax(targetMemberInfoService.save(targetMemberInfo));
    }

    /**
     * 修改被评估单位人员信息
     */
    @ApiOperation("修改被评估单位人员信息")
    @PreAuthorize("@ss.hasPermi('targetMember:info:edit')")
    @Log(title = "被评估单位人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TargetMemberInfo targetMemberInfo) {
        return toAjax(targetMemberInfoService.updateById(targetMemberInfo));
    }

    /**
     * 删除被评估单位人员信息
     */
    @ApiOperation("删除被评估单位人员信息")
    @PreAuthorize("@ss.hasPermi('targetMember:info:remove')")
    @Log(title = "被评估单位人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(targetMemberInfoService.removeByIds(Arrays.asList(ids)));
    }
}