package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemBase;
import com.ruoyi.dataItem.service.IDataItemBaseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据项基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/base")
@Api(value = "数据项基本信息控制器", tags = {"数据项基本信息管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemBaseController extends BaseController
{
    private final IDataItemBaseService dataItemBaseService;

    /**
     * 查询数据项基本信息列表
     */
    @ApiOperation("查询数据项基本信息列表")
    @PreAuthorize("@ss.hasPermi('dataItem:base:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Valid DataItemBase dataItemBase) {
        startPage();
        List<DataItemBase> list = dataItemBaseService.list(new QueryWrapper<DataItemBase>(dataItemBase));
        return getDataTable(list);
    }

    /**
     * 获取数据项基本信息详细信息
     */
    @ApiOperation("获取数据项基本信息详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:base:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemBaseService.getById(id));
    }

    /**
     * 新增数据项基本信息
     */
    @ApiOperation("新增数据项基本信息")
    @PreAuthorize("@ss.hasPermi('dataItem:base:add')")
    @Log(title = "数据项基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody DataItemBase dataItemBase) {
        return toAjax(dataItemBaseService.saveWithValidation(dataItemBase));
    }

    /**
     * 修改数据项基本信息
     */
    @ApiOperation("修改数据项基本信息")
    @PreAuthorize("@ss.hasPermi('dataItem:base:edit')")
    @Log(title = "数据项基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemBase dataItemBase) {
        return toAjax(dataItemBaseService.updateById(dataItemBase));
    }

    /**
     * 删除数据项基本信息
     */
    @ApiOperation("删除数据项基本信息")
    @PreAuthorize("@ss.hasPermi('dataItem:base:remove')")
    @Log(title = "数据项基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemBaseService.removeByIds(Arrays.asList(ids)));
    }
}