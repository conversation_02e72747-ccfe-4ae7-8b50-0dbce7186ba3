<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemSnapshotMapper">
    
    <resultMap type="DataItemSnapshot" id="DataItemSnapshotResult">
        <result property="id"    column="id"    />
        <result property="versionId"    column="version_id"    />
        <result property="dataItemId"    column="data_item_id"    />
        <result property="orgId"    column="org_id"    />
        <result property="dataSystemId"    column="data_system_id"    />
        <result property="dataName"    column="data_name"    />
        <result property="dataLevel"    column="data_level"    />
        <result property="categoryLevel1"    column="category_level1"    />
        <result property="categoryLevel2"    column="category_level2"    />
        <result property="categoryLevel3"    column="category_level3"    />
        <result property="categoryLevel4"    column="category_level4"    />
        <result property="dataCarrier"    column="data_carrier"    />
        <result property="dataSource"    column="data_source"    />
        <result property="dataAmountGb"    column="data_amount_gb"    />
        <result property="dataAmountCount"    column="data_amount_count"    />
        <result property="coverageType"    column="coverage_type"    />
        <result property="coverageRatio"    column="coverage_ratio"    />
        <result property="dataAccuracy"    column="data_accuracy"    />
        <result property="description"    column="description"    />
        <result property="processorName"    column="processor_name"    />
        <result property="orgCode"    column="org_code"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="processorNature"    column="processor_nature"    />
        <result property="industry"    column="industry"    />
        <result property="mainBusiness"    column="main_business"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPosition"    column="contact_position"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="processingMethod"    column="processing_method"    />
        <result property="processingPurpose"    column="processing_purpose"    />
        <result property="isAlgorithmAuto"    column="is_algorithm_auto"    />
        <result property="isCrossBorder"    column="is_cross_border"    />
        <result property="crossBorderReceiver"    column="cross_border_receiver"    />
        <result property="crossBorderMethod"    column="cross_border_method"    />
        <result property="isCrossBorderAssess"    column="is_cross_border_assess"    />
        <result property="crossBorderAssessResult"    column="cross_border_assess_result"    />
        <result property="isExternalShare"    column="is_external_share"    />
        <result property="externalShareReceiver"    column="external_share_receiver"    />
        <result property="externalShareMethod"    column="external_share_method"    />
        <result property="isCrossSubjectFlow"    column="is_cross_subject_flow"    />
        <result property="isForeignData"    column="is_foreign_data"    />
        <result property="systemName"    column="system_name"    />
        <result property="systemType"    column="system_type"    />
        <result property="systemIp"    column="system_ip"    />
        <result property="systemDomain"    column="system_domain"    />
        <result property="cyberProtectLevel"    column="cyber_protect_level"    />
        <result property="networkProtectRecord"    column="network_protect_record"    />
        <result property="isCriticalInfra"    column="is_critical_infra"    />
        <result property="isDataRiskAssess"    column="is_data_risk_assess"    />
        <result property="assessOrg"    column="assess_org"    />
        <result property="assessStandard"    column="assess_standard"    />
        <result property="assessTime"    column="assess_time"    />
        <result property="assessConclusion"    column="assess_conclusion"    />
        <result property="dutyDept"    column="duty_dept"    />
        <result property="dutyUser"    column="duty_user"    />
        <result property="remark"    column="remark"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="snapshotBy"    column="snapshot_by"    />
        <result property="snapshotTime"    column="snapshot_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataItemSnapshotVo">
        select id, version_id, data_item_id, org_id, data_system_id, data_name, data_level, category_level1, category_level2, category_level3, category_level4, data_carrier, data_source, data_amount_gb, data_amount_count, coverage_type, coverage_ratio, data_accuracy, description, processor_name, org_code, province, city, processor_nature, industry, main_business, contact_name, contact_position, contact_info, processing_method, processing_purpose, is_algorithm_auto, is_cross_border, cross_border_receiver, cross_border_method, is_cross_border_assess, cross_border_assess_result, is_external_share, external_share_receiver, external_share_method, is_cross_subject_flow, is_foreign_data, system_name, system_type, system_ip, system_domain, cyber_protect_level, network_protect_record, is_critical_infra, is_data_risk_assess, assess_org, assess_standard, assess_time, assess_conclusion, duty_dept, duty_user, remark, data_status, status, create_by, create_time, update_by, update_time, delete_by, delete_time, snapshot_by, snapshot_time, del_flag from dsa_data_item_snapshot
    </sql>

    <select id="selectDataItemSnapshotList" parameterType="DataItemSnapshot" resultMap="DataItemSnapshotResult">
        <include refid="selectDataItemSnapshotVo"/>
        <where>  
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="dataItemId != null "> and data_item_id = #{dataItemId}</if>
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="dataSystemId != null "> and data_system_id = #{dataSystemId}</if>
            <if test="dataName != null  and dataName != ''"> and data_name like concat('%', #{dataName}, '%')</if>
            <if test="dataLevel != null  and dataLevel != ''"> and data_level = #{dataLevel}</if>
            <if test="categoryLevel1 != null  and categoryLevel1 != ''"> and category_level1 = #{categoryLevel1}</if>
            <if test="categoryLevel2 != null  and categoryLevel2 != ''"> and category_level2 = #{categoryLevel2}</if>
            <if test="categoryLevel3 != null  and categoryLevel3 != ''"> and category_level3 = #{categoryLevel3}</if>
            <if test="categoryLevel4 != null  and categoryLevel4 != ''"> and category_level4 = #{categoryLevel4}</if>
            <if test="dataCarrier != null  and dataCarrier != ''"> and data_carrier = #{dataCarrier}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="dataAmountGb != null "> and data_amount_gb = #{dataAmountGb}</if>
            <if test="dataAmountCount != null "> and data_amount_count = #{dataAmountCount}</if>
            <if test="coverageType != null  and coverageType != ''"> and coverage_type = #{coverageType}</if>
            <if test="coverageRatio != null  and coverageRatio != ''"> and coverage_ratio = #{coverageRatio}</if>
            <if test="dataAccuracy != null  and dataAccuracy != ''"> and data_accuracy = #{dataAccuracy}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="processorName != null  and processorName != ''"> and processor_name like concat('%', #{processorName}, '%')</if>
            <if test="orgCode != null  and orgCode != ''"> and org_code = #{orgCode}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="processorNature != null  and processorNature != ''"> and processor_nature = #{processorNature}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="mainBusiness != null  and mainBusiness != ''"> and main_business = #{mainBusiness}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPosition != null  and contactPosition != ''"> and contact_position = #{contactPosition}</if>
            <if test="contactInfo != null  and contactInfo != ''"> and contact_info = #{contactInfo}</if>
            <if test="processingMethod != null  and processingMethod != ''"> and processing_method = #{processingMethod}</if>
            <if test="processingPurpose != null  and processingPurpose != ''"> and processing_purpose = #{processingPurpose}</if>
            <if test="isAlgorithmAuto != null "> and is_algorithm_auto = #{isAlgorithmAuto}</if>
            <if test="isCrossBorder != null "> and is_cross_border = #{isCrossBorder}</if>
            <if test="crossBorderReceiver != null  and crossBorderReceiver != ''"> and cross_border_receiver = #{crossBorderReceiver}</if>
            <if test="crossBorderMethod != null  and crossBorderMethod != ''"> and cross_border_method = #{crossBorderMethod}</if>
            <if test="isCrossBorderAssess != null "> and is_cross_border_assess = #{isCrossBorderAssess}</if>
            <if test="crossBorderAssessResult != null  and crossBorderAssessResult != ''"> and cross_border_assess_result = #{crossBorderAssessResult}</if>
            <if test="isExternalShare != null "> and is_external_share = #{isExternalShare}</if>
            <if test="externalShareReceiver != null  and externalShareReceiver != ''"> and external_share_receiver = #{externalShareReceiver}</if>
            <if test="externalShareMethod != null  and externalShareMethod != ''"> and external_share_method = #{externalShareMethod}</if>
            <if test="isCrossSubjectFlow != null "> and is_cross_subject_flow = #{isCrossSubjectFlow}</if>
            <if test="isForeignData != null "> and is_foreign_data = #{isForeignData}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="systemType != null  and systemType != ''"> and system_type = #{systemType}</if>
            <if test="systemIp != null  and systemIp != ''"> and system_ip = #{systemIp}</if>
            <if test="systemDomain != null  and systemDomain != ''"> and system_domain = #{systemDomain}</if>
            <if test="cyberProtectLevel != null  and cyberProtectLevel != ''"> and cyber_protect_level = #{cyberProtectLevel}</if>
            <if test="networkProtectRecord != null  and networkProtectRecord != ''"> and network_protect_record = #{networkProtectRecord}</if>
            <if test="isCriticalInfra != null "> and is_critical_infra = #{isCriticalInfra}</if>
            <if test="isDataRiskAssess != null "> and is_data_risk_assess = #{isDataRiskAssess}</if>
            <if test="assessOrg != null  and assessOrg != ''"> and assess_org = #{assessOrg}</if>
            <if test="assessStandard != null  and assessStandard != ''"> and assess_standard = #{assessStandard}</if>
            <if test="assessTime != null "> and assess_time = #{assessTime}</if>
            <if test="assessConclusion != null  and assessConclusion != ''"> and assess_conclusion = #{assessConclusion}</if>
            <if test="dutyDept != null  and dutyDept != ''"> and duty_dept = #{dutyDept}</if>
            <if test="dutyUser != null  and dutyUser != ''"> and duty_user = #{dutyUser}</if>
            <if test="dataStatus != null "> and data_status = #{dataStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="deleteBy != null  and deleteBy != ''"> and delete_by = #{deleteBy}</if>
            <if test="deleteTime != null "> and delete_time = #{deleteTime}</if>
            <if test="snapshotBy != null  and snapshotBy != ''"> and snapshot_by = #{snapshotBy}</if>
            <if test="snapshotTime != null "> and snapshot_time = #{snapshotTime}</if>
        </where>
    </select>
    
    <select id="selectDataItemSnapshotById" parameterType="Long" resultMap="DataItemSnapshotResult">
        <include refid="selectDataItemSnapshotVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemSnapshot" parameterType="DataItemSnapshot" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionId != null">version_id,</if>
            <if test="dataItemId != null">data_item_id,</if>
            <if test="orgId != null">org_id,</if>
            <if test="dataSystemId != null">data_system_id,</if>
            <if test="dataName != null and dataName != ''">data_name,</if>
            <if test="dataLevel != null">data_level,</if>
            <if test="categoryLevel1 != null">category_level1,</if>
            <if test="categoryLevel2 != null">category_level2,</if>
            <if test="categoryLevel3 != null">category_level3,</if>
            <if test="categoryLevel4 != null">category_level4,</if>
            <if test="dataCarrier != null">data_carrier,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="dataAmountGb != null">data_amount_gb,</if>
            <if test="dataAmountCount != null">data_amount_count,</if>
            <if test="coverageType != null">coverage_type,</if>
            <if test="coverageRatio != null">coverage_ratio,</if>
            <if test="dataAccuracy != null">data_accuracy,</if>
            <if test="description != null">description,</if>
            <if test="processorName != null">processor_name,</if>
            <if test="orgCode != null">org_code,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="processorNature != null">processor_nature,</if>
            <if test="industry != null">industry,</if>
            <if test="mainBusiness != null">main_business,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPosition != null">contact_position,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="processingMethod != null">processing_method,</if>
            <if test="processingPurpose != null">processing_purpose,</if>
            <if test="isAlgorithmAuto != null">is_algorithm_auto,</if>
            <if test="isCrossBorder != null">is_cross_border,</if>
            <if test="crossBorderReceiver != null">cross_border_receiver,</if>
            <if test="crossBorderMethod != null">cross_border_method,</if>
            <if test="isCrossBorderAssess != null">is_cross_border_assess,</if>
            <if test="crossBorderAssessResult != null">cross_border_assess_result,</if>
            <if test="isExternalShare != null">is_external_share,</if>
            <if test="externalShareReceiver != null">external_share_receiver,</if>
            <if test="externalShareMethod != null">external_share_method,</if>
            <if test="isCrossSubjectFlow != null">is_cross_subject_flow,</if>
            <if test="isForeignData != null">is_foreign_data,</if>
            <if test="systemName != null">system_name,</if>
            <if test="systemType != null">system_type,</if>
            <if test="systemIp != null">system_ip,</if>
            <if test="systemDomain != null">system_domain,</if>
            <if test="cyberProtectLevel != null">cyber_protect_level,</if>
            <if test="networkProtectRecord != null">network_protect_record,</if>
            <if test="isCriticalInfra != null">is_critical_infra,</if>
            <if test="isDataRiskAssess != null">is_data_risk_assess,</if>
            <if test="assessOrg != null">assess_org,</if>
            <if test="assessStandard != null">assess_standard,</if>
            <if test="assessTime != null">assess_time,</if>
            <if test="assessConclusion != null">assess_conclusion,</if>
            <if test="dutyDept != null">duty_dept,</if>
            <if test="dutyUser != null">duty_user,</if>
            <if test="remark != null">remark,</if>
            <if test="dataStatus != null">data_status,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="snapshotBy != null">snapshot_by,</if>
            <if test="snapshotTime != null">snapshot_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionId != null">#{versionId},</if>
            <if test="dataItemId != null">#{dataItemId},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="dataSystemId != null">#{dataSystemId},</if>
            <if test="dataName != null and dataName != ''">#{dataName},</if>
            <if test="dataLevel != null">#{dataLevel},</if>
            <if test="categoryLevel1 != null">#{categoryLevel1},</if>
            <if test="categoryLevel2 != null">#{categoryLevel2},</if>
            <if test="categoryLevel3 != null">#{categoryLevel3},</if>
            <if test="categoryLevel4 != null">#{categoryLevel4},</if>
            <if test="dataCarrier != null">#{dataCarrier},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="dataAmountGb != null">#{dataAmountGb},</if>
            <if test="dataAmountCount != null">#{dataAmountCount},</if>
            <if test="coverageType != null">#{coverageType},</if>
            <if test="coverageRatio != null">#{coverageRatio},</if>
            <if test="dataAccuracy != null">#{dataAccuracy},</if>
            <if test="description != null">#{description},</if>
            <if test="processorName != null">#{processorName},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="processorNature != null">#{processorNature},</if>
            <if test="industry != null">#{industry},</if>
            <if test="mainBusiness != null">#{mainBusiness},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPosition != null">#{contactPosition},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="processingMethod != null">#{processingMethod},</if>
            <if test="processingPurpose != null">#{processingPurpose},</if>
            <if test="isAlgorithmAuto != null">#{isAlgorithmAuto},</if>
            <if test="isCrossBorder != null">#{isCrossBorder},</if>
            <if test="crossBorderReceiver != null">#{crossBorderReceiver},</if>
            <if test="crossBorderMethod != null">#{crossBorderMethod},</if>
            <if test="isCrossBorderAssess != null">#{isCrossBorderAssess},</if>
            <if test="crossBorderAssessResult != null">#{crossBorderAssessResult},</if>
            <if test="isExternalShare != null">#{isExternalShare},</if>
            <if test="externalShareReceiver != null">#{externalShareReceiver},</if>
            <if test="externalShareMethod != null">#{externalShareMethod},</if>
            <if test="isCrossSubjectFlow != null">#{isCrossSubjectFlow},</if>
            <if test="isForeignData != null">#{isForeignData},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="systemType != null">#{systemType},</if>
            <if test="systemIp != null">#{systemIp},</if>
            <if test="systemDomain != null">#{systemDomain},</if>
            <if test="cyberProtectLevel != null">#{cyberProtectLevel},</if>
            <if test="networkProtectRecord != null">#{networkProtectRecord},</if>
            <if test="isCriticalInfra != null">#{isCriticalInfra},</if>
            <if test="isDataRiskAssess != null">#{isDataRiskAssess},</if>
            <if test="assessOrg != null">#{assessOrg},</if>
            <if test="assessStandard != null">#{assessStandard},</if>
            <if test="assessTime != null">#{assessTime},</if>
            <if test="assessConclusion != null">#{assessConclusion},</if>
            <if test="dutyDept != null">#{dutyDept},</if>
            <if test="dutyUser != null">#{dutyUser},</if>
            <if test="remark != null">#{remark},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="snapshotBy != null">#{snapshotBy},</if>
            <if test="snapshotTime != null">#{snapshotTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataItemSnapshot" parameterType="DataItemSnapshot">
        update dsa_data_item_snapshot
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="dataItemId != null">data_item_id = #{dataItemId},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="dataSystemId != null">data_system_id = #{dataSystemId},</if>
            <if test="dataName != null and dataName != ''">data_name = #{dataName},</if>
            <if test="dataLevel != null">data_level = #{dataLevel},</if>
            <if test="categoryLevel1 != null">category_level1 = #{categoryLevel1},</if>
            <if test="categoryLevel2 != null">category_level2 = #{categoryLevel2},</if>
            <if test="categoryLevel3 != null">category_level3 = #{categoryLevel3},</if>
            <if test="categoryLevel4 != null">category_level4 = #{categoryLevel4},</if>
            <if test="dataCarrier != null">data_carrier = #{dataCarrier},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="dataAmountGb != null">data_amount_gb = #{dataAmountGb},</if>
            <if test="dataAmountCount != null">data_amount_count = #{dataAmountCount},</if>
            <if test="coverageType != null">coverage_type = #{coverageType},</if>
            <if test="coverageRatio != null">coverage_ratio = #{coverageRatio},</if>
            <if test="dataAccuracy != null">data_accuracy = #{dataAccuracy},</if>
            <if test="description != null">description = #{description},</if>
            <if test="processorName != null">processor_name = #{processorName},</if>
            <if test="orgCode != null">org_code = #{orgCode},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="processorNature != null">processor_nature = #{processorNature},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="mainBusiness != null">main_business = #{mainBusiness},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPosition != null">contact_position = #{contactPosition},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="processingMethod != null">processing_method = #{processingMethod},</if>
            <if test="processingPurpose != null">processing_purpose = #{processingPurpose},</if>
            <if test="isAlgorithmAuto != null">is_algorithm_auto = #{isAlgorithmAuto},</if>
            <if test="isCrossBorder != null">is_cross_border = #{isCrossBorder},</if>
            <if test="crossBorderReceiver != null">cross_border_receiver = #{crossBorderReceiver},</if>
            <if test="crossBorderMethod != null">cross_border_method = #{crossBorderMethod},</if>
            <if test="isCrossBorderAssess != null">is_cross_border_assess = #{isCrossBorderAssess},</if>
            <if test="crossBorderAssessResult != null">cross_border_assess_result = #{crossBorderAssessResult},</if>
            <if test="isExternalShare != null">is_external_share = #{isExternalShare},</if>
            <if test="externalShareReceiver != null">external_share_receiver = #{externalShareReceiver},</if>
            <if test="externalShareMethod != null">external_share_method = #{externalShareMethod},</if>
            <if test="isCrossSubjectFlow != null">is_cross_subject_flow = #{isCrossSubjectFlow},</if>
            <if test="isForeignData != null">is_foreign_data = #{isForeignData},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="systemType != null">system_type = #{systemType},</if>
            <if test="systemIp != null">system_ip = #{systemIp},</if>
            <if test="systemDomain != null">system_domain = #{systemDomain},</if>
            <if test="cyberProtectLevel != null">cyber_protect_level = #{cyberProtectLevel},</if>
            <if test="networkProtectRecord != null">network_protect_record = #{networkProtectRecord},</if>
            <if test="isCriticalInfra != null">is_critical_infra = #{isCriticalInfra},</if>
            <if test="isDataRiskAssess != null">is_data_risk_assess = #{isDataRiskAssess},</if>
            <if test="assessOrg != null">assess_org = #{assessOrg},</if>
            <if test="assessStandard != null">assess_standard = #{assessStandard},</if>
            <if test="assessTime != null">assess_time = #{assessTime},</if>
            <if test="assessConclusion != null">assess_conclusion = #{assessConclusion},</if>
            <if test="dutyDept != null">duty_dept = #{dutyDept},</if>
            <if test="dutyUser != null">duty_user = #{dutyUser},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="snapshotBy != null">snapshot_by = #{snapshotBy},</if>
            <if test="snapshotTime != null">snapshot_time = #{snapshotTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemSnapshotById" parameterType="Long">
        delete from dsa_data_item_snapshot where id = #{id}
    </delete>

    <delete id="deleteDataItemSnapshotByIds" parameterType="String">
        delete from dsa_data_item_snapshot where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>