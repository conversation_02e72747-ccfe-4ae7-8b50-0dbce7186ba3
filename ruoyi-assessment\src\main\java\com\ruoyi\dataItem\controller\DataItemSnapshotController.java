package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemSnapshot;
import com.ruoyi.dataItem.service.IDataItemSnapshotService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据项快照Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/snapshot")
@Api(value = "数据项快照控制器", tags = {"数据项快照管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemSnapshotController extends BaseController
{
    private final IDataItemSnapshotService dataItemSnapshotService;

    /**
     * 查询数据项快照列表
     */
    @ApiOperation("查询数据项快照列表")
    @PreAuthorize("@ss.hasPermi('dataItem:snapshot:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataItemSnapshot dataItemSnapshot) {
        startPage();
        List<DataItemSnapshot> list = dataItemSnapshotService.list(new QueryWrapper<DataItemSnapshot>(dataItemSnapshot));
        return getDataTable(list);
    }

    /**
     * 获取数据项快照详细信息
     */
    @ApiOperation("获取数据项快照详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:snapshot:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemSnapshotService.getById(id));
    }

    /**
     * 新增数据项快照
     */
    @ApiOperation("新增数据项快照")
    @PreAuthorize("@ss.hasPermi('dataItem:snapshot:add')")
    @Log(title = "数据项快照", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataItemSnapshot dataItemSnapshot) {
        return toAjax(dataItemSnapshotService.save(dataItemSnapshot));
    }

    /**
     * 修改数据项快照
     */
    @ApiOperation("修改数据项快照")
    @PreAuthorize("@ss.hasPermi('dataItem:snapshot:edit')")
    @Log(title = "数据项快照", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemSnapshot dataItemSnapshot) {
        return toAjax(dataItemSnapshotService.updateById(dataItemSnapshot));
    }

    /**
     * 删除数据项快照
     */
    @ApiOperation("删除数据项快照")
    @PreAuthorize("@ss.hasPermi('dataItem:snapshot:remove')")
    @Log(title = "数据项快照", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemSnapshotService.removeByIds(Arrays.asList(ids)));
    }
}