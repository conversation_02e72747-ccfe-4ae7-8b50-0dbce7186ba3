package com.ruoyi.evaluateModel.service;

import com.ruoyi.evaluateModel.domain.EvaluateType;
import com.baomidou.mybatisplus.extension.service.IService;


import java.util.List;

/**
 * 评估类型Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IEvaluateTypeService extends IService<EvaluateType> {

    /**
     * 查询评估类型列表并关联对应的评估模型（每个类型只关联一个模型）
     * @param evaluateType 查询条件
     * @return 评估类型列表（包含关联的模型）
     */
    List<EvaluateType> listWithModel(EvaluateType evaluateType);

}
