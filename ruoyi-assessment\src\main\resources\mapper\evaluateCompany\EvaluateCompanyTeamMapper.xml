<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateCompany.mapper.EvaluateCompanyTeamMapper">
    
    <resultMap type="EvaluateCompanyTeam" id="EvaluateCompanyTeamResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateCompanyTeamVo">
        select id, org_id, team_name, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_company_team
    </sql>

    <select id="selectEvaluateCompanyTeamList" parameterType="EvaluateCompanyTeam" resultMap="EvaluateCompanyTeamResult">
        <include refid="selectEvaluateCompanyTeamVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateCompanyTeamById" parameterType="Long" resultMap="EvaluateCompanyTeamResult">
        <include refid="selectEvaluateCompanyTeamVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateCompanyTeam" parameterType="EvaluateCompanyTeam" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_company_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateCompanyTeam" parameterType="EvaluateCompanyTeam">
        update dsa_evaluate_company_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateCompanyTeamById" parameterType="Long">
        delete from dsa_evaluate_company_team where id = #{id}
    </delete>

    <delete id="deleteEvaluateCompanyTeamByIds" parameterType="String">
        delete from dsa_evaluate_company_team where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>