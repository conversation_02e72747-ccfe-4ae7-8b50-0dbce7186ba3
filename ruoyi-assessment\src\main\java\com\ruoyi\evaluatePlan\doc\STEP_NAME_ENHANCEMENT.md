# 步骤名称显示优化

## 优化内容

将异常信息和日志中的通用"第一个步骤"替换为具体的步骤名称，提高错误信息的可读性和调试效率。

## 修改的方法

### 1. setFirstStepNextConfigIfNeeded 方法
**优化前**：
```java
throw new ServiceException("未设置第一个步骤的下一步配置");
log.info("设置第一个步骤下一步配置成功，流程实例ID: {}", processInstanceId);
```

**优化后**：
```java
String firstStepName = getFirstStepName(processInstanceId);
throw new ServiceException("未设置" + firstStepName + "的下一步配置");
log.info("设置{}下一步配置成功，流程实例ID: {}", firstStepName, processInstanceId);
```

### 2. setFirstStepNextConfig 方法
**优化前**：
```java
throw new ServiceException("设置第一个步骤下一步配置失败");
log.info("成功设置第一个步骤下一步配置，步骤实例ID: {}, 下一步负责人: {}, 下一步部门: {}",
        firstStep.getId(), dto.getNextStepAssignee(), dto.getNextStepDept());
```

**优化后**：
```java
String stepName = StringUtils.isNotEmpty(firstStep.getStepName()) ? 
                 "\"" + firstStep.getStepName() + "\"步骤" : "第一个步骤";
throw new ServiceException("设置" + stepName + "下一步配置失败");
log.info("成功设置{}下一步配置，步骤实例ID: {}, 下一步负责人: {}, 下一步部门: {}",
        stepName, firstStep.getId(), dto.getNextStepAssignee(), dto.getNextStepDept());
```

### 3. 新增 getFirstStepName 方法
```java
/**
 * 获取第一个步骤名称
 */
private String getFirstStepName(Long processInstanceId) {
    try {
        List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);
        if (stepInstances != null && !stepInstances.isEmpty()) {
            ProcessStepInstance firstStep = stepInstances.get(0);
            return StringUtils.isNotEmpty(firstStep.getStepName()) ? 
                   "\"" + firstStep.getStepName() + "\"步骤" : "第一个步骤";
        }
    } catch (Exception e) {
        log.warn("获取第一个步骤名称失败，流程实例ID: {}, 异常: {}", processInstanceId, e.getMessage());
    }
    return "第一个步骤";
}
```

## 优化效果

### 异常信息对比

**优化前**：
```
未设置第一个步骤的下一步配置
设置第一个步骤下一步配置失败
```

**优化后**：
```
未设置"数据识别"步骤的下一步配置
设置"数据识别"步骤下一步配置失败
```

### 日志信息对比

**优化前**：
```
设置第一个步骤下一步配置成功，流程实例ID: 1001
成功设置第一个步骤下一步配置，步骤实例ID: 2001, 下一步负责人: 张三, 下一步部门: 技术部
```

**优化后**：
```
设置"数据识别"步骤下一步配置成功，流程实例ID: 1001
成功设置"数据识别"步骤下一步配置，步骤实例ID: 2001, 下一步负责人: 张三, 下一步部门: 技术部
```

## 容错处理

### 1. 步骤名称获取失败
- 当无法获取步骤名称时，自动回退到"第一个步骤"
- 记录警告日志，但不影响主流程

### 2. 步骤名称为空
- 当步骤名称为空或null时，使用"第一个步骤"作为默认值
- 确保异常信息的完整性

### 3. 异常处理
- 获取步骤名称的过程中如果出现异常，不会影响主要的业务逻辑
- 异常会被捕获并记录，然后使用默认名称

## 命名规范

### 步骤名称格式
- **有具体名称**：`"数据识别"步骤`
- **无具体名称**：`第一个步骤`

### 引号使用
- 具体的步骤名称用双引号包围，便于区分
- 通用名称不使用引号

## 使用示例

### 数据安全评估流程
假设第一个步骤名称为"数据识别"：

```java
// 创建评估计划时
EvaluatePlanTaskDto dto = new EvaluatePlanTaskDto();
dto.setName("数据安全评估任务");
dto.setModelId(1001L);
// 未设置下一步配置字段

// 调用创建方法
Long taskId = evaluatePlanTaskService.createPlanTask(dto);

// 异常信息将显示：
// "未设置"数据识别"步骤的下一步配置"
```

### 正常设置配置
```java
EvaluatePlanTaskDto dto = new EvaluatePlanTaskDto();
dto.setName("数据安全评估任务");
dto.setModelId(1001L);
dto.setNextStepAssignee("张三");
dto.setNextStepDept("技术部");
dto.setNextStepDeadlineDays(3);
dto.setSetTaskDeadline(1);

// 调用创建方法
Long taskId = evaluatePlanTaskService.createPlanTask(dto);

// 日志信息将显示：
// "设置"数据识别"步骤下一步配置成功，流程实例ID: 1001"
```

## 优势

1. **错误信息更具体**：用户能够清楚知道是哪个具体步骤出现了问题
2. **调试更容易**：开发人员能够快速定位问题所在的步骤
3. **用户体验更好**：业务用户能够理解错误信息的含义
4. **日志更有价值**：运维人员能够从日志中获得更多有用信息
5. **向后兼容**：当无法获取具体步骤名称时，仍然显示通用信息

## 注意事项

1. **性能影响**：每次需要显示步骤名称时都会查询数据库，但影响很小
2. **异常安全**：获取步骤名称的过程不会影响主要业务逻辑
3. **一致性**：所有相关的异常信息和日志都使用相同的命名格式
4. **可扩展性**：这种模式可以应用到其他类似的场景中
