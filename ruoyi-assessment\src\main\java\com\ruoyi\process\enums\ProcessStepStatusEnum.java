package com.ruoyi.process.enums;

/**
 * 流程步骤状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public enum ProcessStepStatusEnum {

    /**
     * 待执行
     */
    PENDING(0, "待执行"),

    /**
     * 处理中
     */
    RUNNING(1, "处理中"),

    /**
     * 完成
     */
    FINISHED(2, "完成");

    /** 流程步骤状态流程步骤状态 */
    private final Integer code;

    /** 流程步骤状态名称 */
    private final String name;

    ProcessStepStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据流程步骤状态获取枚举
     * @param code 流程步骤状态
     * @return 枚举值
     */
    public static ProcessStepStatusEnum getByCode(Integer code) {
        for (ProcessStepStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据流程步骤状态获取名称
     * @param code 流程步骤状态
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        ProcessStepStatusEnum type = getByCode(code);
        return type != null ? type.getName() : null;
    }

    /**
     * 验证流程步骤状态是否有效
     * @param code 流程步骤状态
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
