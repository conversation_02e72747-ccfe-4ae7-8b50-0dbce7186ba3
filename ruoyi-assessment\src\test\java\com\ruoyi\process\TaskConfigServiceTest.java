package com.ruoyi.process;

import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;
import com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.service.ITaskConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务配置服务测试类
 * 测试重构后的任务管理功能，确保与流程控制模块的集成正常工作
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@SpringBootTest
public class TaskConfigServiceTest {

    @Autowired
    private ITaskConfigService taskConfigService;

    @Autowired
    private DataSecurityTaskManager dataSecurityTaskManager;

    /**
     * 测试获取任务配置
     */
    @Test
    public void testGetTaskConfig() {
        System.out.println("=== 测试获取任务配置 ===");
        
        // 测试获取数据收集任务配置
        ProcessStepDefinition taskConfig = taskConfigService.getTaskConfig("data_security_plan", "data_identify");
        if (taskConfig != null) {
            System.out.println("步骤编码: " + taskConfig.getStepCode());
            System.out.println("任务名称: " + taskConfig.getStepName());
            System.out.println("任务描述: " + taskConfig.getDescription());
            System.out.println("处理类型: " + taskConfig.getHandlerType());
            System.out.println("状态: " + (taskConfig.getStatus() == 1 ? "正常" : "禁用"));
        } else {
            System.out.println("未找到任务配置");
        }
    }

    /**
     * 测试获取流程的所有任务配置
     */
    @Test
    public void testGetTaskConfigsByProcess() {
        System.out.println("=== 测试获取流程的所有任务配置 ===");
        
        List<ProcessStepDefinition> taskConfigs = taskConfigService.getTaskConfigsByProcess("data_security_plan");
        System.out.println("找到 " + taskConfigs.size() + " 个任务配置:");
        
        for (ProcessStepDefinition config : taskConfigs) {
            System.out.println("- 步骤: " + config.getStepName() +
                             " | 编码: " + config.getStepCode() +
                             " | 状态: " + (config.getStatus() == 1 ? "正常" : "禁用"));
        }
    }



    /**
     * 测试获取默认任务参数
     */
    @Test
    public void testGetDefaultTaskParams() {
        System.out.println("=== 测试获取默认任务参数 ===");
        
        String[] stepCodes = {"data_identify", "risk_assess", "control_design", "report_generate"};
        
        for (String stepCode : stepCodes) {
            System.out.println("步骤编码: " + stepCode);
            Map<String, Object> defaultParams = taskConfigService.getDefaultTaskParams(stepCode);
            defaultParams.forEach((key, value) ->
                System.out.println("  " + key + ": " + value));
            System.out.println();
        }
    }

    /**
     * 测试任务参数验证
     */
    @Test
    public void testValidateTaskParams() {
        System.out.println("=== 测试任务参数验证 ===");
        
        // 测试有效参数
        Map<String, Object> validParams = new HashMap<>();
        validParams.put("dataScope", "all");
        validParams.put("includeBackup", true);

        boolean isValid = taskConfigService.validateTaskParams("data_identify", validParams);
        System.out.println("有效参数验证结果: " + isValid);

        // 测试无效参数
        Map<String, Object> invalidParams = new HashMap<>();
        invalidParams.put("dataScope", "invalid_scope");

        boolean isInvalid = taskConfigService.validateTaskParams("data_identify", invalidParams);
        System.out.println("无效参数验证结果: " + isInvalid);
    }

    /**
     * 测试获取支持的参数列表
     */
    @Test
    public void testGetSupportedParams() {
        System.out.println("=== 测试获取支持的参数列表 ===");
        
        String[] stepCodes = {"data_identify", "risk_assess", "control_design", "report_generate"};

        for (String stepCode : stepCodes) {
            List<String> supportedParams = taskConfigService.getSupportedParams(stepCode);
            System.out.println(stepCode + " 支持的参数: " + supportedParams);
        }
    }

    /**
     * 测试获取可用步骤编码
     */
    @Test
    public void testGetAvailableStepCodes() {
        System.out.println("=== 测试获取可用步骤编码 ===");

        List<String> stepCodes = taskConfigService.getAvailableStepCodes();
        System.out.println("可用步骤编码: " + stepCodes);
    }

    /**
     * 测试DataSecurityTaskManager重构后的功能
     */
    @Test
    public void testDataSecurityTaskManager() {
        System.out.println("=== 测试DataSecurityTaskManager重构后的功能 ===");
        
        // 测试获取可用任务列表
        List<Map<String, Object>> availableTasks = dataSecurityTaskManager.getAvailableTasks();
        System.out.println("可用任务数量: " + availableTasks.size());
        
        for (Map<String, Object> task : availableTasks) {
            System.out.println("任务: " + task.get("taskName") + 
                             " | 类型: " + task.get("taskType") + 
                             " | 状态: " + task.get("status") + 
                             " | 时间: " + task.get("estimatedDuration"));
        }
        
        // 测试任务执行
        System.out.println("\n=== 测试任务执行 ===");
        EvaluatePlanRequest request = new EvaluatePlanRequest();
        request.setPlanId(1006L);
        request.setEvaluateType("data_security_plan");
        request.setTargetCompanyId(1001L);
        
        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("dataScope", "sensitive_only");
        taskParams.put("includeBackup", true);
        
        try {
            EvaluatePlanResponse response = dataSecurityTaskManager.executeTask(request, "data_collection", taskParams);
            System.out.println("任务执行结果: " + response.getSuccess());
            System.out.println("任务进度: " + response.getProgress() + "%");
            System.out.println("执行结果: " + response.getResultData());
        } catch (Exception e) {
            System.out.println("任务执行异常: " + e.getMessage());
        }
        
        // 测试参数验证
        System.out.println("\n=== 测试参数验证 ===");
        boolean validationResult = dataSecurityTaskManager.validateTaskParams("data_collection", taskParams);
        System.out.println("参数验证结果: " + validationResult);
    }

    /**
     * 测试缓存功能
     */
    @Test
    public void testCacheFunction() {
        System.out.println("=== 测试缓存功能 ===");
        
        // 第一次获取（从数据库）
        long startTime = System.currentTimeMillis();
        ProcessStepDefinition config1 = taskConfigService.getTaskConfig("data_security_plan", "data_identify");
        long firstTime = System.currentTimeMillis() - startTime;
        System.out.println("第一次获取耗时: " + firstTime + "ms");
        
        // 第二次获取（从缓存）
        startTime = System.currentTimeMillis();
        ProcessStepDefinition config2 = taskConfigService.getTaskConfig("data_security_plan", "data_identify");
        long secondTime = System.currentTimeMillis() - startTime;
        System.out.println("第二次获取耗时: " + secondTime + "ms");
        
        System.out.println("缓存效果: " + (config1 != null && config2 != null && firstTime > secondTime ? "有效" : "无效"));
        
        // 刷新缓存
        taskConfigService.refreshTaskConfigCache();
        System.out.println("缓存已刷新");
    }
}
