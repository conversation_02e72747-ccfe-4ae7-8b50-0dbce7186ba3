package com.ruoyi.common.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

/**
 * 通用暂存功能控制器
 * 提供统一的暂存数据管理接口，支持多种业务类型
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/common/draft")
@Api(value = "通用暂存控制器", tags = {"暂存数据管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DraftStorageController extends BaseController {
    
    private final IDraftStorageService draftStorageService;

    /**
     * 暂存数据
     */
    @ApiOperation("暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:save')")
    @Log(title = "暂存数据", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult saveDraft(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                               @RequestParam("draftKey") @ApiParam("暂存键") String draftKey,
                               @RequestBody @ApiParam("暂存数据") Object data,
                               @RequestParam(value = "expireHours", defaultValue = "24") @ApiParam("过期时间（小时）") int expireHours) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean success = draftStorageService.saveDraft(
                    businessType,
                    userId,
                    draftKey,
                    data,
                    expireHours,
                    TimeUnit.HOURS
            );
            
            if (success) {
                return AjaxResult.success("暂存成功");
            } else {
                return AjaxResult.error("暂存失败");
            }
        } catch (Exception e) {
            return handleException(e, "暂存数据失败");
        }
    }

    /**
     * 获取暂存数据
     */
    @ApiOperation("获取暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:query')")
    @GetMapping("/get")
    public AjaxResult getDraft(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                              @RequestParam("draftKey") @ApiParam("暂存键") String draftKey,
                              @RequestParam(value = "dataType", defaultValue = "Object") @ApiParam("数据类型") String dataType) {
        try {
            Long userId = SecurityUtils.getUserId();
            
            // 根据数据类型获取对应的Class
            Class<?> clazz = getDataTypeClass(dataType);
            
            Object data = draftStorageService.getDraft(businessType, userId, draftKey, clazz);
            
            if (data != null) {
                return AjaxResult.success("获取成功", data);
            } else {
                return AjaxResult.error("暂存数据不存在或已过期");
            }
        } catch (Exception e) {
            return handleException(e, "获取暂存数据失败");
        }
    }

    /**
     * 删除暂存数据
     */
    @ApiOperation("删除暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:remove')")
    @Log(title = "删除暂存数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public AjaxResult deleteDraft(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                                 @RequestParam("draftKey") @ApiParam("暂存键") String draftKey) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean success = draftStorageService.deleteDraft(businessType, userId, draftKey);
            
            if (success) {
                return AjaxResult.success("删除成功");
            } else {
                return AjaxResult.error("删除失败");
            }
        } catch (Exception e) {
            return handleException(e, "删除暂存数据失败");
        }
    }

    /**
     * 获取用户暂存数据列表
     */
    @ApiOperation("获取用户暂存数据列表")
    @PreAuthorize("@ss.hasPermi('common:draft:list')")
    @GetMapping("/list")
    public AjaxResult getDraftList(@RequestParam("businessType") @ApiParam("业务类型") String businessType) {
        try {
            Long userId = SecurityUtils.getUserId();
            Map<String, Map<String, Object>> draftSummary = draftStorageService.getUserDraftSummary(businessType, userId);
            
            return AjaxResult.success("获取成功", draftSummary);
        } catch (Exception e) {
            return handleException(e, "获取暂存数据列表失败");
        }
    }

    /**
     * 清理用户暂存数据
     */
    @ApiOperation("清理用户暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:clear')")
    @Log(title = "清理暂存数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/clear")
    public AjaxResult clearDrafts(@RequestParam("businessType") @ApiParam("业务类型") String businessType) {
        try {
            Long userId = SecurityUtils.getUserId();
            int count = draftStorageService.clearUserDrafts(businessType, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("clearedCount", count);
            
            return AjaxResult.success("清理完成", result);
        } catch (Exception e) {
            return handleException(e, "清理暂存数据失败");
        }
    }

    /**
     * 检查暂存数据是否存在
     */
    @ApiOperation("检查暂存数据是否存在")
    @PreAuthorize("@ss.hasPermi('common:draft:query')")
    @GetMapping("/exists")
    public AjaxResult existsDraft(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                                 @RequestParam("draftKey") @ApiParam("暂存键") String draftKey) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean exists = draftStorageService.existsDraft(businessType, userId, draftKey);
            
            Map<String, Object> result = new HashMap<>();
            result.put("exists", exists);
            
            if (exists) {
                // 获取剩余过期时间
                long ttl = draftStorageService.getDraftTtl(businessType, userId, draftKey);
                result.put("ttl", ttl);
            }
            
            return AjaxResult.success("检查完成", result);
        } catch (Exception e) {
            return handleException(e, "检查暂存数据失败");
        }
    }

    /**
     * 批量暂存数据
     */
    @ApiOperation("批量暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:save')")
    @Log(title = "批量暂存数据", businessType = BusinessType.INSERT)
    @PostMapping("/batch/save")
    public AjaxResult batchSaveDrafts(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                                     @RequestBody @ApiParam("暂存数据Map") Map<String, Object> drafts,
                                     @RequestParam(value = "expireHours", defaultValue = "24") @ApiParam("过期时间（小时）") int expireHours) {
        try {
            Long userId = SecurityUtils.getUserId();
            int count = draftStorageService.batchSaveDrafts(businessType, userId, drafts, expireHours, TimeUnit.HOURS);
            
            Map<String, Object> result = new HashMap<>();
            result.put("savedCount", count);
            result.put("totalCount", drafts.size());
            
            return AjaxResult.success("批量暂存完成", result);
        } catch (Exception e) {
            return handleException(e, "批量暂存数据失败");
        }
    }

    /**
     * 批量删除暂存数据
     */
    @ApiOperation("批量删除暂存数据")
    @PreAuthorize("@ss.hasPermi('common:draft:remove')")
    @Log(title = "批量删除暂存数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/delete")
    public AjaxResult batchDeleteDrafts(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                                       @RequestBody @ApiParam("暂存键列表") java.util.List<String> draftKeys) {
        try {
            Long userId = SecurityUtils.getUserId();
            int count = draftStorageService.batchDeleteDrafts(businessType, userId, draftKeys);
            
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", count);
            result.put("totalCount", draftKeys.size());
            
            return AjaxResult.success("批量删除完成", result);
        } catch (Exception e) {
            return handleException(e, "批量删除暂存数据失败");
        }
    }

    /**
     * 更新暂存数据过期时间
     */
    @ApiOperation("更新暂存数据过期时间")
    @PreAuthorize("@ss.hasPermi('common:draft:edit')")
    @Log(title = "更新暂存数据过期时间", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTtl")
    public AjaxResult updateDraftTtl(@RequestParam("businessType") @ApiParam("业务类型") String businessType,
                                    @RequestParam("draftKey") @ApiParam("暂存键") String draftKey,
                                    @RequestParam("expireHours") @ApiParam("新的过期时间（小时）") int expireHours) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean success = draftStorageService.updateDraftTtl(businessType, userId, draftKey, expireHours, TimeUnit.HOURS);
            
            if (success) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            return handleException(e, "更新暂存数据过期时间失败");
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据数据类型字符串获取对应的Class
     */
    private Class<?> getDataTypeClass(String dataType) {
        switch (dataType.toLowerCase()) {
            case "string":
                return String.class;
            case "integer":
            case "int":
                return Integer.class;
            case "long":
                return Long.class;
            case "double":
                return Double.class;
            case "boolean":
                return Boolean.class;
            case "map":
                return Map.class;
            case "list":
                return java.util.List.class;
            default:
                return Object.class;
        }
    }
}
