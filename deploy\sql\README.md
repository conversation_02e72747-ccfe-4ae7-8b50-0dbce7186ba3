# 数据库初始化脚本目录

## 📁 目录说明

此目录用于存放MySQL数据库初始化脚本。容器启动时，会自动执行此目录下的SQL脚本。

## 📋 使用方法

### 1. 脚本执行顺序
脚本按文件名的字母顺序执行，建议使用数字前缀：
```
01_create_database.sql
02_create_tables.sql
03_insert_data.sql
```

### 2. 支持的文件类型
- `.sql` - SQL脚本文件
- `.sql.gz` - 压缩的SQL脚本文件
- `.sh` - Shell脚本文件

### 3. 脚本示例

#### 创建数据库脚本 (01_create_database.sql)
```sql
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS data_security_v3 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE data_security_v3;
```

#### 创建用户脚本 (02_create_user.sql)
```sql
-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'data_security_v3'@'%' IDENTIFIED BY 'dKSaKe3aRTs7xAhb';

-- 授权
GRANT ALL PRIVILEGES ON data_security_v3.* TO 'data_security_v3'@'%';
FLUSH PRIVILEGES;
```

## 📝 注意事项

1. **文件编码**: 确保SQL文件使用UTF-8编码
2. **权限问题**: 脚本以root用户身份执行
3. **错误处理**: 如果脚本执行失败，容器启动会失败
4. **幂等性**: 脚本应该支持重复执行

## 🔧 从项目复制脚本

如果您的项目中已有SQL脚本，可以复制到此目录：

```bash
# 复制项目中的SQL文件
cp ../sql/*.sql ./

# 或者创建软链接
ln -s ../sql/*.sql ./
```

## 🚀 自动执行

当MySQL容器首次启动时，会自动执行此目录下的所有脚本。执行日志可以通过以下命令查看：

```bash
# 查看MySQL容器日志
docker-compose logs mysql
```

## ⚠️ 重要提醒

- 此目录下的脚本只在容器**首次启动**时执行
- 如果需要重新执行脚本，需要删除MySQL数据卷：`docker-compose down -v`
- 生产环境请谨慎使用自动初始化脚本
