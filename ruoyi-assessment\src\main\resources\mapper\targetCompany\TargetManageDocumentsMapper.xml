<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.company.mapper.TargetManageDocumentsMapper">
    
    <resultMap type="TargetManageDocuments" id="TargetManageDocumentsResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="documentName"    column="document_name"    />
        <result property="content"    column="content"    />
        <result property="scope"    column="scope"    />
        <result property="processActivityId"    column="process_activity_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTargetManageDocumentsVo">
        select id, org_id, document_name, content, scope, process_activity_id, status, create_by, create_time, update_by, update_time, remark, del_flag from dsa_target_manage_documents
    </sql>

    <select id="selectTargetManageDocumentsList" parameterType="TargetManageDocuments" resultMap="TargetManageDocumentsResult">
        <include refid="selectTargetManageDocumentsVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="documentName != null  and documentName != ''"> and document_name like concat('%', #{documentName}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="scope != null  and scope != ''"> and scope = #{scope}</if>
            <if test="processActivityId != null  and processActivityId != ''"> and process_activity_id = #{processActivityId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTargetManageDocumentsById" parameterType="Long" resultMap="TargetManageDocumentsResult">
        <include refid="selectTargetManageDocumentsVo"/>
        where id = #{id}
    </select>

    <insert id="insertTargetManageDocuments" parameterType="TargetManageDocuments" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_target_manage_documents
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="documentName != null">document_name,</if>
            <if test="content != null">content,</if>
            <if test="scope != null">scope,</if>
            <if test="processActivityId != null">process_activity_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="documentName != null">#{documentName},</if>
            <if test="content != null">#{content},</if>
            <if test="scope != null">#{scope},</if>
            <if test="processActivityId != null">#{processActivityId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTargetManageDocuments" parameterType="TargetManageDocuments">
        update dsa_target_manage_documents
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="documentName != null">document_name = #{documentName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="processActivityId != null">process_activity_id = #{processActivityId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTargetManageDocumentsById" parameterType="Long">
        delete from dsa_target_manage_documents where id = #{id}
    </delete>

    <delete id="deleteTargetManageDocumentsByIds" parameterType="String">
        delete from dsa_target_manage_documents where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>