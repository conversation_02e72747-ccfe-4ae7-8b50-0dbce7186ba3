package com.ruoyi.process.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 流程定义对象 dsa_process_definition
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_process_definition")
public class ProcessDefinition extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 流程名称 */
    @Excel(name = "流程名称")
    @TableField(value = "name")
    private String name;

    /** 流程编码，唯一 */
    @Excel(name = "流程编码，唯一")
    @TableField(value = "code")
    private String code;

    /** 流程描述 */
    @Excel(name = "流程描述")
    @TableField(value = "description")
    private String description;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}