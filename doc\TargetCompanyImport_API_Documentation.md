# 被评估单位数据导入功能 API 文档（多Sheet版本）

## 概述

在 `TargetCompanyInfoController` 控制器中实现了支持多Sheet Excel文件导入的功能，使用EasyExcel和对应的listener处理每个sheet：

**导入顺序：**
1. Sheet1: 公司信息的一部分（基本信息）
2. Sheet0: 公司信息的另一部分（扩展信息）
3. Sheet2: 部门信息
4. Sheet3: 人员信息
5. Sheet4: 文档信息

## API 接口

### 导入包含5个sheet的Excel文件

**接口地址：** `POST /targetCompany/companyInfo/importMultiSheetExcel`

**权限要求：** `targetCompany:companyInfo:import`

**请求参数：**
- `file`: MultipartFile - 包含5个sheet的Excel文件

**Excel文件结构：**
- **Sheet0**: 公司信息的另一部分（扩展信息）
- **Sheet1**: 公司信息的一部分（基本信息）
- **Sheet2**: 部门信息
- **Sheet3**: 人员信息
- **Sheet4**: 文档信息

**返回示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 25,
    "totalFailure": 1,
    "message": "多Sheet导入完成。总计成功：25条，失败：1条。详情：公司信息：成功5条，失败0条；部门信息：成功3条，失败1条；人员信息：成功10条，失败0条；文档信息：成功7条，失败0条；"
  }
}
```

## 模板下载接口

### 1. 下载被评估单位基本信息导入模板

**接口地址：** `POST /targetCompany/companyInfo/importCompanyTemplate`

**返回：** Excel模板文件下载

### 2. 下载部门信息导入模板

**接口地址：** `POST /targetCompany/companyInfo/importDeptTemplate`

**返回：** Excel模板文件下载

### 3. 下载人员信息导入模板

**接口地址：** `POST /targetCompany/companyInfo/importMemberTemplate`

**返回：** Excel模板文件下载

### 4. 下载文档信息导入模板

**接口地址：** `POST /targetCompany/companyInfo/importDocumentsTemplate`

**返回：** Excel模板文件下载

## Excel 文件格式要求

### Sheet1: 公司信息基本部分 (ExcelTargetCompanyInfo1)
字段列表（按列索引顺序）：
- 列0: 公司名称 (companyName)
- 列1: 单位简称 (simpleCompanyName)
- 列2: 公司地址 (companyAddress)
- 列3: 邮政编码 (postCode)
- 列4: 联系人姓名 (contactName)
- 列5: 职务 (job)
- 列6: 部门 (dept)
- 列7: 电话 (tel)
- 列8: 手机号码 (mobile)
- 列9: 邮箱 (email)
- 列10: 注册地 (registerPlace)
- 列11: 统一社会信用代码 (creditCode)
- 列12: 组织类型 (orgType)
- 列13: 法定代表人 (legalRepresentative)
- 列14: 分支机构 (branchUnit)
- 列15: 运营控制 (operationControl)
- 列16: 人员情况 (personnelSituation)
- 列17: 经营范围 (businessScope)
- 列18: 上市情况 (listingSituation)

### Sheet0: 联系人信息 (ExcelTargetCompanyInfo0)
字段列表（按列索引顺序）：
- 列0: 姓名 (name) - 对应公司联系人姓名
- 列1: 职务/职称 (position) - 对应公司联系人职务
- 列2: 所属部门 (department) - 对应公司联系人部门
- 列3: 办公电话 (officePhone) - 对应公司电话
- 列4: 移动电话 (mobilePhone) - 对应公司移动电话
- 列5: 电子邮件 (email) - 对应公司邮箱

### Sheet2: 部门信息 (ExcelTargetOrgDeptInfo)
字段列表（按列索引顺序）：
- 列0: 所属单位ID (orgId) - 必须是有效的公司ID
- 列1: 部门名称 (departmentName)
- 列2: 部门职责 (departmentDuty)
- 列3: 部门负责人 (departmentLeader)
- 列4: 状态 (status)
- 列5: 备注 (remark)

### Sheet3: 人员信息 (ExcelTargetMemberInfo)
字段列表（按列索引顺序）：
- 列0: 所属单位ID (orgId) - 必须是有效的公司ID
- 列1: 数据处理活动 (processActivityId)
- 列2: 所属部门ID (deptId)
- 列3: 人员姓名 (name)
- 列4: 岗位或角色名称 (post)
- 列5: 岗位职责 (duty)
- 列6: 所属部门 (department)
- 列7: 涉及的数据处理活动 (dataProcessing)
- 列8: 是否专职 (fullTime)
- 列9: 国籍 (nationality)
- 列10: 状态 (status)
- 列11: 备注 (remark)

### Sheet4: 文档信息 (ExcelTargetManageDocuments)
字段列表（按列索引顺序）：
- 列0: 所属单位ID (orgId) - 必须是有效的公司ID
- 列1: 文档名称 (documentName)
- 列2: 主要内容 (content)
- 列3: 使用范围 (scope)
- 列4: 数据处理活动 (processActivityId)
- 列5: 状态 (status)
- 列6: 备注 (remark)

## 使用说明

1. **多Sheet导入**：使用单个Excel文件包含5个sheet，一次性导入所有类型的数据
2. **导入顺序**：系统按照固定顺序处理各个sheet：Sheet1→Sheet0→Sheet2→Sheet3→Sheet4
3. **数据合并**：Sheet1和Sheet0的数据会合并为完整的公司信息
4. **EasyExcel处理**：使用EasyExcel和对应的listener处理每个sheet的数据
5. **错误处理**：每个sheet的解析错误和数据错误都会被记录并返回
6. **权限控制**：需要 `targetCompany:companyInfo:import` 权限

## 注意事项

1. Excel文件大小建议不超过10MB
2. 单次导入建议不超过1000条记录
3. 部门、人员、文档信息的 `orgId` 字段必须是已存在的公司ID
4. 导入前建议先备份现有数据
5. 如果导入过程中出现错误，已成功导入的数据不会回滚
