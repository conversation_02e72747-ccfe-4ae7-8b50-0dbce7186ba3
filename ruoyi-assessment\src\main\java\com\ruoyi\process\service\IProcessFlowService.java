package com.ruoyi.process.service;

import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.domain.ProcessStepInstance;

import java.util.List;

/**
 * 流程流转服务接口
 * 负责流程实例和步骤实例的创建、管理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IProcessFlowService {

    /**
     * 根据评估类型和业务ID创建流程实例
     *
     * @param type 评估类型
     * @param processCode 流程编码
     * @param businessId 业务ID（如评估计划任务ID）
     * @param operator 操作人
     * @return 流程实例ID
     */
    Long createProcessInstance(String type, String processCode, Long businessId, String operator);

    /**
     * 为流程实例创建所有步骤实例
     *
     * @param processInstanceId 流程实例ID
     * @param planTaskId 评估计划任务ID
     * @param operator 操作人
     * @return 创建的步骤实例列表
     */
    List<ProcessStepInstance> createStepInstances(Long processInstanceId, Long planTaskId, String operator);

    /**
     * 根据流程实例ID获取所有步骤实例
     *
     * @param processInstanceId 流程实例ID
     * @return 步骤实例列表
     */
    List<ProcessStepInstance> getStepInstancesByProcessId(Long processInstanceId);

    /**
     * 根据业务ID获取流程实例
     *
     * @param businessId 业务ID
     * @return 流程实例
     */
    ProcessInstance getProcessInstanceByBusinessId(Long businessId);

    /**
     * 更新步骤实例状态
     *
     * @param stepInstanceId 步骤实例ID
     * @param status 状态
     * @param operator 操作人
     * @param remark 备注
     * @return 更新结果
     */
    boolean updateStepInstanceStatus(Long stepInstanceId, Integer status, String operator, String remark);

    /**
     * 获取流程进度
     *
     * @param processInstanceId 流程实例ID
     * @return 进度百分比
     */
    Integer getProcessProgress(Long processInstanceId);

    /**
     * 启动流程实例（创建流程实例和步骤实例）
     *
     * @param type 流程类型
     * @param processCode 流程标识
     * @param businessId 业务ID
     * @param operator 操作人
     * @return 流程实例ID
     */
    Long startProcessInstance(String type, String processCode, Long businessId, String operator);

    /**
     * 执行下一步骤
     *
     * @param processInstanceId 流程实例ID
     * @param operator 操作人
     * @param remark 备注
     * @return 执行结果
     */
    boolean executeNextStep(Long processInstanceId, String operator, String remark);
}
