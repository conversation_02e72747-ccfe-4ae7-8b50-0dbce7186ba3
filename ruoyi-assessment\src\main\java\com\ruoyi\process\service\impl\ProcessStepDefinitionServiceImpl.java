package com.ruoyi.process.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.process.mapper.ProcessStepDefinitionMapper;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.service.IProcessStepDefinitionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 流程步骤定义Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class ProcessStepDefinitionServiceImpl extends ServiceImpl<ProcessStepDefinitionMapper, ProcessStepDefinition> implements IProcessStepDefinitionService {

    @Override
    public ProcessStepDefinition getNextStep(Long processId, Long currentStepOrder) {
        // 使用lambda表达式构建查询条件
        LambdaQueryWrapper<ProcessStepDefinition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessStepDefinition::getProcessId, processId)
                   .eq(ProcessStepDefinition::getStepOrder, currentStepOrder + 1)
                   .eq(ProcessStepDefinition::getStatus, 1)
                   .orderByAsc(ProcessStepDefinition::getStepOrder);

        return this.getOne(queryWrapper);
    }

}
