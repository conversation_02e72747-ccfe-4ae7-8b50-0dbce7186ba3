# 若依数据安全评估平台 Docker 部署指南

## 概述

本文档介绍如何使用 Docker Compose 部署若依数据安全评估平台。该部署方案包含以下服务：

- **ruoyi-app**: 主应用服务（Spring Boot）
- **mysql**: MySQL 5.7 数据库
- **redis**: Redis 7 缓存服务
- **nginx**: Nginx 反向代理（可选）

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 20GB以上可用空间

### 软件要求
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.6+（用于构建应用）
- JDK 8（用于构建应用）

## 快速开始

### 1. 准备工作

确保已安装 Docker 和 Docker Compose：

```bash
# 检查 Docker 版本
docker --version

# 检查 Docker Compose 版本
docker-compose --version
```

### 2. 进入部署目录

```bash
cd deploy
```

### 3. 准备JAR包

**重要**: 将您编译好的JAR包命名为 `ruoyi_admin.jar` 并放在 `deploy` 目录下。

```bash
# 示例：如果您的JAR包名为 ruoyi-admin-3.9.0.jar
cp ../ruoyi-admin/target/ruoyi-admin.jar ruoyi_admin.jar
```

### 4. 检查JAR包

```bash
# Linux/macOS
chmod +x deploy.sh
./deploy.sh check

# Windows
deploy.bat check
```

### 5. 启动服务

```bash
# Linux/macOS
./deploy.sh start

# Windows
deploy.bat start
```

### 6. 访问应用

- 应用地址: http://localhost:8080
- 默认账号: admin
- 默认密码: admin123

## 详细配置

### 环境变量配置

编辑 `.env` 文件来修改配置：

```env
# 数据库配置
MYSQL_ROOT_PASSWORD=secdriver@q1!w2@
MYSQL_DATABASE=data_security_v3
MYSQL_USER=data_security_v3
MYSQL_PASSWORD=dKSaKe3aRTs7xAhb

# Redis配置
REDIS_PASSWORD=secdriver@q1!w2@

# 应用配置
APP_VERSION=3.9.0
APP_PORT=8080

# 时区配置
TZ=Asia/Shanghai
```

### 端口配置

默认端口映射：
- 应用: 8080
- MySQL: 3306
- Redis: 6379
- Nginx: 80, 443

如需修改端口，请编辑 `docker-compose.yml` 文件。

### 数据持久化

数据卷配置：
- `mysql_data`: MySQL 数据目录
- `redis_data`: Redis 数据目录
- `app_logs`: 应用日志目录
- `app_upload`: 文件上传目录
- `app_config`: 应用配置目录

## 管理命令

### 使用部署脚本

```bash
# 检查JAR包是否存在
./deploy.sh check

# 构建Docker镜像
./deploy.sh build

# 启动所有服务
./deploy.sh start

# 停止所有服务
./deploy.sh stop

# 重启所有服务
./deploy.sh restart

# 查看所有服务日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs ruoyi-app

# 查看服务状态
./deploy.sh status

# 清理所有数据（危险操作）
./deploy.sh clean
```

### 使用 Docker Compose 命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 查看服务状态
docker-compose ps

# 重启特定服务
docker-compose restart ruoyi-app

# 进入容器
docker-compose exec ruoyi-app sh
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 查看服务日志: `./deploy.sh logs`
   - 确认 `.env` 文件配置正确

2. **数据库连接失败**
   - 等待 MySQL 服务完全启动（约30-60秒）
   - 检查数据库配置和密码
   - 查看 MySQL 日志: `./deploy.sh logs mysql`

3. **应用无法访问**
   - 确认防火墙设置
   - 检查应用健康状态: `docker-compose ps`
   - 查看应用日志: `./deploy.sh logs ruoyi-app`

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs ruoyi-app
docker-compose logs mysql
docker-compose logs redis

# 实时查看日志
docker-compose logs -f ruoyi-app
```

### 健康检查

```bash
# 检查服务状态
docker-compose ps

# 检查容器健康状态
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 测试应用健康检查
curl http://localhost:8080/actuator/health
```

## 生产环境部署

### 安全配置

1. **修改默认密码**
   - 修改 `.env` 文件中的数据库密码
   - 修改 Redis 密码
   - 修改应用管理员密码

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置 HTTPS（修改 nginx 配置）
   - 使用内网部署，通过 VPN 访问

3. **数据备份**
   - 定期备份 MySQL 数据
   - 备份应用配置和上传文件

### 性能优化

1. **JVM 参数调优**
   - 根据服务器配置调整 Dockerfile 中的 JAVA_OPTS
   - 监控内存使用情况

2. **数据库优化 (MySQL 5.7)**
   - 调整 `innodb_buffer_pool_size` 为物理内存的70-80%
   - 启用查询缓存: `query_cache_size=8M`
   - 优化连接数: `max_connections=1000`
   - 定期优化数据库表
   - 详细配置请参考 [MYSQL_5.7_NOTES.md](./MYSQL_5.7_NOTES.md)

3. **缓存优化**
   - 调整 Redis 配置
   - 监控缓存命中率

## 升级指南

### 应用升级

1. 停止服务: `./deploy.sh stop`
2. 备份数据: 备份数据库和上传文件
3. 更新代码: 拉取最新代码
4. 重新构建: `./deploy.sh build-app && ./deploy.sh build`
5. 启动服务: `./deploy.sh start`

### 数据库升级

1. 备份现有数据库
2. 执行数据库升级脚本
3. 测试数据完整性

## 监控和维护

### 日志管理

- 应用日志: `/app/logs`
- Nginx 日志: `/var/log/nginx`
- 定期清理旧日志文件

### 性能监控

- 监控容器资源使用情况
- 监控数据库性能
- 监控应用响应时间

### 定期维护

- 定期更新 Docker 镜像
- 清理无用的 Docker 镜像和容器
- 检查磁盘空间使用情况

## 支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查应用日志
3. 联系技术支持团队

---

**注意**: 本部署方案适用于开发和测试环境。生产环境部署请根据实际需求进行安全加固和性能优化。
