<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluateModel.mapper.EvaluateTypeMapper">
    
    <resultMap type="EvaluateType" id="EvaluateTypeResult">
        <result property="id"    column="id"    />
        <result property="processCode"    column="process_code"    />
        <result property="title"    column="title"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateTypeVo">
        select id, process_code, title, sort, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_type
    </sql>

    <select id="selectEvaluateTypeList" parameterType="EvaluateType" resultMap="EvaluateTypeResult">
        <include refid="selectEvaluateTypeVo"/>
        <where>  
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateTypeById" parameterType="Long" resultMap="EvaluateTypeResult">
        <include refid="selectEvaluateTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateType" parameterType="EvaluateType" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processCode != null">process_code,</if>
            <if test="title != null">title,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processCode != null">#{processCode},</if>
            <if test="title != null">#{title},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateType" parameterType="EvaluateType">
        update dsa_evaluate_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="title != null">title = #{title},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateTypeById" parameterType="Long">
        delete from dsa_evaluate_type where id = #{id}
    </delete>

    <delete id="deleteEvaluateTypeByIds" parameterType="String">
        delete from dsa_evaluate_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>