package com.ruoyi.evaluate.company.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件清理工具测试类
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@SpringBootTest
public class FileCleanupUtilTest {

    @Test
    public void testReportFileDistribution() {
        log.info("开始测试文件分布情况报告");
        FileCleanupUtil.reportFileDistribution();
        log.info("文件分布情况报告测试完成");
    }

    @Test
    public void testCleanupIncorrectTargetCompanyFiles() {
        log.info("开始测试清理错误位置的targetCompany文件");
        
        // 先报告当前情况
        FileCleanupUtil.reportFileDistribution();
        
        // 执行清理
        FileCleanupUtil.cleanupIncorrectTargetCompanyFiles();
        
        // 再次报告清理后的情况
        FileCleanupUtil.reportFileDistribution();
        
        log.info("清理错误位置的targetCompany文件测试完成");
    }
}
