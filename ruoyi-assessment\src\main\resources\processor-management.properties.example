# 处理器管理配置示例
# 复制此文件为 processor-management.properties 并根据需要修改配置

# ================================
# 处理器管理控制器配置
# ================================

# 启用处理器管理控制器（默认启用）
processor.management.enabled=true

# 启用处理器管理测试控制器（默认禁用，仅开发环境使用）
processor.management.test.enabled=false

# ================================
# 处理器热重载配置
# ================================

# 启用处理器热重载功能（默认禁用）
processor.hot-reload.enabled=true

# 热重载检查间隔（毫秒）
processor.hot-reload.check-interval=5000

# 热重载最大重试次数
processor.hot-reload.max-retry=3

# ================================
# 处理器注册表配置
# ================================

# 处理器注册表初始化超时时间（毫秒）
processor.registry.init-timeout=30000

# 处理器注册表缓存大小
processor.registry.cache-size=1000

# 是否启用处理器注册表统计
processor.registry.statistics.enabled=true

# ================================
# 处理器包管理配置
# ================================

# 处理器包扫描路径
processor.package.scan-paths=com.ruoyi.evaluate.evaluatePlan.service.processor.impl

# 处理器包验证模式（strict/loose）
processor.package.validation-mode=loose

# ================================
# 日志配置
# ================================

# 处理器管理日志级别
logging.level.com.ruoyi.evaluate.evaluatePlan.controller.ProcessorManagementController=INFO
logging.level.com.ruoyi.evaluate.evaluatePlan.service.processor=DEBUG

# ================================
# 安全配置
# ================================

# 处理器管理接口访问IP白名单（逗号分隔，空表示不限制）
processor.management.allowed-ips=

# 处理器管理接口访问时间限制（小时，0表示不限制）
processor.management.access-hours=0

# ================================
# 开发环境特殊配置
# ================================

# 开发环境下启用所有功能
spring.profiles.active=dev

# 开发环境配置
---
spring:
  profiles: dev
  
processor:
  management:
    enabled: true
    test:
      enabled: true
  hot-reload:
    enabled: true
    check-interval: 3000
  registry:
    statistics:
      enabled: true

logging:
  level:
    com.ruoyi.evaluate.evaluatePlan.controller: DEBUG
    com.ruoyi.evaluate.evaluatePlan.service.processor: DEBUG

# ================================
# 生产环境配置
# ================================
---
spring:
  profiles: prod
  
processor:
  management:
    enabled: false
    test:
      enabled: false
  hot-reload:
    enabled: false
  registry:
    statistics:
      enabled: false

logging:
  level:
    com.ruoyi.evaluate.evaluatePlan.controller: WARN
    com.ruoyi.evaluate.evaluatePlan.service.processor: INFO

# ================================
# 测试环境配置
# ================================
---
spring:
  profiles: test
  
processor:
  management:
    enabled: true
    test:
      enabled: true
  hot-reload:
    enabled: true
  registry:
    statistics:
      enabled: true

logging:
  level:
    com.ruoyi.evaluate.evaluatePlan.controller: DEBUG
    com.ruoyi.evaluate.evaluatePlan.service.processor: DEBUG
