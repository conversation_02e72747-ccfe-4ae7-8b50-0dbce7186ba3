# Git相关
.git
.gitignore
.gitattributes

# IDE相关
.idea/
.vscode/
*.iml
*.ipr
*.iws

# 构建相关
target/
*.class
# 只保留当前目录下的ruoyi_admin.jar
*.jar
!ruoyi_admin.jar

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
Thumbs.db

# Maven相关
.mvn/
mvnw
mvnw.cmd

# Node.js相关（如果有前端构建）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 文档（除了部署相关文档）
*.md
!README.md
!DOCKER_DEPLOY.md
!JAR_DEPLOYMENT_GUIDE.md
!MYSQL_5.7_NOTES.md

# 其他
.env.local
.env.development
.env.test
.env.production
