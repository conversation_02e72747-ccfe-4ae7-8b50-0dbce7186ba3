package com.ruoyi.dataItem.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/16 17:33
 * @description 数据项Excel导入导出对象
 */
@Data
public class ExcelDataItem {
    /** 序号 */
    @ExcelProperty(index = 0)
    private Long no;

    /** 数据名称 */
    @ExcelProperty(index = 1)
    private String dataName;

    /** 依据数据分类分级规范（无对应字段，预留） */
    @ExcelProperty(index = 2)
    private String dataStandard;

    /** 数据一级类别 */
    @ExcelProperty(index = 3)
    private String categoryLevel1;

    /** 数据二级类别 */
    @ExcelProperty(index = 4)
    private String categoryLevel2;

    /** 数据三级类别 */
    @ExcelProperty(index = 5)
    private String categoryLevel3;

    /** 数据四级类别 */
    @ExcelProperty(index = 6)
    private String categoryLevel4;

    /** 数据级别 */
    @ExcelProperty(index = 7)
    private String dataLevel;

    /** 数据载体 */
    @ExcelProperty(index = 8)
    private String dataCarrier;

    /** 数据来源 */
    @ExcelProperty(index = 9)
    private String dataSource;

    /** 数据数量（单位：GB） */
    @ExcelProperty(index = 10)
    private BigDecimal dataAmountGb;

    /** 数据数量（单位：条） */
    @ExcelProperty(index = 11)
    private String dataAmountCount;

    /** 覆盖类型 */
    @ExcelProperty(index = 12)
    private String coverageType;

    /** 覆盖占比 */
    @ExcelProperty(index = 13)
    private String coverageRatio;

    /** 数据精度 */
    @ExcelProperty(index = 14)
    private String dataAccuracy;

    /** 详细描述 */
    @ExcelProperty(index = 15)
    private String description;

    /** 数据处理者名称 */
    @ExcelProperty(index = 16)
    private String processorName;

    /** 机构代码 */
    @ExcelProperty(index = 17)
    private String orgCode;

    /** 省 */
    @ExcelProperty(index = 18)
    private String province;

    /** 市 */
    @ExcelProperty(index = 19)
    private String city;

    /** 数据处理者性质 */
    @ExcelProperty(index = 20)
    private String processorNature;

    /** 所属行业 */
    @ExcelProperty(index = 21)
    private String industry;

    /** 主营业务范围 */
    @ExcelProperty(index = 22)
    private String mainBusiness;

    /** 姓名 */
    @ExcelProperty(index = 23)
    private String contactName;

    /** 职务 */
    @ExcelProperty(index = 24)
    private String contactPosition;

    /** 联系方式 */
    @ExcelProperty(index = 25)
    private String contactInfo;

    /** 数据处理活动方式 */
    @ExcelProperty(index = 26)
    private String processingMethod;

    /** 数据处理目的 */
    @ExcelProperty(index = 27)
    private String processingPurpose;

    /** 是否涉及算法自动化处理，1-是 0-否 */
    @ExcelProperty(index = 28)
    private String isAlgorithmAuto;

    /** 是否出境 */
    @ExcelProperty(index = 29)
    private String isCrossBorder;

    /** 数据出境接收方名称 */
    @ExcelProperty(index = 30)
    private String crossBorderReceiver;

    /** 数据出境方式 */
    @ExcelProperty(index = 31)
    private String crossBorderMethod;

    /** 是否开展数据出境安全评估，1-是 0-否 */
    @ExcelProperty(index = 32)
    private String isCrossBorderAssess;

    /** 数据出境安全评估结果 */
    @ExcelProperty(index = 33)
    private String crossBorderAssessResult;

    /** 是否对外共享 */
    @ExcelProperty(index = 34)
    private String isExternalShare;

    /** 数据对外共享接收方名称 */
    @ExcelProperty(index = 35)
    private String externalShareReceiver;

    /** 数据对外共享方式 */
    @ExcelProperty(index = 36)
    private String externalShareMethod;

    /** 是否涉及跨主体流动 */
    @ExcelProperty(index = 37)
    private String isCrossSubjectFlow;

    /** 是否为涉外数据 */
    @ExcelProperty(index = 38)
    private String isForeignData;

    /** 信息系统名称 */
    @ExcelProperty(index = 39)
    private String systemName;

    /** 信息系统类型 */
    @ExcelProperty(index = 40)
    private String systemType;

    /** 信息系统IP地址 */
    @ExcelProperty(index = 41)
    private String systemIp;

    /** 信息系统域名 */
    @ExcelProperty(index = 42)
    private String systemDomain;

    /** 网络安全等级保护情况 */
    @ExcelProperty(index = 43)
    private String cyberProtectLevel;

    /** 通信网络安全防护定级备案情况 */
    @ExcelProperty(index = 44)
    private String networkProtectRecord;

    /** 是否为关键信息基础设施，1-是 0-否 */
    @ExcelProperty(index = 45)
    private String isCriticalInfra;

    /** 是否进行数据安全风险评估，1-是 0-否 */
    @ExcelProperty(index = 46)
    private String isDataRiskAssess;

    /** 评估机构 */
    @ExcelProperty(index = 47)
    private String assessOrg;

    /** 评估规范 */
    @ExcelProperty(index = 48)
    private String assessStandard;

    /** 评估时间 */
    @ExcelProperty(index = 49)
    private String assessTime;

    /** 评估结论 */
    @ExcelProperty(index = 50)
    private String assessConclusion;

    /** 备注 */
    @ExcelProperty(index = 51)
    private String remark;

    /** 责任部门 */
    @ExcelProperty(index = 52)
    private String dutyDept;

    /** 责任人 */
    @ExcelProperty(index = 53)
    private String dutyUser;
}
