package com.ruoyi.evaluate.company.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 被评估单位人员信息对象 dsa_target_member_info
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_target_member_info")
public class TargetMemberInfo extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 数据处理活动（多选，逗号分隔） */
    @Excel(name = "数据处理活动")
    @TableField(value = "process_activity_id")
    private String processActivityId;

    /** 所属部门ID */
    @Excel(name = "所属部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    @TableField(value = "name")
    private String name;

    /** 岗位或角色名称 */
    @Excel(name = "岗位或角色名称")
    @TableField(value = "post")
    private String post;

    /** 岗位职责 */
    @Excel(name = "岗位职责")
    @TableField(value = "duty")
    private String duty;

    /** 所属部门 */
    @Excel(name = "所属部门")
    @TableField(value = "department")
    private String department;

    /** 涉及的数据处理活动 */
    @Excel(name = "涉及的数据处理活动")
    @TableField(value = "data_processing")
    private String dataProcessing;

    /** 是否专职 */
    @Excel(name = "是否专职")
    @TableField(value = "full_time")
    private String fullTime;

    /** 国籍 */
    @Excel(name = "国籍")
    @TableField(value = "nationality")
    private String nationality;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;







}