package com.ruoyi.evaluate.evaluateCompany.enums;

/**
 * 评估团队分组枚举
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public enum TeamGroupEnum {
    /**
     * 项目管理组
     */
    PROJECT_MANAGEMENT(1L, "项目管理组"),

    /**
     * 评估组
     */
    EVALUATION(2L, "评估组"),

    /**
     * 技术支持组
     */
    TECHNICAL_SUPPORT(3L, "技术支持组"),

    /**
     * 质量保证组
     */
    QUALITY_ASSURANCE(4L, "质量保证组"),

    /**
     * 配置管理组
     */
    CONFIGURATION_MANAGEMENT(5L, "配置管理组");

    private final Long code;
    private final String name;

    TeamGroupEnum(Long code, String name) {
        this.code = code;
        this.name = name;
    }

    public Long getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取对应的枚举
     *
     * @param code 分组代码
     * @return 对应的枚举，如果找不到返回null
     */
    public static TeamGroupEnum fromCode(Long code) {
        if (code == null) {
            return null;
        }
        for (TeamGroupEnum groupEnum : TeamGroupEnum.values()) {
            if (groupEnum.code.equals(code)) {
                return groupEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取对应的名称
     *
     * @param code 分组代码
     * @return 对应的名称，如果找不到返回空字符串
     */
    public static String getNameByCode(Long code) {
        TeamGroupEnum groupEnum = fromCode(code);
        return groupEnum != null ? groupEnum.getName() : "";
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name 分组名称
     * @return 对应的枚举，如果找不到返回null
     */
    public static TeamGroupEnum fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (TeamGroupEnum groupEnum : TeamGroupEnum.values()) {
            if (groupEnum.name.equals(name.trim())) {
                return groupEnum;
            }
        }
        return null;
    }

    /**
     * 根据名称获取对应的code
     *
     * @param name 分组名称
     * @return 对应的code，如果找不到返回null
     */
    public static Long getCodeByName(String name) {
        TeamGroupEnum groupEnum = fromName(name);
        return groupEnum != null ? groupEnum.getCode() : null;
    }
}
