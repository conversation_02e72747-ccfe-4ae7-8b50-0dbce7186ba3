package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemLog;
import com.ruoyi.dataItem.service.IDataItemLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据项变更日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/log")
@Api(value = "数据项变更日志控制器", tags = {"数据项变更日志管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemLogController extends BaseController
{
    private final IDataItemLogService dataItemLogService;

    /**
     * 查询数据项变更日志列表
     */
    @ApiOperation("查询数据项变更日志列表")
    @PreAuthorize("@ss.hasPermi('dataItem:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Valid DataItemLog dataItemLog) {
        startPage();
        List<DataItemLog> list = dataItemLogService.list(new QueryWrapper<DataItemLog>(dataItemLog));
        return getDataTable(list);
    }

    /**
     * 获取数据项变更日志详细信息
     */
    @ApiOperation("获取数据项变更日志详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemLogService.getById(id));
    }

    /**
     * 新增数据项变更日志
     */
    @ApiOperation("新增数据项变更日志")
    @PreAuthorize("@ss.hasPermi('dataItem:log:add')")
    @Log(title = "数据项变更日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody DataItemLog dataItemLog) {
        return toAjax(dataItemLogService.save(dataItemLog));
    }

    /**
     * 修改数据项变更日志
     */
    @ApiOperation("修改数据项变更日志")
    @PreAuthorize("@ss.hasPermi('dataItem:log:edit')")
    @Log(title = "数据项变更日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemLog dataItemLog) {
        return toAjax(dataItemLogService.updateById(dataItemLog));
    }

    /**
     * 删除数据项变更日志
     */
    @ApiOperation("删除数据项变更日志")
    @PreAuthorize("@ss.hasPermi('dataItem:log:remove')")
    @Log(title = "数据项变更日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemLogService.removeByIds(Arrays.asList(ids)));
    }
}