package com.ruoyi.process.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.process.domain.ProcessDefinition;
import com.ruoyi.process.service.IProcessDefinitionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程定义Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/process/definition")
@Api(value = "流程定义控制器", tags = {"流程定义管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessDefinitionController extends BaseController {
    private final IProcessDefinitionService processDefinitionService;

    /**
     * 查询流程定义列表
     */
    @ApiOperation("查询流程定义列表")
    @PreAuthorize("@ss.hasPermi('process:definition:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessDefinition processDefinition) {
        startPage();
        List<ProcessDefinition> list = processDefinitionService.list(new QueryWrapper<ProcessDefinition>(processDefinition));
        return getDataTable(list);
    }

    /**
     * 获取流程定义详细信息
     */
    @ApiOperation("获取流程定义详细信息")
    @PreAuthorize("@ss.hasPermi('process:definition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processDefinitionService.getById(id));
    }

    /**
     * 新增流程定义
     */
    @ApiOperation("新增流程定义")
    @PreAuthorize("@ss.hasPermi('process:definition:add')")
    @Log(title = "流程定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessDefinition processDefinition) {
        return toAjax(processDefinitionService.save(processDefinition));
    }

    /**
     * 修改流程定义
     */
    @ApiOperation("修改流程定义")
    @PreAuthorize("@ss.hasPermi('process:definition:edit')")
    @Log(title = "流程定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessDefinition processDefinition) {
        return toAjax(processDefinitionService.updateById(processDefinition));
    }

    /**
     * 删除流程定义
     */
    @ApiOperation("删除流程定义")
    @PreAuthorize("@ss.hasPermi('process:definition:remove')")
    @Log(title = "流程定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processDefinitionService.removeByIds(Arrays.asList(ids)));
    }
}