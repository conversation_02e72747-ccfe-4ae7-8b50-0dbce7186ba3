package com.ruoyi.evaluateModel.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.FileUtil;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import com.ruoyi.common.utils.file.ConfigFileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluateModel.domain.EvaluateTemplate;
import com.ruoyi.evaluateModel.service.IEvaluateTemplateService;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 评估报告模板Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/evaluateModel/template")
@Api(value = "评估报告模板控制器", tags = {"评估报告模板管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateTemplateController extends BaseController {
    private final ServerConfig serverConfig;
    private final IEvaluateTemplateService evaluateTemplateService;

    /**
     * 查询评估报告模板列表
     */
    @ApiOperation("查询评估报告模板列表")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluateTemplate evaluateTemplate) {
        startPage();
        List<EvaluateTemplate> list = evaluateTemplateService.list(new QueryWrapper<EvaluateTemplate>(evaluateTemplate));
        return getDataTable(list);
    }

    /**
     * 获取评估报告模板详细信息
     */
    @ApiOperation("获取评估报告模板详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateTemplateService.getById(id));
    }

    /**
     * 新增评估报告模板
     */
    @ApiOperation("新增评估报告模板")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:add')")
    @Log(title = "评估报告模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EvaluateTemplate evaluateTemplate) {
        return toAjax(evaluateTemplateService.save(evaluateTemplate));
    }

    /**
     * 修改评估报告模板
     */
    @ApiOperation("修改评估报告模板")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:edit')")
    @Log(title = "评估报告模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EvaluateTemplate evaluateTemplate) {
        EvaluateTemplate tpl = evaluateTemplateService.getById(evaluateTemplate.getId());
        if (tpl.getType() == 1) {
            throw new ServiceException("内置模板，不允许修改");
        }
        return toAjax(evaluateTemplateService.updateById(evaluateTemplate));
    }

    /**
     * 删除评估报告模板
     */
    @ApiOperation("删除评估报告模板")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:remove')")
    @Log(title = "评估报告模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 校验是否为内置模板，内置模板不允许删除
        for (Long id : ids) {
            EvaluateTemplate template = evaluateTemplateService.getById(id);
            if (template != null && template.getType() == 1) {
                throw new ServiceException("内置模板，不允许删除");
            }
        }
        return toAjax(evaluateTemplateService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 复制评估报告模板
     */
    @ApiOperation("复制评估报告模板")
    @PreAuthorize("@ss.hasPermi('evaluateModel:template:add')")
    @Log(title = "评估报告模板复制", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{templateId}")
    public AjaxResult copyTemplate(@PathVariable("templateId") Long templateId, @Validated @RequestBody EvaluateTemplate newTemplate) {
        return toAjax(evaluateTemplateService.copyTemplate(templateId, newTemplate));
    }

    @ApiOperation("上传模板文件")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String configPath = RuoYiConfig.getConfigPath();
            // 上传并返回新文件名称
            String filePath = configPath + "/template/report";
            if (!FileUtil.exist(filePath)) {
                FileUtil.mkdir(filePath);
            }
            String fileName = ConfigFileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取评估计划清单Excel模板
     *
     * @param response
     */
    @ApiOperation("下载模板标签说明文件（文件流形式）")
    @GetMapping("/tplReadme")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        logger.info("下载模板标签说明文件 {}", configPath);
        String templatePath = configPath + "/template/report/自定义报告模板标签说明.docx";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "自定义报告模板-标签说明.xlsx");
    }
}