package com.ruoyi.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 暂存数据元数据
 * 包含暂存数据的基本信息和扩展属性
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Accessors(chain = true)
public class DraftMetadata implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 业务类型 */
    private String businessType;

    /** 用户ID */
    private Long userId;

    /** 暂存键 */
    private String draftKey;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    /** 数据版本号 */
    private Integer version;

    /** 数据大小（字节） */
    private Long dataSize;

    /** 数据类型 */
    private String dataType;

    /** 备注信息 */
    private String remark;

    /** 扩展属性 */
    private Map<String, Object> extendProps;

    /**
     * 添加扩展属性
     *
     * @param key 属性键
     * @param value 属性值
     * @return 当前对象
     */
    public DraftMetadata addExtendProp(String key, Object value) {
        if (this.extendProps == null) {
            this.extendProps = new HashMap<>();
        }
        this.extendProps.put(key, value);
        return this;
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getExtendProp(String key) {
        if (this.extendProps == null) {
            return null;
        }
        return this.extendProps.get(key);
    }

    /**
     * 获取扩展属性（指定类型）
     *
     * @param key 属性键
     * @param clazz 属性值类型
     * @param <T> 类型参数
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtendProp(String key, Class<T> clazz) {
        Object value = getExtendProp(key);
        if (value == null) {
            return null;
        }
        
        if (clazz.isInstance(value)) {
            return (T) value;
        }
        
        return null;
    }

    /**
     * 移除扩展属性
     *
     * @param key 属性键
     * @return 被移除的属性值
     */
    public Object removeExtendProp(String key) {
        if (this.extendProps == null) {
            return null;
        }
        return this.extendProps.remove(key);
    }

    /**
     * 检查是否包含指定扩展属性
     *
     * @param key 属性键
     * @return 包含返回true
     */
    public boolean hasExtendProp(String key) {
        return this.extendProps != null && this.extendProps.containsKey(key);
    }

    /**
     * 增加版本号
     *
     * @return 当前对象
     */
    public DraftMetadata incrementVersion() {
        if (this.version == null) {
            this.version = 1;
        } else {
            this.version++;
        }
        this.updateTime = new Date();
        return this;
    }

    /**
     * 检查是否已过期
     *
     * @return 已过期返回true
     */
    public boolean isExpired() {
        if (this.expireTime == null) {
            return false;
        }
        return new Date().after(this.expireTime);
    }

    /**
     * 获取剩余过期时间（毫秒）
     *
     * @return 剩余时间，负数表示已过期，null表示永不过期
     */
    public Long getRemainingTime() {
        if (this.expireTime == null) {
            return null;
        }
        return this.expireTime.getTime() - System.currentTimeMillis();
    }

    /**
     * 转换为摘要Map
     *
     * @return 摘要信息
     */
    public Map<String, Object> toSummaryMap() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("businessType", this.businessType);
        summary.put("userId", this.userId);
        summary.put("draftKey", this.draftKey);
        summary.put("createTime", this.createTime);
        summary.put("updateTime", this.updateTime);
        summary.put("expireTime", this.expireTime);
        summary.put("version", this.version);
        summary.put("dataSize", this.dataSize);
        summary.put("dataType", this.dataType);
        summary.put("remark", this.remark);
        summary.put("isExpired", isExpired());
        summary.put("remainingTime", getRemainingTime());
        return summary;
    }
}
