package com.ruoyi.dataItem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据项版本对象 dsa_data_item_version
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_item_version")
public class DataItemVersion extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属单位ID
     */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    @TableField(value = "version_code")
    private String versionCode;

    /**
     * 版本描述
     */
    @Excel(name = "版本描述")
    @TableField(value = "version_desc")
    private String versionDesc;

    /**
     * 状态，1-正常 0-禁用
     */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;
}