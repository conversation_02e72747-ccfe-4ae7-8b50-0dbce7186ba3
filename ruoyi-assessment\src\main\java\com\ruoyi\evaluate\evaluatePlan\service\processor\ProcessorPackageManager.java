package com.ruoyi.evaluate.evaluatePlan.service.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理器包管理器
 * <p>
 * 负责管理不同评估类型的处理器包，提供处理器的分类和统计功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class ProcessorPackageManager {

    /**
     * 评估类型与包路径的映射
     */
    private static final Map<String, String> EVALUATE_TYPE_PACKAGE_MAP = new HashMap<>();
    
    static {
        EVALUATE_TYPE_PACKAGE_MAP.put("data_security_plan", "dataSecurityPlan");
        EVALUATE_TYPE_PACKAGE_MAP.put("basic_info", "basicInfo");
        EVALUATE_TYPE_PACKAGE_MAP.put("common", "common");
    }

    /**
     * 根据评估类型获取对应的包名
     */
    public String getPackageByEvaluateType(String evaluateType) {
        return EVALUATE_TYPE_PACKAGE_MAP.get(evaluateType);
    }

    /**
     * 获取所有支持的评估类型
     */
    public Set<String> getSupportedEvaluateTypes() {
        return EVALUATE_TYPE_PACKAGE_MAP.keySet().stream()
                .filter(type -> !"common".equals(type))
                .collect(Collectors.toSet());
    }

    /**
     * 根据处理器类名推断评估类型
     */
    public String inferEvaluateTypeFromClassName(String className) {
        if (className.startsWith("BasicInfo")) {
            return "basic_info";
        } else if (className.contains("DataSecurity") ||
                   (!className.startsWith("BasicInfo") && !className.equals("DefaultStepDataProcessor"))) {
            return "data_security_plan";
        } else {
            return "common";
        }
    }

    /**
     * 获取处理器统计信息
     */
    public Map<String, Object> getProcessorStatistics(List<IStepDataProcessor> processors) {
        Map<String, Object> stats = new HashMap<>();
        
        // 按评估类型分组统计
        Map<String, Long> typeStats = processors.stream()
                .collect(Collectors.groupingBy(
                    processor -> processor.getEvaluateType() != null ? 
                        processor.getEvaluateType() : "common",
                    Collectors.counting()
                ));
        
        stats.put("byEvaluateType", typeStats);
        stats.put("totalProcessors", processors.size());
        
        // 按包分组统计
        Map<String, Long> packageStats = processors.stream()
                .collect(Collectors.groupingBy(
                    processor -> getPackageByEvaluateType(
                        processor.getEvaluateType() != null ? 
                            processor.getEvaluateType() : "common"
                    ),
                    Collectors.counting()
                ));
        
        stats.put("byPackage", packageStats);
        
        return stats;
    }

    /**
     * 验证处理器包结构
     */
    public List<String> validatePackageStructure(List<IStepDataProcessor> processors) {
        List<String> issues = new ArrayList<>();

        for (IStepDataProcessor processor : processors) {
            String className = processor.getClass().getSimpleName();
            String actualPackage = processor.getClass().getPackage().getName();
            String evaluateType = processor.getEvaluateType();

            // 只检查包名是否正确，不检查命名规范（因为现有的处理器命名已经确定）
            String expectedPackageSuffix = getPackageByEvaluateType(
                evaluateType != null ? evaluateType : "common"
            );

            if (expectedPackageSuffix != null && !actualPackage.endsWith(expectedPackageSuffix)) {
                // 只记录信息，不作为错误
                log.debug("处理器 {} 在包 {} 中，期望在包 {} 中",
                    className, actualPackage, expectedPackageSuffix);
            }
        }

        return issues;
    }

    /**
     * 根据评估类型获取类名前缀
     */
    private String getClassNamePrefix(String evaluateType) {
        switch (evaluateType) {
            case "basic_info":
                return "BasicInfo";
            case "data_security_plan":
                return "DataSecurity"; // 或者不要求前缀
            default:
                return null;
        }
    }

    /**
     * 生成处理器包结构报告
     */
    public String generatePackageStructureReport(List<IStepDataProcessor> processors) {
        StringBuilder report = new StringBuilder();
        report.append("=== 处理器包结构报告 ===\n\n");
        
        // 按包分组
        Map<String, List<IStepDataProcessor>> packageGroups = processors.stream()
                .collect(Collectors.groupingBy(processor -> {
                    String packageName = processor.getClass().getPackage().getName();
                    String[] parts = packageName.split("\\.");
                    return parts[parts.length - 1]; // 获取最后一级包名
                }));
        
        for (Map.Entry<String, List<IStepDataProcessor>> entry : packageGroups.entrySet()) {
            String packageName = entry.getKey();
            List<IStepDataProcessor> packageProcessors = entry.getValue();
            
            report.append(String.format("📦 %s 包 (%d个处理器):\n", packageName, packageProcessors.size()));
            
            for (IStepDataProcessor processor : packageProcessors) {
                report.append(String.format("  - %s (步骤: %s, 类型: %s)\n", 
                    processor.getClass().getSimpleName(),
                    processor.getStepCode(),
                    processor.getEvaluateType() != null ? processor.getEvaluateType() : "通用"
                ));
            }
            report.append("\n");
        }
        
        // 添加统计信息
        Map<String, Object> stats = getProcessorStatistics(processors);
        report.append("📊 统计信息:\n");
        report.append(String.format("  总处理器数: %d\n", stats.get("totalProcessors")));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> typeStats = (Map<String, Long>) stats.get("byEvaluateType");
        report.append("  按评估类型分布:\n");
        typeStats.forEach((type, count) -> 
            report.append(String.format("    %s: %d个\n", type, count))
        );
        
        return report.toString();
    }
}
