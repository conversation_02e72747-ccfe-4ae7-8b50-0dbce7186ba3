# MySQL 5.7 配置说明

## 版本选择说明

本项目使用 MySQL 5.7 版本，主要考虑：

1. **稳定性**: MySQL 5.7 是一个成熟稳定的版本
2. **兼容性**: 与现有应用代码完全兼容
3. **性能**: 在大多数场景下性能表现良好
4. **维护**: 仍在维护期内，安全更新及时

## MySQL 5.7 vs 8.0 主要差异

### 1. 认证插件
- **MySQL 5.7**: 默认使用 `mysql_native_password`
- **MySQL 8.0**: 默认使用 `caching_sha2_password`

### 2. SQL模式
- **MySQL 5.7**: 默认SQL模式相对宽松
- **MySQL 8.0**: 默认SQL模式更严格

### 3. 字符集
- **MySQL 5.7**: 默认字符集 `latin1`
- **MySQL 8.0**: 默认字符集 `utf8mb4`

## 配置优化

### Docker Compose 配置
```yaml
mysql:
  image: mysql:5.7
  command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
```

### MySQL 配置文件 (my.cnf)
```ini
[mysqld]
# 字符集设置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# SQL模式（兼容性设置）
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# InnoDB设置（5.7优化）
innodb_buffer_pool_size=128M
innodb_flush_log_at_trx_commit=2
innodb_log_buffer_size=2M
```

## 连接配置

### JDBC URL 参数
```
******************************************************************************************************************************************************
```

重要参数说明：
- `useUnicode=true`: 启用Unicode支持
- `characterEncoding=utf8`: 字符编码设置
- `zeroDateTimeBehavior=convertToNull`: 零日期处理
- `useSSL=false`: 禁用SSL（内网环境）
- `serverTimezone=GMT%2B8`: 时区设置

## 性能调优建议

### 1. 内存配置
```ini
# 根据服务器内存调整
innodb_buffer_pool_size=128M  # 可调整为物理内存的70-80%
query_cache_size=8M           # 查询缓存
tmp_table_size=16M            # 临时表大小
```

### 2. 连接配置
```ini
max_connections=1000          # 最大连接数
max_connect_errors=6000       # 最大连接错误数
```

### 3. 日志配置
```ini
slow_query_log=1              # 启用慢查询日志
long_query_time=3             # 慢查询阈值（秒）
```

## 常见问题

### 1. 字符集问题
如果遇到中文乱码，检查：
- 数据库字符集: `utf8mb4`
- 表字符集: `utf8mb4`
- 连接字符集: `utf8`

### 2. 时区问题
确保设置正确的时区：
```sql
SET GLOBAL time_zone = '+8:00';
```

### 3. SQL模式问题
如果遇到SQL兼容性问题，可以调整SQL模式：
```sql
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';
```

## 升级路径

如果将来需要升级到MySQL 8.0：

1. **备份数据**
2. **测试兼容性**
3. **调整配置**
4. **逐步迁移**

### 升级注意事项
- 检查应用代码兼容性
- 更新JDBC驱动版本
- 调整认证插件配置
- 测试所有功能模块

## 监控建议

### 1. 关键指标
- 连接数使用率
- 缓冲池命中率
- 慢查询数量
- 锁等待时间

### 2. 监控工具
- MySQL自带的性能监控
- 第三方监控工具
- 应用层面的数据库监控

## 安全建议

1. **用户权限**: 使用最小权限原则
2. **网络安全**: 限制数据库访问来源
3. **密码策略**: 使用强密码
4. **定期备份**: 制定备份策略
5. **日志审计**: 启用审计日志

---

**注意**: 本配置适用于中小型应用。大型应用请根据实际负载进行调优。
