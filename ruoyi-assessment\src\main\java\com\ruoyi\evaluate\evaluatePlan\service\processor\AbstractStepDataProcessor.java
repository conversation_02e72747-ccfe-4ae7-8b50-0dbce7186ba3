package com.ruoyi.evaluate.evaluatePlan.service.processor;

import java.util.*;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.service.IProcessStepInstanceService;
import com.ruoyi.process.service.IProcessStepDefinitionService;

/**
 * 抽象步骤数据处理器基类
 * <p>
 * 提供步骤数据处理的通用功能和模板方法
 * 子类只需要实现具体的业务逻辑即可
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
public abstract class AbstractStepDataProcessor implements IStepDataProcessor {

    @Autowired
    protected IProcessStepInstanceService processStepInstanceService;

    @Autowired
    protected IProcessStepDefinitionService processStepDefinitionService;

    @Autowired
    protected IDraftStorageService draftStorageService;


    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        try {
            log.debug("开始处理步骤数据: {}, 计划ID: {}, 流程实例ID: {}", stepCode, planTask.getId(), processInstanceId);

            // 获取步骤实例
            ProcessStepInstance stepInstance = getStepInstance(processInstanceId, stepCode);

            // 构建基础响应对象
            StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(
                    planTask, stepInstance, stepCode, getEvaluateType());

            // 处理具体步骤数据（由子类实现）
            getSpecificStepData(planTask, stepInstance, processInstanceId, response);

            log.debug("步骤数据处理完成: {}, 扩展数据项数量: {}", stepCode,
                    response.getStepData() != null ? response.getStepData().size() : 0);
            return response;

        } catch (Exception e) {
            log.error("处理步骤数据失败: {}, 计划ID: {}", stepCode, planTask.getId(), e);
            throw new ServiceException("处理步骤数据失败: " + e.getMessage());
        }
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    protected abstract void getSpecificStepData(EvaluatePlanTask planTask,
                                                ProcessStepInstance stepInstance,
                                                Long processInstanceId,
                                                StepDataResponse response);


    /**
     * 获取步骤名称（由子类实现）
     *
     * @return 步骤名称
     */
    protected abstract String getStepName();

    /**
     * 获取步骤实例
     * <p>
     * 优化点：
     * 1. 添加参数校验
     * 2. 优化查询逻辑，减少不必要的数据库查询
     * 3. 添加日志记录便于调试
     * 4. 提高查询效率
     *
     * @param processInstanceId 流程实例ID
     * @param stepCode          步骤编码
     * @return 匹配的步骤实例，如果未找到则返回null
     */
    protected ProcessStepInstance getStepInstance(Long processInstanceId, String stepCode) {
        if (processInstanceId == null) {
            log.debug("流程实例ID为空，无法获取步骤实例");
            return null;
        }

        if (stepCode == null || stepCode.trim().isEmpty()) {
            log.debug("步骤编码为空，无法获取步骤实例");
            return null;
        }

        try {
            log.debug("开始查找步骤实例，流程实例ID: {}, 步骤编码: {}", processInstanceId, stepCode);

            // 获取该流程实例下的所有步骤实例
            List<ProcessStepInstance> stepInstances = processStepInstanceService.list(
                    new LambdaQueryWrapper<ProcessStepInstance>()
                            .eq(ProcessStepInstance::getProcessInstanceId, processInstanceId)
                            .orderByAsc(ProcessStepInstance::getId));

            if (stepInstances.isEmpty()) {
                log.debug("未找到任何步骤实例，流程实例ID: {}", processInstanceId);
                return null;
            }

            // 根据步骤编码匹配
            ProcessStepInstance matchedInstance = null;
            for (ProcessStepInstance instance : stepInstances) {
                String instanceStepCode = getStepCodeFromInstance(instance);
                if (stepCode.equals(instanceStepCode)) {
                    matchedInstance = instance;
                    break;
                }
            }

            if (matchedInstance != null) {
                log.debug("找到匹配的步骤实例，实例ID: {}, 步骤名称: {}",
                        matchedInstance.getId(), matchedInstance.getStepName());
            } else {
                log.debug("未找到匹配的步骤实例，流程实例ID: {}, 步骤编码: {}, 总步骤数: {}",
                        processInstanceId, stepCode, stepInstances.size());
            }

            return matchedInstance;

        } catch (Exception e) {
            log.error("获取步骤实例失败，流程实例ID: {}, 步骤编码: {}, 错误: {}",
                    processInstanceId, stepCode, e.getMessage(), e);
            return null;
        }
    }


    /**
     * 计算持续时间
     */
    protected Long calculateDuration(ProcessStepInstance stepInstance) {
        if (stepInstance == null || stepInstance.getStartTime() == null) {
            return null;
        }

        Date endTime = stepInstance.getEndTime() != null ? stepInstance.getEndTime() : new Date();
        return endTime.getTime() - stepInstance.getStartTime().getTime();
    }

    /**
     * 从步骤实例获取步骤编码
     *
     * @param instance 步骤实例
     * @return 步骤编码，如果无法获取则返回null
     */
    protected String getStepCodeFromInstance(ProcessStepInstance instance) {
        if (instance == null || instance.getStepDefinitionId() == null) {
            return null;
        }

        // 直接通过stepDefinitionId查询，因为它是唯一主键
        ProcessStepDefinition stepDefinition = processStepDefinitionService.getById(instance.getStepDefinitionId());
        return stepDefinition != null ? stepDefinition.getStepCode() : null;
    }

    /**
     * 根据stepCode和processInstanceId查找对应的stepInstance
     * 查询逻辑：stepCode -> step_definition_id -> stepInstance
     *
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 匹配的步骤实例，如果未找到则返回null
     */
    protected ProcessStepInstance findStepInstanceByCode(String stepCode, Long processInstanceId) {
        // 直接调用已有的getStepInstance方法，避免重复实现
        return getStepInstance(processInstanceId, stepCode);
    }

    // ==================== 暂存数据相关方法 ====================

    /**
     * 获取暂存数据的默认实现
     * 根据数据类型分发到不同的处理方法
     */
    @Override
    public StepDataResponse getDraftData(Long userId, Long planId, String stepCode) {
        String draftDataType = getDraftDataType();

        if (DraftDataTypeEnum.FORM.getCode().equals(draftDataType)) {
            return getFormDraftData(userId, planId, stepCode);
        } else if (DraftDataTypeEnum.LIST.getCode().equals(draftDataType)) {
            return getListDraftData(userId, planId, stepCode);
        } else {
            return getUnsupportedDraftData(planId, stepCode, draftDataType);
        }
    }

    /**
     * 获取表单类型暂存数据（现有逻辑）
     */
    protected StepDataResponse getFormDraftData(Long userId, Long planId, String stepCode) {
        String draftKey = planId + "_" + stepCode;
        log.debug("获取表单类型暂存数据，用户ID: {}, 暂存键: {}", userId, draftKey);

        EvaluatePlanTaskDto taskDto = draftStorageService.getDraft(
                DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                userId,
                draftKey,
                EvaluatePlanTaskDto.class
        );

        // 构建 StepDataResponse
        StepDataResponse response = StepDataResponse.builder()
                .stepCode(stepCode)
                .evaluateType(getEvaluateType())
                .build();

        // 添加暂存相关的扩展数据
        response.addStepData("draftKey", draftKey);
        response.addStepData("planId", planId);
        response.addStepData("step", stepCode);
        response.addStepData("data", taskDto);
        response.addStepData("exists", taskDto != null);
        response.addStepData("description", String.format("评估计划[%d]的步骤[%s]", planId, stepCode));
        response.addStepData("draftDataType", getDraftDataType());

        return response;
    }

    /**
     * 获取列表类型暂存数据（待子类实现）
     */
    protected StepDataResponse getListDraftData(Long userId, Long planId, String stepCode) {
        // 默认实现：暂不支持列表类型
        log.warn("列表类型暂存数据获取暂未实现，步骤: {}, 处理器: {}", stepCode, this.getClass().getSimpleName());
        return getUnsupportedDraftData(planId, stepCode, DraftDataTypeEnum.LIST.getCode());
    }

    /**
     * 不支持的暂存数据类型处理
     */
    protected StepDataResponse getUnsupportedDraftData(Long planId, String stepCode, String draftDataType) {
        StepDataResponse response = StepDataResponse.builder()
                .stepCode(stepCode)
                .evaluateType(getEvaluateType())
                .build();

        // 添加不支持类型的扩展数据
        response.addStepData("planId", planId);
        response.addStepData("step", stepCode);
        response.addStepData("data", null);
        response.addStepData("exists", false);
        response.addStepData("description", String.format("评估计划[%d]的步骤[%s]", planId, stepCode));
        response.addStepData("draftDataType", draftDataType);
        response.addStepData("message", "当前步骤不支持暂存数据获取，数据类型: " + draftDataType);

        return response;
    }

    // ==================== 保存暂存数据相关方法 ====================

    /**
     * 保存暂存数据的默认实现
     * 根据数据类型分发到不同的处理方法
     */
    @Override
    public Map<String, Object> saveDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        String draftDataType = getDraftDataType();

        if (DraftDataTypeEnum.FORM.getCode().equals(draftDataType)) {
            return saveFormDraftData(userId, planId, stepCode, stepData);
        } else if (DraftDataTypeEnum.LIST.getCode().equals(draftDataType)) {
            return saveListDraftData(userId, planId, stepCode, stepData);
        } else {
            return saveUnsupportedDraftData(planId, stepCode, draftDataType);
        }
    }

    /**
     * 保存表单类型暂存数据（现有逻辑）
     */
    protected Map<String, Object> saveFormDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        String draftKey = planId + "_" + stepCode;
        log.info("开始保存表单类型暂存数据，用户ID: {}, 暂存键: {}", userId, draftKey);

        try {
            // 永久保存（设置365天过期时间）
            boolean success = draftStorageService.saveDraft(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKey,
                    stepData,
                    365 * 24, // 365天
                    TimeUnit.HOURS
            );

            Map<String, Object> result = new HashMap<>();
            result.put("draftKey", draftKey);
            result.put("planId", planId);
            result.put("step", stepCode);
            result.put("success", success);
            result.put("description", String.format("评估计划[%d]的步骤[%s]", planId, stepCode));
            result.put("draftDataType", getDraftDataType());

            if (success) {
                log.info("保存表单类型暂存数据成功，用户ID: {}, 暂存键: {}", userId, draftKey);
            } else {
                log.warn("保存表单类型暂存数据失败，用户ID: {}, 暂存键: {}", userId, draftKey);
            }

            return result;

        } catch (Exception e) {
            log.error("保存表单类型暂存数据异常，用户ID: {}, 暂存键: {}, 错误: {}", userId, draftKey, e.getMessage(), e);
            throw new ServiceException("保存暂存数据失败：" + e.getMessage());
        }
    }

    /**
     * 保存列表类型暂存数据（待子类实现）
     */
    protected Map<String, Object> saveListDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        // 默认实现：暂不支持列表类型
        log.warn("列表类型暂存数据保存暂未实现，步骤: {}, 处理器: {}", stepCode, this.getClass().getSimpleName());
        return saveUnsupportedDraftData(planId, stepCode, DraftDataTypeEnum.LIST.getCode());
    }

    /**
     * 不支持的暂存数据类型保存处理
     */
    protected Map<String, Object> saveUnsupportedDraftData(Long planId, String stepCode, String draftDataType) {
        Map<String, Object> result = new HashMap<>();
        result.put("planId", planId);
        result.put("step", stepCode);
        result.put("success", false);
        result.put("description", String.format("评估计划[%d]的步骤[%s]", planId, stepCode));
        result.put("draftDataType", draftDataType);
        result.put("message", "当前步骤不支持暂存数据保存，数据类型: " + draftDataType);

        return result;
    }

}
