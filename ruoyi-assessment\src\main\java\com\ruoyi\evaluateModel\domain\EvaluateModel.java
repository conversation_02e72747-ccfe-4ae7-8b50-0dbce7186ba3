package com.ruoyi.evaluateModel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 评估模型对象 dsa_evaluate_model
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_model")
public class EvaluateModel extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 标题 */
    @NotNull(message = "模型标题不能为空", groups = {AddGroup.class, EditGroup.class})
    @Excel(name = "标题")
    @TableField(value = "title")
    private String title;

    /** 评估类型ID */
    @NotNull(message = "评估类型不能为空", groups = {AddGroup.class})
    @Excel(name = "评估类型ID")
    @TableField(value = "type_id")
    private Long typeId;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;

    /** 是否为当前启用的模型，1-启用 0-未启用（每个type_id下只能有一个模型启用） */
    @Excel(name = "是否启用")
    @TableField(value = "is_enabled")
    private Integer isEnabled;






}