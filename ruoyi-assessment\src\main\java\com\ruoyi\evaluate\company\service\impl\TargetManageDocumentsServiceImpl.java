package com.ruoyi.evaluate.company.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.company.mapper.TargetManageDocumentsMapper;
import com.ruoyi.evaluate.company.domain.TargetManageDocuments;
import com.ruoyi.evaluate.company.service.ITargetManageDocumentsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 被评估单位文档信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class TargetManageDocumentsServiceImpl extends ServiceImpl<TargetManageDocumentsMapper, TargetManageDocuments> implements ITargetManageDocumentsService {

}
