package com.ruoyi.evaluateModel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 评估项分类对象 dsa_evaluate_item_category
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@TableName("dsa_evaluate_item_category")
public class EvaluateItemCategory extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 评估模型ID */
    @NotNull(message = "模型不能为空", groups = {AddGroup.class})
    @Excel(name = "评估模型ID")
    @TableField(value = "model_id")
    private Long modelId;

    /** 父菜单ID */
    @TableField(value = "parent_id")
    private Long parentId;

    /** 评估项分类名称 */
    @NotNull(message = "评估项分类名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Excel(name = "评估项分类名称")
    @TableField(value = "category_name")
    private String categoryName;

    /** 排序值，越大越靠前 */
    @Excel(name = "排序值，越大越靠前")
    @TableField(value = "sort")
    private Long sort;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;

}