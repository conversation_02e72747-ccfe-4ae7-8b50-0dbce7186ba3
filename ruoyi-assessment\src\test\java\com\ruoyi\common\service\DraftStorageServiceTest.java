package com.ruoyi.common.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.service.impl.DraftStorageServiceImpl;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.system.service.ISysConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 暂存服务单元测试
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@ExtendWith(MockitoExtension.class)
class DraftStorageServiceTest {

    @Mock
    private RedisCache redisCache;

    @Mock
    private ISysConfigService sysConfigService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private DraftStorageServiceImpl draftStorageService;

    private static final String BUSINESS_TYPE = DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK;
    private static final Long USER_ID = 1L;
    private static final String DRAFT_KEY = "test_draft_001";

    private EvaluatePlanTaskDto testTaskDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testTaskDto = new EvaluatePlanTaskDto();
        testTaskDto.setOrgId(1L);
        testTaskDto.setEvaluateOrgId(2L);
        testTaskDto.setModelId(100L);
        testTaskDto.setName("测试评估计划任务");
        testTaskDto.setTaskDescription("这是一个测试任务");

        // 设置默认配置
        when(sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_DRAFT_ENABLED))
                .thenReturn("1");
        when(sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_DEFAULT_EXPIRE_HOURS))
                .thenReturn("24");
        when(sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_MAX_COUNT_PER_USER))
                .thenReturn("100");
        when(sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_MAX_SIZE_MB))
                .thenReturn("1");
    }

    @Test
    void testSaveDraft_Success() throws Exception {
        // 准备数据
        when(redisCache.getCacheSet(anyString())).thenReturn(new HashSet<>());
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"data\"}");

        // 执行测试
        boolean result = draftStorageService.saveDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY, testTaskDto);

        // 验证结果
        assertTrue(result);

        // 验证Redis操作被调用
        verify(redisCache, times(2)).setCacheObject(anyString(), any(), anyLong(), any(TimeUnit.class));
        verify(redisCache, times(1)).expire(anyString(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSaveDraft_DraftDisabled() {
        // 设置暂存功能禁用
        when(sysConfigService.selectConfigByKey(DraftConstants.CONFIG_KEY_DRAFT_ENABLED))
                .thenReturn("0");

        // 执行测试
        boolean result = draftStorageService.saveDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY, testTaskDto);

        // 验证结果
        assertFalse(result);

        // 验证没有进行Redis操作
        verify(redisCache, never()).setCacheObject(anyString(), any(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSaveDraft_InvalidParams() {
        // 测试空业务类型
        boolean result1 = draftStorageService.saveDraft(null, USER_ID, DRAFT_KEY, testTaskDto);
        assertFalse(result1);

        // 测试空用户ID
        boolean result2 = draftStorageService.saveDraft(BUSINESS_TYPE, null, DRAFT_KEY, testTaskDto);
        assertFalse(result2);

        // 测试空暂存键
        boolean result3 = draftStorageService.saveDraft(BUSINESS_TYPE, USER_ID, null, testTaskDto);
        assertFalse(result3);

        // 测试空数据
        boolean result4 = draftStorageService.saveDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY, null);
        assertFalse(result4);
    }

    @Test
    void testGetDraft_Success() {
        // 准备数据
        when(redisCache.getCacheObject(anyString())).thenReturn(testTaskDto);

        // 执行测试
        EvaluatePlanTaskDto result = draftStorageService.getDraft(
                BUSINESS_TYPE, USER_ID, DRAFT_KEY, EvaluatePlanTaskDto.class);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试评估计划任务", result.getName());
    }

    @Test
    void testGetDraft_NotExists() {
        // 设置Redis返回null
        when(redisCache.getCacheObject(anyString())).thenReturn(null);

        // 执行测试
        EvaluatePlanTaskDto result = draftStorageService.getDraft(
                BUSINESS_TYPE, USER_ID, DRAFT_KEY, EvaluatePlanTaskDto.class);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testDeleteDraft_Success() {
        // 执行测试
        boolean result = draftStorageService.deleteDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY);

        // 验证结果
        assertTrue(result);

        // 验证Redis删除操作被调用
        verify(redisCache, times(2)).deleteObject(anyString());
    }

    @Test
    void testGetUserDraftKeys_Success() {
        // 准备数据
        Set<Object> mockKeys = new HashSet<>();
        mockKeys.add("draft1");
        mockKeys.add("draft2");
        mockKeys.add("draft3");

        when(redisCache.getCacheSet(anyString())).thenReturn(mockKeys);

        // 执行测试
        List<String> result = draftStorageService.getUserDraftKeys(BUSINESS_TYPE, USER_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("draft1"));
        assertTrue(result.contains("draft2"));
        assertTrue(result.contains("draft3"));
    }

    @Test
    void testGetUserDraftKeys_Empty() {
        // 设置Redis返回空集合
        when(redisCache.getCacheSet(anyString())).thenReturn(new HashSet<>());

        // 执行测试
        List<String> result = draftStorageService.getUserDraftKeys(BUSINESS_TYPE, USER_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testExistsDraft_True() {
        // 设置Redis返回true
        when(redisCache.hasKey(anyString())).thenReturn(true);

        // 执行测试
        boolean result = draftStorageService.existsDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testExistsDraft_False() {
        // 设置Redis返回false
        when(redisCache.hasKey(anyString())).thenReturn(false);

        // 执行测试
        boolean result = draftStorageService.existsDraft(BUSINESS_TYPE, USER_ID, DRAFT_KEY);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetDraftTtl_Success() {
        // 设置Redis返回TTL
        when(redisCache.getExpire(anyString())).thenReturn(3600L);

        // 执行测试
        long result = draftStorageService.getDraftTtl(BUSINESS_TYPE, USER_ID, DRAFT_KEY);

        // 验证结果
        assertEquals(3600L, result);
    }

    @Test
    void testBatchSaveDrafts_Success() throws Exception {
        // 准备数据
        Map<String, Object> drafts = new HashMap<>();
        drafts.put("draft1", testTaskDto);
        drafts.put("draft2", testTaskDto);

        when(redisCache.getCacheSet(anyString())).thenReturn(new HashSet<>());
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"data\"}");

        // 执行测试
        int result = draftStorageService.batchSaveDrafts(BUSINESS_TYPE, USER_ID, drafts, 24, TimeUnit.HOURS);

        // 验证结果
        assertEquals(2, result);
    }

    @Test
    void testBatchGetDrafts_Success() {
        // 准备数据
        List<String> draftKeys = Arrays.asList("draft1", "draft2");

        when(redisCache.getCacheObject(anyString())).thenReturn(testTaskDto);

        // 执行测试
        Map<String, EvaluatePlanTaskDto> result = draftStorageService.batchGetDrafts(
                BUSINESS_TYPE, USER_ID, draftKeys, EvaluatePlanTaskDto.class);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("draft1"));
        assertTrue(result.containsKey("draft2"));
    }

    @Test
    void testBatchDeleteDrafts_Success() {
        // 准备数据
        List<String> draftKeys = Arrays.asList("draft1", "draft2");

        // 执行测试
        int result = draftStorageService.batchDeleteDrafts(BUSINESS_TYPE, USER_ID, draftKeys);

        // 验证结果
        assertEquals(2, result);

        // 验证删除操作被调用
        verify(redisCache, times(4)).deleteObject(anyString()); // 每个draft删除2次（数据+元数据）
    }
}
