package com.ruoyi.dataItem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataItemProcessingActivityMapper;
import com.ruoyi.dataItem.domain.DataItemProcessingActivity;
import com.ruoyi.dataItem.service.IDataItemProcessingActivityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 数据处理活动Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class DataItemProcessingActivityServiceImpl extends ServiceImpl<DataItemProcessingActivityMapper, DataItemProcessingActivity> implements IDataItemProcessingActivityService {

}
