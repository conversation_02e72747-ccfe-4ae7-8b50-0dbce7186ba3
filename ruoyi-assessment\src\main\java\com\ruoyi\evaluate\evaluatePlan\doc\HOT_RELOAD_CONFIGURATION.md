# 处理器热重载使用指南

## 🔥 **问题解决**

原来的 `@PostConstruct` 自动注册模式会在应用启动时一次性注册所有处理器，这导致 JRebel 热部署时新的处理器类无法被重新注册。

现在改为**按需懒加载 + 自动监控**模式，完美支持热部署，无需任何配置。

## 🚀 **使用方式**

### 完全自动化（无需配置）

系统启动后会自动启用以下检测机制：

**自动检测机制**：
- ✅ **文件监控**：监控 `.class` 文件变化
- ✅ **定时检查**：每5秒检查处理器数量变化
- ✅ **Spring事件**：监听上下文刷新事件
- ✅ **类加载器检测**：检测JRebel等工具的类重载

**使用流程**：
```java
// 正常调用处理器，系统会自动处理一切
IStepDataProcessor processor = processorFactory.getProcessor("data_security_plan", "create_plan");
```

#### 通过代码刷新：
```java
@Autowired
private ProcessorHotReloadHelper hotReloadHelper;

// 刷新注册表
hotReloadHelper.refreshProcessors();

// 强制重新注册
hotReloadHelper.forceReregisterAll();

// 测试功能
hotReloadHelper.testProcessors();
```

## 🔄 **工作原理**

### 懒加载机制

```java
public IStepDataProcessor findProcessor(String evaluateType, String stepCode) {
    // 每次查找时都会检查是否需要初始化
    ensureInitialized();
    
    // 然后执行正常的查找逻辑
    // ...
}

private void ensureInitialized() {
    if (!initialized || shouldReregister()) {
        synchronized (this) {
            // 双重检查锁定
            if (!initialized || shouldReregister()) {
                // 重新注册所有处理器
                clearRegistry();
                registerAllProcessors();
                initialized = true;
            }
        }
    }
}
```

### 变化检测

系统会检测处理器数量是否发生变化：

```java
private boolean shouldReregister() {
    // 检查Spring容器中的处理器数量是否与注册表中的不一致
    return allProcessors.size() != getTotalRegisteredProcessors();
}
```

## 📊 **开发工作流**

### JRebel 热部署工作流：

1. **修改处理器代码**
   ```java
   @Component
   public class CreatePlanProcessor extends AbstractStepDataProcessor {
       // 修改业务逻辑
       @Override
       protected void processSpecificStepData(...) {
           // 新的处理逻辑
       }
   }
   ```

2. **JRebel 自动热部署**
   - JRebel 检测到类文件变化
   - 自动重新加载修改的类

3. **处理器自动重新注册**
   - 下次调用处理器时自动检测变化
   - 重新注册所有处理器
   - 使用最新的处理器实例

4. **验证修改效果**
   ```bash
   # 测试修改后的处理器
   curl -X POST http://localhost:8080/dev/processor/test
   ```

## 🛠️ **开发工具**

### REST API 接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/dev/processor/refresh` | POST | 刷新处理器注册表 |
| `/dev/processor/force-reregister` | POST | 强制重新注册 |
| `/dev/processor/test` | POST | 测试处理器功能 |
| `/dev/processor/status` | GET | 获取处理器状态 |
| `/dev/processor/report` | GET | 获取状态报告 |
| `/dev/processor/hot-reload-status` | GET | 检查热重载状态 |

### 日志监控

启用调试日志查看详细信息：

```yaml
logging:
  level:
    com.ruoyi.evaluate.evaluatePlan.service.processor: DEBUG
```

## 🔒 **安全考虑**

### 生产环境保护

- 热重载功能只在配置启用时才生效
- 开发接口通过 `@ConditionalOnProperty` 注解保护
- 生产环境默认关闭，避免安全风险

### 性能影响

- 懒加载只在第一次使用时执行
- 后续调用直接使用缓存的注册表
- 变化检测开销很小（只比较数量）

## 🐛 **故障排查**

### 常见问题

1. **热重载不生效**
   ```bash
   # 检查配置是否启用
   curl -X GET http://localhost:8080/dev/processor/hot-reload-status
   
   # 手动强制刷新
   curl -X POST http://localhost:8080/dev/processor/force-reregister
   ```

2. **处理器找不到**
   ```bash
   # 查看处理器状态
   curl -X GET http://localhost:8080/dev/processor/status
   
   # 测试处理器功能
   curl -X POST http://localhost:8080/dev/processor/test
   ```

3. **注册表状态异常**
   ```bash
   # 获取详细报告
   curl -X GET http://localhost:8080/dev/processor/report
   ```

## 💡 **最佳实践**

1. **开发环境启用热重载**：提高开发效率
2. **生产环境关闭热重载**：确保系统稳定性
3. **使用REST接口验证**：修改后及时验证效果
4. **监控日志输出**：了解注册表状态变化
5. **定期测试功能**：确保处理器正常工作

## 🎯 **优势总结**

- ✅ **完美支持JRebel**：代码修改后自动重新注册
- ✅ **按需加载**：只在需要时才初始化，提高启动速度
- ✅ **开发友好**：提供丰富的开发工具和接口
- ✅ **生产安全**：生产环境自动关闭热重载功能
- ✅ **性能优化**：懒加载 + 缓存机制，性能影响最小
