# 步骤实例下一步配置功能使用指南

## 概述

本文档说明了如何使用"步骤实例下一步配置"功能。该功能允许在**流程执行过程中**，每个步骤的执行人可以动态设置下一步的执行信息，包括负责人、负责部门、截止日期等。

## 功能特性

### 1. 核心概念

- **动态配置**：在流程执行过程中，每个步骤都可以设置下一步的执行信息
- **实例级别**：配置信息存储在 `ProcessStepInstance`（步骤实例）中，每次流程执行都是独立的
- **统一提交**：4个字段作为一个整体在每个步骤完成时统一提交

### 2. 新增字段

在 `ProcessStepInstance` 和 `EvaluatePlanTaskDto` 中新增了以下4个字段：

- **nextStepAssignee**: 下一步负责人
- **nextStepDept**: 下一步负责部门  
- **nextStepDeadlineDays**: 下一步截止日期（相对天数）
- **setTaskDeadline**: 是否设置任务截止日期（0-否，1-是）

### 3. 核心服务

- **IStepInstanceNextConfigService**: 步骤实例下一步配置服务接口
- **StepInstanceNextConfigServiceImpl**: 步骤实例下一步配置服务实现类
- **StepInstanceNextConfigController**: 步骤实例下一步配置控制器

## 数据库变更

### 1. 执行SQL脚本

```sql
-- 执行脚本文件
source ruoyi-assessment/src/main/resources/sql/add_next_step_fields_to_instance.sql
```

### 2. 表结构变更

`dsa_process_step_instance` 表新增字段：

```sql
ALTER TABLE `dsa_process_step_instance` 
ADD COLUMN `next_step_assignee` varchar(64) DEFAULT NULL COMMENT '下一步负责人',
ADD COLUMN `next_step_dept` varchar(64) DEFAULT NULL COMMENT '下一步负责部门',
ADD COLUMN `next_step_deadline_days` int(11) DEFAULT NULL COMMENT '下一步截止日期（相对天数）',
ADD COLUMN `set_task_deadline` int(1) DEFAULT NULL COMMENT '是否设置任务截止日期（0-否，1-是）';
```

## API接口使用

### 1. 设置步骤实例的下一步配置

```http
POST /process/stepInstanceNextConfig/setConfig
Content-Type: application/x-www-form-urlencoded

stepInstanceId=1001&nextStepAssignee=张三&nextStepDept=技术部&nextStepDeadlineDays=3&setTaskDeadline=1
```

### 2. 获取步骤实例的下一步配置

```http
GET /process/stepInstanceNextConfig/getConfig?stepInstanceId=1001
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1001,
    "processInstanceId": 2001,
    "stepName": "数据识别",
    "nextStepAssignee": "张三",
    "nextStepDept": "技术部",
    "nextStepDeadlineDays": 3,
    "setTaskDeadline": 1,
    "status": 2
  }
}
```

### 3. 完成步骤并设置下一步配置

```http
POST /process/stepInstanceNextConfig/completeWithNextConfig
Content-Type: application/x-www-form-urlencoded

stepInstanceId=1001&nextStepAssignee=李四&nextStepDept=评估部&nextStepDeadlineDays=5&setTaskDeadline=1&operator=admin&remark=完成数据识别
```

### 4. 获取下一步完整配置信息

```http
GET /process/stepInstanceNextConfig/getFullConfig?stepInstanceId=1001
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "stepInstanceId": 1001,
    "nextStepAssignee": "李四",
    "nextStepDept": "评估部",
    "nextStepDeadlineDays": 5,
    "setTaskDeadline": 1,
    "nextStepDeadline": "2025-08-03T10:30:00.000+00:00"
  }
}
```

### 5. 获取流程实例的配置历史

```http
GET /process/stepInstanceNextConfig/getConfigHistory?processInstanceId=2001
```

## 代码使用示例

### 1. 在Service中使用

```java
@Autowired
private IStepInstanceNextConfigService stepInstanceNextConfigService;

// 设置下一步配置
boolean result = stepInstanceNextConfigService.setNextStepConfig(
    1001L, "张三", "技术部", 3, 1);

// 完成步骤并设置下一步配置
boolean completeResult = stepInstanceNextConfigService.completeStepWithNextConfig(
    1001L, "李四", "评估部", 5, 1, "admin", "完成当前步骤");

// 获取完整配置
Map<String, Object> fullConfig = stepInstanceNextConfigService.getNextStepFullConfig(
    1001L, new Date());
```

### 2. 在流程执行中使用

```java
@Service
public class ProcessExecutionService {
    
    @Autowired
    private IStepInstanceNextConfigService stepInstanceNextConfigService;
    
    public boolean executeStep(Long stepInstanceId, String operator, 
                              String nextAssignee, String nextDept, 
                              Integer deadlineDays, Integer setDeadline) {
        
        // 验证配置
        if (!stepInstanceNextConfigService.validateNextStepConfig(
                nextAssignee, nextDept, deadlineDays, setDeadline)) {
            throw new ServiceException("下一步配置无效");
        }
        
        // 完成当前步骤并设置下一步配置
        return stepInstanceNextConfigService.completeStepWithNextConfig(
            stepInstanceId, nextAssignee, nextDept, deadlineDays, 
            setDeadline, operator, "步骤执行完成");
    }
}
```

## 业务流程

### 1. 典型的执行流程

1. **步骤开始**：流程实例创建，第一个步骤实例状态为"待执行"
2. **步骤执行**：执行人处理当前步骤的业务逻辑
3. **配置下一步**：执行人设置下一步的负责人、部门、截止日期等
4. **完成步骤**：调用 `completeStepWithNextConfig` 完成当前步骤
5. **自动创建下一步**：系统自动创建下一步骤实例，并应用配置的信息
6. **流程继续**：下一步骤开始执行，重复上述流程

### 2. 配置应用逻辑

- 当 `setTaskDeadline=1` 时，系统会根据 `nextStepDeadlineDays` 计算具体的截止日期
- 当 `setTaskDeadline=0` 时，不设置截止日期
- `nextStepAssignee` 会自动设置为下一步骤实例的 `operator` 字段

## 最佳实践

### 1. 配置管理
- 在每个步骤完成时统一设置下一步配置
- 根据业务规则动态决定下一步的负责人和部门
- 合理设置截止日期，避免过短或过长

### 2. 权限控制
- 确保只有当前步骤的执行人才能设置下一步配置
- 验证下一步负责人是否有执行权限
- 记录配置变更的操作日志

### 3. 异常处理
- 处理步骤实例不存在的情况
- 验证配置参数的有效性
- 提供友好的错误提示信息

## 注意事项

1. **数据一致性**：配置信息存储在步骤实例中，每次流程执行都是独立的
2. **权限验证**：确保操作人有权限设置下一步配置
3. **参数验证**：严格验证所有配置参数的有效性
4. **流程控制**：正确处理最后一个步骤的情况
5. **日志记录**：记录所有配置变更和步骤完成的操作日志

## 扩展建议

1. **条件配置**：根据不同的业务条件设置不同的下一步配置
2. **模板配置**：提供常用的下一步配置模板
3. **通知集成**：自动通知下一步负责人
4. **审批流程**：支持多级审批的下一步配置
