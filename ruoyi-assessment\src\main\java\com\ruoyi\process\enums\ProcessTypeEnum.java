package com.ruoyi.process.enums;

/**
 * 评估类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public enum ProcessTypeEnum {

    /**
     * 数据安全风险评估
     */
    EVALUATE("evaluate", "数据安全风险评估"),

    /**
     * 数据安全检查
     */
    INSPECT("inspect", "数据安全检查");

    /** 评估类型编码 */
    private final String code;

    /** 评估类型名称 */
    private final String name;

    ProcessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举值
     */
    public static ProcessTypeEnum getByCode(String code) {
        for (ProcessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        ProcessTypeEnum type = getByCode(code);
        return type != null ? type.getName() : null;
    }

    /**
     * 验证编码是否有效
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
