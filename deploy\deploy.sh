#!/bin/bash

# 若依数据安全评估平台Docker部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|build|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="ruoyi-data-security"
JAR_NAME="ruoyi_admin.jar"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Docker和Docker Compose是否安装
check_requirements() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "错误: Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查必要文件是否存在
check_files() {
    local files=("docker-compose.yml" ".env" "Dockerfile")
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_message $RED "错误: 文件 $file 不存在"
            exit 1
        fi
    done
}

# 检查JAR包是否存在
check_jar() {
    print_message $BLUE "检查JAR包是否存在..."
    
    if [ ! -f "$JAR_NAME" ]; then
        print_message $RED "错误: 当前目录下未找到 $JAR_NAME 文件"
        print_message $YELLOW "请将您编译好的JAR包命名为 $JAR_NAME 并放在当前目录下"
        exit 1
    fi
    
    print_message $GREEN "找到JAR包: $JAR_NAME"
}

# 启动服务
start_services() {
    # 先检查JAR包
    check_jar
    
    print_message $BLUE "启动Docker服务..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "服务启动成功"
        print_message $YELLOW "请等待服务完全启动（约1-2分钟）"
        print_message $BLUE "访问地址: http://localhost:8080"
        print_message $BLUE "默认账号: admin/admin123"
    else
        print_message $RED "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    print_message $BLUE "停止Docker服务..."
    docker-compose down
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "服务停止成功"
    else
        print_message $RED "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    print_message $BLUE "重启Docker服务..."
    docker-compose restart
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "服务重启成功"
    else
        print_message $RED "服务重启失败"
        exit 1
    fi
}

# 构建Docker镜像
build_images() {
    # 先检查JAR包
    check_jar
    
    print_message $BLUE "构建Docker镜像..."
    docker-compose build --no-cache
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "Docker镜像构建成功"
    else
        print_message $RED "Docker镜像构建失败"
        exit 1
    fi
}

# 查看日志
view_logs() {
    local service=$2
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f $service
    fi
}

# 查看服务状态
check_status() {
    print_message $BLUE "服务状态:"
    docker-compose ps
    
    print_message $BLUE "\n容器健康状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 清理数据
clean_data() {
    print_message $YELLOW "警告: 此操作将删除所有数据，包括数据库数据！"
    read -p "确认继续吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message $BLUE "停止服务并清理数据..."
        docker-compose down -v
        docker system prune -f
        print_message $GREEN "数据清理完成"
    else
        print_message $BLUE "操作已取消"
    fi
}

# 显示帮助信息
show_help() {
    echo "若依数据安全评估平台Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  check        检查JAR包是否存在"
    echo "  build        构建Docker镜像"
    echo "  start        启动所有服务"
    echo "  stop         停止所有服务"
    echo "  restart      重启所有服务"
    echo "  logs [服务]  查看日志（可指定服务名）"
    echo "  status       查看服务状态"
    echo "  clean        清理所有数据（危险操作）"
    echo "  help         显示此帮助信息"
    echo ""
    echo "使用前准备:"
    echo "  请将编译好的JAR包命名为 $JAR_NAME 并放在当前目录下"
    echo ""
    echo "示例:"
    echo "  $0 check         # 检查JAR包是否存在"
    echo "  $0 start         # 启动所有服务"
    echo "  $0 logs ruoyi-app # 查看应用日志"
}

# 主函数
main() {
    check_requirements
    check_files
    
    case "$1" in
        "check")
            check_jar
            ;;
        "build")
            build_images
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            view_logs $@
            ;;
        "status")
            check_status
            ;;
        "clean")
            clean_data
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            print_message $RED "错误: 未知命令 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main $@
