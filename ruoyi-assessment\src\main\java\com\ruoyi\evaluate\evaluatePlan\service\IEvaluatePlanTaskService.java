package com.ruoyi.evaluate.evaluatePlan.service;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;

/**
 * 评估计划任务Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IEvaluatePlanTaskService extends IService<EvaluatePlanTask> {

    /**
     * 新增评估计划任务（包含业务逻辑处理）
     *
     * @param evaluatePlanTaskDto 评估计划任务DTO
     * @return 结果
     */
    Long createPlanTask(EvaluatePlanTaskDto evaluatePlanTaskDto);

}
