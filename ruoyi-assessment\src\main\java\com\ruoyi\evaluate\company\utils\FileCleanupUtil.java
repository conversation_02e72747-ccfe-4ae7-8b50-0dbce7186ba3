package com.ruoyi.evaluate.company.utils;

import com.ruoyi.common.config.RuoYiConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件清理工具类
 * 用于清理错误位置的文件
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class FileCleanupUtil {

    /**
     * 清理错误位置的targetCompany文件
     * 
     * 问题描述：
     * 1. 正常路径：uploadPath/zip/targetCompany/2025/07/24 (ZIP文件)
     * 2. 正常路径：uploadPath/zip/targetCompany/extract/20250724_101620 (解压文件)
     * 3. 错误路径：uploadPath/targetCompany/2025/07/24 (Excel文件，应该删除)
     */
    public static void cleanupIncorrectTargetCompanyFiles() {
        String incorrectPath = RuoYiConfig.getProfile() + "/targetCompany";
        
        log.info("开始清理错误位置的targetCompany文件，路径: {}", incorrectPath);
        
        try {
            Path path = Paths.get(incorrectPath);
            if (!Files.exists(path)) {
                log.info("错误路径不存在，无需清理: {}", incorrectPath);
                return;
            }
            
            List<Path> filesToDelete = new ArrayList<>();
            List<Path> dirsToDelete = new ArrayList<>();
            
            // 遍历目录，收集需要删除的文件和目录
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    String fileName = file.getFileName().toString().toLowerCase();
                    if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                        filesToDelete.add(file);
                        log.info("发现需要删除的Excel文件: {}", file.toAbsolutePath());
                    } else {
                        filesToDelete.add(file);
                        log.info("发现需要删除的其他文件: {}", file.toAbsolutePath());
                    }
                    return FileVisitResult.CONTINUE;
                }
                
                @Override
                public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                    if (!dir.equals(path)) { // 不删除根目录本身
                        dirsToDelete.add(dir);
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
            
            // 删除文件
            int deletedFiles = 0;
            for (Path file : filesToDelete) {
                try {
                    if (Files.deleteIfExists(file)) {
                        deletedFiles++;
                        log.info("已删除文件: {}", file.toAbsolutePath());
                    }
                } catch (IOException e) {
                    log.error("删除文件失败: {}, 错误: {}", file.toAbsolutePath(), e.getMessage());
                }
            }
            
            // 删除空目录（从最深层开始）
            int deletedDirs = 0;
            for (int i = dirsToDelete.size() - 1; i >= 0; i--) {
                Path dir = dirsToDelete.get(i);
                try {
                    if (Files.deleteIfExists(dir)) {
                        deletedDirs++;
                        log.info("已删除目录: {}", dir.toAbsolutePath());
                    }
                } catch (IOException e) {
                    log.warn("删除目录失败: {}, 错误: {}", dir.toAbsolutePath(), e.getMessage());
                }
            }
            
            // 最后删除根目录（如果为空）
            try {
                if (Files.deleteIfExists(path)) {
                    deletedDirs++;
                    log.info("已删除根目录: {}", path.toAbsolutePath());
                }
            } catch (IOException e) {
                log.warn("删除根目录失败: {}, 错误: {}", path.toAbsolutePath(), e.getMessage());
            }
            
            log.info("清理完成，删除了{}个文件，{}个目录", deletedFiles, deletedDirs);
            
        } catch (IOException e) {
            log.error("清理错误位置的targetCompany文件时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查并报告文件分布情况
     */
    public static void reportFileDistribution() {
        log.info("=== 文件分布情况报告 ===");
        
        // 检查正确的ZIP文件路径
        String correctZipPath = RuoYiConfig.getZipPath() + "/targetCompany";
        reportDirectoryContents("正确的ZIP文件路径", correctZipPath);
        
        // 检查错误的targetCompany路径
        String incorrectPath = RuoYiConfig.getProfile() + "/targetCompany";
        reportDirectoryContents("错误的targetCompany路径", incorrectPath);
        
        log.info("=== 文件分布情况报告结束 ===");
    }
    
    /**
     * 报告目录内容
     */
    private static void reportDirectoryContents(String description, String dirPath) {
        log.info("检查 {}: {}", description, dirPath);
        
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                log.info("  目录不存在");
                return;
            }
            
            if (!Files.isDirectory(path)) {
                log.info("  路径不是目录");
                return;
            }
            
            Files.walk(path)
                 .filter(Files::isRegularFile)
                 .forEach(file -> {
                     try {
                         long size = Files.size(file);
                         String relativePath = path.relativize(file).toString();
                         log.info("  文件: {} ({}字节)", relativePath, size);
                     } catch (IOException e) {
                         log.warn("  无法获取文件信息: {}", file.toAbsolutePath());
                     }
                 });
                 
        } catch (IOException e) {
            log.error("检查目录时发生异常: {}", e.getMessage());
        }
    }
}
