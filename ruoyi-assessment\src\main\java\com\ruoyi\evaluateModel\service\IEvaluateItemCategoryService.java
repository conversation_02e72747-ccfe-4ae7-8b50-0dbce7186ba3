package com.ruoyi.evaluateModel.service;

import com.ruoyi.evaluateModel.domain.EvaluateItemCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 评估项分类Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IEvaluateItemCategoryService extends IService<EvaluateItemCategory> {

    /**
     * 批量更新排序
     *
     * @param categories 需要更新排序的分类列表
     * @return 更新结果
     */
    boolean updateSort(List<EvaluateItemCategory> categories);
}
