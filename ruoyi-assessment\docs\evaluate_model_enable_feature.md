# 评估模型启用功能说明

## 功能概述

为了确保每个评估类型（type_id）下同时只能有一个评估模型处于启用状态，我们为 `EvaluateModel` 实体添加了新的启用状态管理功能。

## 主要变更

### 1. 数据库变更

- 在 `dsa_evaluate_model` 表中添加了 `is_enabled` 字段
- 字段类型：`int(1)`，默认值：`0`
- 字段含义：`1-启用，0-未启用`
- 约束：每个 `type_id` 下只能有一个模型的 `is_enabled = 1`

### 2. 实体类变更

在 `EvaluateModel.java` 中添加了：
```java
/** 是否为当前启用的模型，1-启用 0-未启用（每个type_id下只能有一个模型启用） */
@Excel(name = "是否启用")
@TableField(value = "is_enabled")
private Integer isEnabled;
```

### 3. 业务逻辑变更

#### 3.1 新增模型时的逻辑
- 新添加的模型默认设置为启用状态（`is_enabled = 1`）
- 同时将同一 `type_id` 下的其他所有模型设置为未启用状态（`is_enabled = 0`）

#### 3.2 启用模型的方法
新增了 `enableModel(Long modelId)` 方法：
- 将指定模型设置为启用状态
- 同时将同一 `type_id` 下的其他所有模型设置为未启用状态
- 支持事务回滚

### 4. API 接口变更

#### 4.1 新增接口
```
PUT /evaluateType/evaluateType/enableModel/{modelId}
```
- 功能：启用指定的评估模型
- 权限：`evaluateType:evaluateType:edit`
- 参数：`modelId` - 要启用的模型ID
- 返回：操作结果

#### 4.2 查询逻辑变更
- `EvaluateTypeService.listWithModel()` 方法现在只返回启用状态的模型
- 查询条件增加了 `is_enabled = 1` 的过滤

## 使用方法

### 1. 添加新的评估模型
```java
EvaluateModel model = new EvaluateModel();
model.setTitle("新评估模型");
model.setTypeId(1L);
// 不需要手动设置 isEnabled，系统会自动设置为启用并禁用其他模型
evaluateModelService.save(model);
```

### 2. 启用某个已存在的模型
```java
// 通过API调用
PUT /evaluateType/evaluateType/enableModel/123

// 或通过Service调用
boolean result = evaluateModelService.enableModel(123L);
```

### 3. 查询启用的模型
```java
// 查询评估类型及其启用的模型
List<EvaluateType> types = evaluateTypeService.listWithModel(new EvaluateType());
// 每个 EvaluateType 的 model 属性将包含该类型下当前启用的模型
```

## 数据库迁移

执行以下SQL脚本来添加新字段并初始化数据：

```sql
-- 添加字段
ALTER TABLE `dsa_evaluate_model` 
ADD COLUMN `is_enabled` int(1) DEFAULT 0 COMMENT '是否为当前启用的模型，1-启用 0-未启用' 
AFTER `status`;

-- 为每个type_id设置一个默认启用的模型
UPDATE `dsa_evaluate_model` m1 
SET `is_enabled` = 1 
WHERE m1.id = (
    SELECT m2.id 
    FROM (
        SELECT id, type_id, 
               ROW_NUMBER() OVER (PARTITION BY type_id ORDER BY id DESC) as rn
        FROM `dsa_evaluate_model` 
        WHERE status = 1 AND del_flag = 0
    ) m2 
    WHERE m2.type_id = m1.type_id AND m2.rn = 1
);
```

## 注意事项

1. **事务安全**：所有涉及启用状态变更的操作都使用了事务，确保数据一致性
2. **并发控制**：在高并发场景下，建议在数据库层面添加唯一约束来防止同一type_id下出现多个启用模型
3. **向后兼容**：现有的查询逻辑已更新，但如果有直接的数据库查询，需要相应更新查询条件
4. **权限控制**：启用模型的操作需要相应的权限，确保只有授权用户可以执行此操作

## 测试建议

1. 测试新增模型时的自动启用逻辑
2. 测试手动启用模型的功能
3. 测试查询时只返回启用模型的逻辑
4. 测试并发场景下的数据一致性
5. 测试事务回滚机制
