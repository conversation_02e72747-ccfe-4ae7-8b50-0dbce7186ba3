<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.planList.mapper.IssuePlanListMapper">
    
    <resultMap type="IssuePlanList" id="IssuePlanListResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="dataItemName"    column="data_item_name"    />
        <result property="dataType"    column="data_type"    />
        <result property="dataProcessingActivity"    column="data_processing_activity"    />
        <result property="dataLevel"    column="data_level"    />
        <result property="infoSystemName"    column="info_system_name"    />
        <result property="importantSystemCategory"    column="important_system_category"    />
        <result property="businessLine"    column="business_line"    />
        <result property="issueCategory"    column="issue_category"    />
        <result property="issueDescription"    column="issue_description"    />
        <result property="issueFoundTime"    column="issue_found_time"    />
        <result property="planRectifyTime"    column="plan_rectify_time"    />
        <result property="actualRectifyTime"    column="actual_rectify_time"    />
        <result property="isRectified"    column="is_rectified"    />
        <result property="rectifyMeasures"    column="rectify_measures"    />
        <result property="rectifyPlan"    column="rectify_plan"    />
        <result property="dataProcessingRisk"    column="data_processing_risk"    />
        <result property="riskAfterRectify"    column="risk_after_rectify"    />
        <result property="inMiitReport"    column="in_miit_report"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectIssuePlanListVo">
        select id, org_id, org_name, data_item_name, data_type, data_processing_activity, data_level, info_system_name, important_system_category, business_line, issue_category, issue_description, issue_found_time, plan_rectify_time, actual_rectify_time, is_rectified, rectify_measures, rectify_plan, data_processing_risk, risk_after_rectify, in_miit_report, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_issue_plan_list
    </sql>

    <select id="selectIssuePlanListList" parameterType="IssuePlanList" resultMap="IssuePlanListResult">
        <include refid="selectIssuePlanListVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="dataItemName != null  and dataItemName != ''"> and data_item_name like concat('%', #{dataItemName}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="dataProcessingActivity != null  and dataProcessingActivity != ''"> and data_processing_activity = #{dataProcessingActivity}</if>
            <if test="dataLevel != null  and dataLevel != ''"> and data_level = #{dataLevel}</if>
            <if test="infoSystemName != null  and infoSystemName != ''"> and info_system_name like concat('%', #{infoSystemName}, '%')</if>
            <if test="importantSystemCategory != null  and importantSystemCategory != ''"> and important_system_category = #{importantSystemCategory}</if>
            <if test="businessLine != null  and businessLine != ''"> and business_line = #{businessLine}</if>
            <if test="issueCategory != null  and issueCategory != ''"> and issue_category = #{issueCategory}</if>
            <if test="issueDescription != null  and issueDescription != ''"> and issue_description = #{issueDescription}</if>
            <if test="issueFoundTime != null "> and issue_found_time = #{issueFoundTime}</if>
            <if test="planRectifyTime != null "> and plan_rectify_time = #{planRectifyTime}</if>
            <if test="actualRectifyTime != null "> and actual_rectify_time = #{actualRectifyTime}</if>
            <if test="isRectified != null "> and is_rectified = #{isRectified}</if>
            <if test="rectifyMeasures != null  and rectifyMeasures != ''"> and rectify_measures = #{rectifyMeasures}</if>
            <if test="rectifyPlan != null  and rectifyPlan != ''"> and rectify_plan = #{rectifyPlan}</if>
            <if test="dataProcessingRisk != null  and dataProcessingRisk != ''"> and data_processing_risk = #{dataProcessingRisk}</if>
            <if test="riskAfterRectify != null  and riskAfterRectify != ''"> and risk_after_rectify = #{riskAfterRectify}</if>
            <if test="inMiitReport != null "> and in_miit_report = #{inMiitReport}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectIssuePlanListById" parameterType="Long" resultMap="IssuePlanListResult">
        <include refid="selectIssuePlanListVo"/>
        where id = #{id}
    </select>

    <insert id="insertIssuePlanList" parameterType="IssuePlanList" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_issue_plan_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="dataItemName != null">data_item_name,</if>
            <if test="dataType != null">data_type,</if>
            <if test="dataProcessingActivity != null">data_processing_activity,</if>
            <if test="dataLevel != null">data_level,</if>
            <if test="infoSystemName != null">info_system_name,</if>
            <if test="importantSystemCategory != null">important_system_category,</if>
            <if test="businessLine != null">business_line,</if>
            <if test="issueCategory != null">issue_category,</if>
            <if test="issueDescription != null">issue_description,</if>
            <if test="issueFoundTime != null">issue_found_time,</if>
            <if test="planRectifyTime != null">plan_rectify_time,</if>
            <if test="actualRectifyTime != null">actual_rectify_time,</if>
            <if test="isRectified != null">is_rectified,</if>
            <if test="rectifyMeasures != null">rectify_measures,</if>
            <if test="rectifyPlan != null">rectify_plan,</if>
            <if test="dataProcessingRisk != null">data_processing_risk,</if>
            <if test="riskAfterRectify != null">risk_after_rectify,</if>
            <if test="inMiitReport != null">in_miit_report,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="dataItemName != null">#{dataItemName},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="dataProcessingActivity != null">#{dataProcessingActivity},</if>
            <if test="dataLevel != null">#{dataLevel},</if>
            <if test="infoSystemName != null">#{infoSystemName},</if>
            <if test="importantSystemCategory != null">#{importantSystemCategory},</if>
            <if test="businessLine != null">#{businessLine},</if>
            <if test="issueCategory != null">#{issueCategory},</if>
            <if test="issueDescription != null">#{issueDescription},</if>
            <if test="issueFoundTime != null">#{issueFoundTime},</if>
            <if test="planRectifyTime != null">#{planRectifyTime},</if>
            <if test="actualRectifyTime != null">#{actualRectifyTime},</if>
            <if test="isRectified != null">#{isRectified},</if>
            <if test="rectifyMeasures != null">#{rectifyMeasures},</if>
            <if test="rectifyPlan != null">#{rectifyPlan},</if>
            <if test="dataProcessingRisk != null">#{dataProcessingRisk},</if>
            <if test="riskAfterRectify != null">#{riskAfterRectify},</if>
            <if test="inMiitReport != null">#{inMiitReport},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateIssuePlanList" parameterType="IssuePlanList">
        update dsa_issue_plan_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="dataItemName != null">data_item_name = #{dataItemName},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="dataProcessingActivity != null">data_processing_activity = #{dataProcessingActivity},</if>
            <if test="dataLevel != null">data_level = #{dataLevel},</if>
            <if test="infoSystemName != null">info_system_name = #{infoSystemName},</if>
            <if test="importantSystemCategory != null">important_system_category = #{importantSystemCategory},</if>
            <if test="businessLine != null">business_line = #{businessLine},</if>
            <if test="issueCategory != null">issue_category = #{issueCategory},</if>
            <if test="issueDescription != null">issue_description = #{issueDescription},</if>
            <if test="issueFoundTime != null">issue_found_time = #{issueFoundTime},</if>
            <if test="planRectifyTime != null">plan_rectify_time = #{planRectifyTime},</if>
            <if test="actualRectifyTime != null">actual_rectify_time = #{actualRectifyTime},</if>
            <if test="isRectified != null">is_rectified = #{isRectified},</if>
            <if test="rectifyMeasures != null">rectify_measures = #{rectifyMeasures},</if>
            <if test="rectifyPlan != null">rectify_plan = #{rectifyPlan},</if>
            <if test="dataProcessingRisk != null">data_processing_risk = #{dataProcessingRisk},</if>
            <if test="riskAfterRectify != null">risk_after_rectify = #{riskAfterRectify},</if>
            <if test="inMiitReport != null">in_miit_report = #{inMiitReport},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIssuePlanListById" parameterType="Long">
        delete from dsa_issue_plan_list where id = #{id}
    </delete>

    <delete id="deleteIssuePlanListByIds" parameterType="String">
        delete from dsa_issue_plan_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>