<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemCategoryMapper">
    
    <resultMap type="DataItemCategory" id="DataItemCategoryResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="level"    column="level"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataItemCategoryVo">
        select id, parent_id, category_name, level, sort, remark, status, create_by, create_time, update_by, update_time from dsa_data_item_category
    </sql>

    <select id="selectDataItemCategoryList" parameterType="DataItemCategory" resultMap="DataItemCategoryResult">
        <include refid="selectDataItemCategoryVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDataItemCategoryById" parameterType="Long" resultMap="DataItemCategoryResult">
        <include refid="selectDataItemCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemCategory" parameterType="DataItemCategory" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="level != null">level,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="level != null">#{level},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataItemCategory" parameterType="DataItemCategory">
        update dsa_data_item_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="level != null">level = #{level},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemCategoryById" parameterType="Long">
        delete from dsa_data_item_category where id = #{id}
    </delete>

    <delete id="deleteDataItemCategoryByIds" parameterType="String">
        delete from dsa_data_item_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>