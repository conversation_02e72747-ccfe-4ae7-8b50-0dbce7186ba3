package com.ruoyi.evaluate.evaluateCompany.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeam;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估团队Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/evaluateCompany/team")
@Api(value = "评估团队控制器", tags = {"评估团队管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateCompanyTeamController extends BaseController {
    private final IEvaluateCompanyTeamService evaluateCompanyTeamService;

    /**
     * 查询评估团队列表
     */
    @ApiOperation("查询评估团队列表")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) EvaluateCompanyTeam evaluateCompanyTeam) {
        startPage();
        List<EvaluateCompanyTeam> list = evaluateCompanyTeamService.list(new QueryWrapper<EvaluateCompanyTeam>(evaluateCompanyTeam).orderByDesc("id"));
        return getDataTable(list);
    }

    /**
     * 获取评估团队详细信息
     */
    @ApiOperation("获取评估团队详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateCompanyTeamService.getById(id));
    }

    /**
     * 新增评估团队
     */
    @ApiOperation("新增评估团队")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:add')")
    @Log(title = "评估团队", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody EvaluateCompanyTeam evaluateCompanyTeam) {
        return toAjax(evaluateCompanyTeamService.save(evaluateCompanyTeam));
    }

    /**
     * 修改评估团队
     */
    @ApiOperation("修改评估团队")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:edit')")
    @Log(title = "评估团队", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated(EditGroup.class) @RequestBody EvaluateCompanyTeam evaluateCompanyTeam) {
        return toAjax(evaluateCompanyTeamService.updateById(evaluateCompanyTeam));
    }

    /**
     * 删除评估团队
     */
    @ApiOperation("删除评估团队")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:remove')")
    @Log(title = "评估团队", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateCompanyTeamService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取导入团队成员Excel文件模板
     *
     * @param response
     */
    @ApiOperation("获取导入团队成员Excel文件模板，文件流形式")
    @GetMapping("/getExcelTemplate")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/evaluate_company/数据安全风险评估-项目组织结构表D--XX公司.xlsx";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "数据安全风险评估-项目组织结构表D.xlsx");
    }

    /**
     * 导入团队成员Excel文件
     */
    @ApiOperation("导入团队成员Excel文件")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:team:import')")
    @Log(title = "评估团队导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importTeamMembers(@RequestParam("orgId") Long orgId, @RequestParam("teamName") String teamName, @RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = evaluateCompanyTeamService.importTeamMembers(teamName, orgId, file);

            // 检查导入结果
            Boolean success = (Boolean) result.get("success");

            if (success != null && success) {
                // 导入成功
                return AjaxResult.success("导入成功", result);
            } else {
                // 导入失败，但有详细错误信息
                return AjaxResult.error("导入失败", result);
            }
        } catch (Exception e) {
            logger.error("导入团队成员失败", e);
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }
}