# EvaluatePlanTaskController 增强版使用指南

## 概述

EvaluatePlanTaskController的add方法已经增强，现在可以在创建评估计划任务的同时，集成其他控制器和服务类的功能。

## 控制器关系图

```
EvaluatePlanTaskController (评估计划任务控制器)
├── IEvaluatePlanTaskService (评估计划任务服务)
├── EvaluatePlanDispatcher (评估计划分发器)
└── IStepInstanceNextConfigService (步骤实例下一步配置服务)
```

## 增强功能

### 1. 基础任务创建
- 使用 `IEvaluatePlanTaskService.createPlanTask()` 创建评估计划任务

### 2. 评估计划执行
- 使用 `EvaluatePlanDispatcher.dispatch()` 执行评估计划
- 使用 `EvaluatePlanDispatcher.executeTask()` 执行特定任务内容
- 使用 `EvaluatePlanDispatcher.getSupportedTypes()` 获取支持的评估类型

### 3. 流程步骤配置
- 使用 `IStepInstanceNextConfigService.setNextStepConfig()` 设置下一步配置

## API 调用示例

### 基础调用（仅创建任务）

```json
POST /evaluatePlan/task
Content-Type: application/json

{
    "orgId": 1,
    "evaluateOrgId": 2,
    "modelId": 100,
    "reportNo": "RPT-2025-001",
    "name": "数据安全风险评估任务",
    "planStartDate": "2025-07-29",
    "planEndDate": "2025-08-29",
    "deadline": "2025-08-30",
    "taskDescription": "对某公司进行全面的数据安全风险评估"
}
```

### 增强调用（创建任务 + 执行评估计划）

```json
POST /evaluatePlan/task
Content-Type: application/json

{
    "orgId": 1,
    "evaluateOrgId": 2,
    "modelId": 100,
    "reportNo": "RPT-2025-002",
    "name": "数据安全风险评估任务",
    "planStartDate": "2025-07-29",
    "planEndDate": "2025-08-29",
    "deadline": "2025-08-30",
    "taskDescription": "对某公司进行全面的数据安全风险评估，立即执行"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "评估计划任务创建成功",
    "data": {
        "id": 1001,
        "evaluateResult": {
            "planId": 1001,
            "success": true,
            "message": "评估计划执行成功",
            "progress": 10,
            "status": 1
        },
        "evaluateSuccess": true,
        "taskExecuteResult": {
            "planId": 1001,
            "success": true,
            "message": "任务内容执行成功"
        },
        "taskExecuteSuccess": true,
        "supportedEvaluateTypes": {
            "data_security_plan": "数据安全风险评估",
            "compliance_check": "合规性检查"
        }
    }
}
```

### 增强调用（创建任务 + 设置下一步配置）

```json
POST /evaluatePlan/task
Content-Type: application/json

{
    "orgId": 1,
    "evaluateOrgId": 2,
    "modelId": 100,
    "reportNo": "RPT-2025-003",
    "name": "数据安全风险评估任务",
    "planStartDate": "2025-07-29",
    "planEndDate": "2025-08-29",
    "deadline": "2025-08-30",
    "taskDescription": "对某公司进行全面的数据安全风险评估",
    "nextStepAssignee": "张三",
    "nextStepDept": "技术部",
    "nextStepDeadlineDays": 5,
    "setTaskDeadline": 1
}
```

## 业务流程说明

### 1. 任务创建流程
```
1. 接收EvaluatePlanTaskDto请求
2. 调用evaluatePlanTaskService.createPlanTask()创建任务
3. 根据条件判断是否需要执行额外操作：
   - shouldExecuteEvaluatePlan() -> 执行评估计划
   - shouldSetNextStepConfig() -> 设置下一步配置  
   - shouldExecuteTaskContent() -> 执行任务内容
4. 返回综合结果
```

### 2. 条件判断逻辑

#### shouldExecuteEvaluatePlan()
- 当modelId不为空且planStartDate不为空时执行

#### shouldSetNextStepConfig()
- 当nextStepAssignee、nextStepDept或nextStepDeadlineDays任一不为空时执行

#### shouldExecuteTaskContent()
- 当taskDescription包含"立即执行"关键字时执行

## 扩展建议

### 1. 添加更多集成功能
```java
// 可以添加更多服务集成，例如：
@Autowired
private INotificationService notificationService; // 通知服务

@Autowired
private IAuditService auditService; // 审计服务

@Autowired
private IReportService reportService; // 报告服务
```

### 2. 增加配置化支持
```java
// 通过配置文件控制是否启用各种集成功能
@Value("${evaluate.task.auto-execute:false}")
private boolean autoExecuteEnabled;

@Value("${evaluate.task.auto-config-next-step:false}")
private boolean autoConfigNextStepEnabled;
```

### 3. 添加异步处理
```java
@Async
public CompletableFuture<EvaluatePlanResponse> executeEvaluatePlanAsync(EvaluatePlanRequest request) {
    return CompletableFuture.completedFuture(evaluatePlanDispatcher.dispatch(request));
}
```

## 注意事项

1. **事务管理**：确保所有操作在同一事务中，避免数据不一致
2. **异常处理**：每个集成功能都有独立的异常处理，不会影响主流程
3. **性能考虑**：根据实际需求选择性启用集成功能
4. **权限控制**：确保调用其他服务时有相应的权限
5. **日志记录**：重要操作都有详细的日志记录

## 测试建议

1. **单元测试**：测试各个辅助方法的逻辑
2. **集成测试**：测试与其他服务的集成功能
3. **性能测试**：测试在高并发情况下的表现
4. **异常测试**：测试各种异常情况的处理
