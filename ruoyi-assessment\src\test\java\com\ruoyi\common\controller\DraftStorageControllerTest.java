package com.ruoyi.common.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.service.IDraftStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 通用暂存控制器单元测试
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@WebMvcTest(DraftStorageController.class)
class DraftStorageControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IDraftStorageService draftStorageService;

    @Autowired
    private ObjectMapper objectMapper;

    private Map<String, Object> testData;
    private static final String BUSINESS_TYPE = DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK;
    private static final String DRAFT_KEY = "test_draft_001";
    private static final Long USER_ID = 1L;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testData = new HashMap<>();
        testData.put("name", "测试数据");
        testData.put("value", 123);
        testData.put("description", "这是一个测试数据");
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:save"})
    void testSaveDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.saveDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                any(Object.class),
                eq(24L),
                eq(TimeUnit.HOURS)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/common/draft/save")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY)
                        .param("expireHours", "24")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("暂存成功"));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).saveDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                any(Object.class),
                eq(24L),
                eq(TimeUnit.HOURS)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:query"})
    void testGetDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.getDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(Object.class)
        )).thenReturn(testData);

        // 执行测试
        mockMvc.perform(get("/common/draft/get")
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY)
                        .param("dataType", "Object"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("获取成功"))
                .andExpect(jsonPath("$.data.name").value("测试数据"))
                .andExpect(jsonPath("$.data.value").value(123));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).getDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(Object.class)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:remove"})
    void testDeleteDraft_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.deleteDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/common/draft/delete")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).deleteDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:list"})
    void testGetDraftList_Success() throws Exception {
        // 准备测试数据
        Map<String, Map<String, Object>> mockSummary = new HashMap<>();
        Map<String, Object> draftInfo = new HashMap<>();
        draftInfo.put("businessType", BUSINESS_TYPE);
        draftInfo.put("userId", USER_ID);
        draftInfo.put("draftKey", DRAFT_KEY);
        draftInfo.put("createTime", "2025-07-30 10:00:00");
        mockSummary.put(DRAFT_KEY, draftInfo);

        // 设置Mock行为
        when(draftStorageService.getUserDraftSummary(
                eq(BUSINESS_TYPE),
                eq(USER_ID)
        )).thenReturn(mockSummary);

        // 执行测试
        mockMvc.perform(get("/common/draft/list")
                        .param("businessType", BUSINESS_TYPE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("获取成功"))
                .andExpect(jsonPath("$.data").isMap())
                .andExpect(jsonPath("$.data['" + DRAFT_KEY + "'].userId").value(USER_ID));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:clear"})
    void testClearDrafts_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.clearUserDrafts(
                eq(BUSINESS_TYPE),
                eq(USER_ID)
        )).thenReturn(5);

        // 执行测试
        mockMvc.perform(delete("/common/draft/clear")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("清理完成"))
                .andExpect(jsonPath("$.data.clearedCount").value(5));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:query"})
    void testExistsDraft_True() throws Exception {
        // 设置Mock行为
        when(draftStorageService.existsDraft(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(true);
        
        when(draftStorageService.getDraftTtl(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY)
        )).thenReturn(7200L);

        // 执行测试
        mockMvc.perform(get("/common/draft/exists")
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("检查完成"))
                .andExpect(jsonPath("$.data.exists").value(true))
                .andExpect(jsonPath("$.data.ttl").value(7200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:save"})
    void testBatchSaveDrafts_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> batchData = new HashMap<>();
        batchData.put("draft1", testData);
        batchData.put("draft2", testData);

        // 设置Mock行为
        when(draftStorageService.batchSaveDrafts(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(batchData),
                eq(24L),
                eq(TimeUnit.HOURS)
        )).thenReturn(2);

        // 执行测试
        mockMvc.perform(post("/common/draft/batch/save")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE)
                        .param("expireHours", "24")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(batchData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("批量暂存完成"))
                .andExpect(jsonPath("$.data.savedCount").value(2))
                .andExpect(jsonPath("$.data.totalCount").value(2));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:edit"})
    void testUpdateDraftTtl_Success() throws Exception {
        // 设置Mock行为
        when(draftStorageService.updateDraftTtl(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(48L),
                eq(TimeUnit.HOURS)
        )).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/common/draft/updateTtl")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY)
                        .param("expireHours", "48"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("更新成功"));

        // 验证服务方法被调用
        verify(draftStorageService, times(1)).updateDraftTtl(
                eq(BUSINESS_TYPE),
                eq(USER_ID),
                eq(DRAFT_KEY),
                eq(48L),
                eq(TimeUnit.HOURS)
        );
    }

    @Test
    void testSaveDraft_NoPermission() throws Exception {
        // 执行测试（没有权限）
        mockMvc.perform(post("/common/draft/save")
                        .with(csrf())
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY)
                        .param("expireHours", "24")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testData)))
                .andExpect(status().isForbidden());

        // 验证服务方法没有被调用
        verify(draftStorageService, never()).saveDraft(
                any(String.class),
                any(Long.class),
                any(String.class),
                any(Object.class),
                any(Long.class),
                any(TimeUnit.class)
        );
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"common:draft:query"})
    void testGetDraft_NotExists() throws Exception {
        // 设置Mock行为
        when(draftStorageService.getDraft(
                any(String.class),
                any(Long.class),
                any(String.class),
                any(Class.class)
        )).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/common/draft/get")
                        .param("businessType", BUSINESS_TYPE)
                        .param("draftKey", DRAFT_KEY)
                        .param("dataType", "Object"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("暂存数据不存在或已过期"));
    }
}
