package com.ruoyi.dataItem.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelFieldValidate;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据项管理对象 dsa_data_item
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_item")
public class DataItem extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属单位ID
     */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class, ListGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 数据名称
     */
    @ExcelFieldValidate(name = "数据项名称", required = true)
    @Excel(name = "数据名称")
    @TableField(value = "data_name")
    private String dataName;

    /**
     * 分类分级规范
     */
    @ExcelFieldValidate(name = "依据数据分类分级规范", required = true)
    @Excel(name = "分类分级规范")
    @TableField(value = "data_standard")
    private String dataStandard;

    /**
     * 数据一级类别
     */
    @ExcelFieldValidate(name = "数据一级类别", required = true)
    @Excel(name = "数据一级类别")
    @TableField(value = "category_level1")
    private String categoryLevel1;

    /**
     * 数据二级类别
     */
    @ExcelFieldValidate(name = "数据二级类别", required = true)
    @Excel(name = "数据二级类别")
    @TableField(value = "category_level2")
    private String categoryLevel2;

    /**
     * 数据三级类别
     */
    @Excel(name = "数据三级类别")
    @TableField(value = "category_level3")
    private String categoryLevel3;

    /**
     * 数据四级类别
     */
    @Excel(name = "数据四级类别")
    @TableField(value = "category_level4")
    private String categoryLevel4;

    /**
     * 数据级别
     */
    @ExcelFieldValidate(name = "数据级别", required = true, dictType = "business_data_level")
    @Excel(name = "数据级别")
    @TableField(value = "data_level")
    private String dataLevel;

    /**
     * 数据载体
     */
    @ExcelFieldValidate(name = "数据载体", required = true)
    @Excel(name = "数据载体")
    @TableField(value = "data_carrier")
    private String dataCarrier;

    /**
     * 数据来源
     */
    @ExcelFieldValidate(name = "数据来源", required = true)
    @Excel(name = "数据来源")
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 数据数量（单位：GB）
     */
    @ExcelFieldValidate(name = "数据数量（单位：GB）", required = true)
    @Excel(name = "数据数量", readConverterExp = "单=位：GB")
    @TableField(value = "data_amount_gb")
    private BigDecimal dataAmountGb;

    /**
     * 数据数量（单位：条）
     */
    @ExcelFieldValidate(name = "数据数量（单位：条）", required = true)
    @Excel(name = "数据数量", readConverterExp = "单=位：条")
    @TableField(value = "data_amount_count")
    private String dataAmountCount;

    /**
     * 覆盖类型
     */
    @ExcelFieldValidate(name = "覆盖类型", required = true)
    @Excel(name = "覆盖类型")
    @TableField(value = "coverage_type")
    private String coverageType;

    /**
     * 覆盖占比
     */
    @ExcelFieldValidate(name = "覆盖占比", required = true)
    @Excel(name = "覆盖占比")
    @TableField(value = "coverage_ratio")
    private String coverageRatio;

    /**
     * 数据精度
     */
    @Excel(name = "数据精度")
    @TableField(value = "data_accuracy")
    private String dataAccuracy;

    /**
     * 详细描述
     */
    @ExcelFieldValidate(name = "详细描述", required = true)
    @Excel(name = "详细描述")
    @TableField(value = "description")
    private String description;

    /**
     * 数据处理者名称
     */
    @ExcelFieldValidate(name = "数据处理者名称", required = true)
    @Excel(name = "数据处理者名称")
    @TableField(value = "processor_name")
    private String processorName;

    /**
     * 机构代码
     */
    @ExcelFieldValidate(name = "机构代码", required = true)
    @Excel(name = "机构代码")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 省
     */
    @ExcelFieldValidate(name = "省", required = true)
    @Excel(name = "省")
    @TableField(value = "province")
    private String province;

    /**
     * 市
     */
    @ExcelFieldValidate(name = "市", required = true)
    @Excel(name = "市")
    @TableField(value = "city")
    private String city;

    /**
     * 数据处理者性质
     */
    @ExcelFieldValidate(name = "数据处理者性质", required = true)
    @Excel(name = "数据处理者性质")
    @TableField(value = "processor_nature")
    private String processorNature;

    /**
     * 所属行业
     */
    @ExcelFieldValidate(name = "所属行业", required = true)
    @Excel(name = "所属行业")
    @TableField(value = "industry")
    private String industry;

    /**
     * 主营业务范围
     */
    @ExcelFieldValidate(name = "主营业务范围", required = true)
    @Excel(name = "主营业务范围")
    @TableField(value = "main_business")
    private String mainBusiness;

    /**
     * 姓名
     */
    @ExcelFieldValidate(name = "姓名", required = true)
    @Excel(name = "姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 职务
     */
    @ExcelFieldValidate(name = "职务", required = true)
    @Excel(name = "职务")
    @TableField(value = "contact_position")
    private String contactPosition;

    /**
     * 联系方式
     */
    @ExcelFieldValidate(name = "联系方式", required = true)
    @Excel(name = "联系方式")
    @TableField(value = "contact_info")
    private String contactInfo;

    /**
     * 数据处理活动方式
     */
    @ExcelFieldValidate(name = "数据处理活动方式", required = true)
    @Excel(name = "数据处理活动方式")
    @TableField(value = "processing_method")
    private String processingMethod;

    /**
     * 数据处理目的
     */
    @ExcelFieldValidate(name = "数据处理目的", required = true)
    @Excel(name = "数据处理目的")
    @TableField(value = "processing_purpose")
    private String processingPurpose;

    /**
     * 是否涉及算法自动化处理，1-是 0-否
     */
    @ExcelFieldValidate(name = "是否涉及算法自动化处理", required = true)
    @Excel(name = "是否涉及算法自动化处理")
    @TableField(value = "is_algorithm_auto")
    private String isAlgorithmAuto;

    /**
     * 是否出境
     */
    @ExcelFieldValidate(name = "是否出境", required = true)
    @Excel(name = "是否出境")
    @TableField(value = "is_cross_border")
    private String isCrossBorder;

    /**
     * 数据出境接收方名称
     */
    @ExcelFieldValidate(name = "数据出境接收方名称", required = true)
    @Excel(name = "数据出境接收方名称")
    @TableField(value = "cross_border_receiver")
    private String crossBorderReceiver;

    /**
     * 数据出境方式
     */
    @Excel(name = "数据出境方式")
    @TableField(value = "cross_border_method")
    private String crossBorderMethod;

    /**
     * 是否开展数据出境安全评估，1-是 0-否
     */
    @ExcelFieldValidate(name = "是否开展数据出境安全评估", required = true)
    @Excel(name = "是否开展数据出境安全评估")
    @TableField(value = "is_cross_border_assess")
    private String isCrossBorderAssess;

    /**
     * 数据出境安全评估结果
     */
    @ExcelFieldValidate(name = "数据出境安全评估结果", required = true)
    @Excel(name = "数据出境安全评估结果")
    @TableField(value = "cross_border_assess_result")
    private String crossBorderAssessResult;

    /**
     * 是否对外共享
     */
    @ExcelFieldValidate(name = "是否对外共享", required = true)
    @Excel(name = "是否对外共享")
    @TableField(value = "is_external_share")
    private String isExternalShare;

    /**
     * 数据对外共享接收方名称
     */
    @ExcelFieldValidate(name = "数据对外共享接收方名称", required = true)
    @Excel(name = "数据对外共享接收方名称")
    @TableField(value = "external_share_receiver")
    private String externalShareReceiver;

    /**
     * 数据对外共享方式
     */
    @ExcelFieldValidate(name = "数据对外共享方式", required = true)
    @Excel(name = "数据对外共享方式")
    @TableField(value = "external_share_method")
    private String externalShareMethod;

    /**
     * 是否涉及跨主体流动
     */
    @ExcelFieldValidate(name = "是否涉及跨主体流动", required = true)
    @Excel(name = "是否涉及跨主体流动")
    @TableField(value = "is_cross_subject_flow")
    private String isCrossSubjectFlow;

    /**
     * 是否为涉外数据
     */
    @ExcelFieldValidate(name = "是否为涉外数据", required = true)
    @Excel(name = "是否为涉外数据")
    @TableField(value = "is_foreign_data")
    private String isForeignData;

    /**
     * 信息系统名称
     */
    @ExcelFieldValidate(name = "信息系统名称", required = true)
    @Excel(name = "信息系统名称")
    @TableField(value = "system_name")
    private String systemName;

    /**
     * 信息系统ID
     */
    @Excel(name = "信息系统ID")
    @TableField(value = "data_system_id")
    private Long dataSystemId;

    /**
     * 信息系统类型
     */
    @Excel(name = "信息系统类型")
    @TableField(value = "system_type")
    private String systemType;

    /**
     * 信息系统IP地址
     */
    @Excel(name = "信息系统IP地址")
    @TableField(value = "system_ip")
    private String systemIp;

    /**
     * 信息系统域名
     */
    @Excel(name = "信息系统域名")
    @TableField(value = "system_domain")
    private String systemDomain;

    /**
     * 网络安全等级保护情况
     */
    @ExcelFieldValidate(name = "网络安全等级保护情况", required = true)
    @Excel(name = "网络安全等级保护情况")
    @TableField(value = "cyber_protect_level")
    private String cyberProtectLevel;

    /**
     * 通信网络安全防护定级备案情况
     */
    @ExcelFieldValidate(name = "通信网络安全防护定级备案情况", required = true)
    @Excel(name = "通信网络安全防护定级备案情况")
    @TableField(value = "network_protect_record")
    private String networkProtectRecord;

    /**
     * 是否为关键信息基础设施，1-是 0-否
     */
    @ExcelFieldValidate(name = "是否为关键信息基础设施", required = true)
    @Excel(name = "是否为关键信息基础设施")
    @TableField(value = "is_critical_infra")
    private String isCriticalInfra;

    /**
     * 是否进行数据安全风险评估，1-是 0-否
     */
    @ExcelFieldValidate(name = "是否进行数据安全风险评估", required = true)
    @Excel(name = "是否进行数据安全风险评估")
    @TableField(value = "is_data_risk_assess")
    private String isDataRiskAssess;

    /**
     * 评估机构
     */
    @Excel(name = "评估机构")
    @TableField(value = "assess_org")
    private String assessOrg;

    /**
     * 评估规范
     */
    @Excel(name = "评估规范")
    @TableField(value = "assess_standard")
    private String assessStandard;

    /**
     * 评估时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评估时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "assess_time")
    private Date assessTime;

    /**
     * 评估结论
     */
    @Excel(name = "评估结论")
    @TableField(value = "assess_conclusion")
    private String assessConclusion;

    /**
     * 责任部门
     */
    @Excel(name = "责任部门")
    @TableField(value = "duty_dept")
    private String dutyDept;

    /**
     * 责任人
     */
    @Excel(name = "责任人")
    @TableField(value = "duty_user")
    private String dutyUser;

    /**
     * 数据状态，1-增加 2-修改 3-删除
     */
    @Excel(name = "数据状态，1-增加 2-修改 3-删除")
    @TableField(value = "data_status")
    private Integer dataStatus;

    /**
     * 状态，1-正常 0-禁用
     */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;

    /**
     * 删除人
     */
    @Excel(name = "删除人")
    @TableField(value = "delete_by")
    private String deleteBy;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "delete_time")
    private Date deleteTime;
}