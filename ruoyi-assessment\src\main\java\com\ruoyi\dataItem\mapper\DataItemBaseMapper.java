package com.ruoyi.dataItem.mapper;

import com.ruoyi.dataItem.domain.DataItemBase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 数据项基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface DataItemBaseMapper extends BaseMapper<DataItemBase> {

    /**
     * 物理删除指定单位下所有数据项基本信息
     */
    void physicalDeleteByOrgId(Long orgId);

    /**
     * 根据数据项id批量物理删除
     */
    void deleteByDataItemIds(List<Long> dataItemIds);
}
