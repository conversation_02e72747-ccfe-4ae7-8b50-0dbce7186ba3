package com.ruoyi.process.service;

import com.ruoyi.process.domain.ProcessStepDefinition;

import java.util.List;
import java.util.Map;

/**
 * 任务配置服务接口
 * 负责从流程定义中获取任务配置信息
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ITaskConfigService {

    /**
     * 根据流程编码和步骤编码获取任务配置
     * 
     * @param processCode 流程编码
     * @param stepCode 步骤编码
     * @return 步骤定义信息
     */
    ProcessStepDefinition getTaskConfig(String processCode, String stepCode);

    /**
     * 根据流程编码获取所有任务配置
     * 
     * @param processCode 流程编码
     * @return 步骤定义列表
     */
    List<ProcessStepDefinition> getTaskConfigsByProcess(String processCode);

    /**
     * 根据步骤编码获取任务配置
     *
     * @param stepCode 步骤编码
     * @return 步骤定义列表
     */
    List<ProcessStepDefinition> getTaskConfigsByStepCode(String stepCode);

    /**
     * 获取任务的默认参数
     *
     * @param stepCode 步骤编码
     * @return 默认参数Map
     */
    Map<String, Object> getDefaultTaskParams(String stepCode);

    /**
     * 验证任务配置参数
     *
     * @param stepCode 步骤编码
     * @param taskParams 任务参数
     * @return 验证结果
     */
    boolean validateTaskParams(String stepCode, Map<String, Object> taskParams);

    /**
     * 获取任务支持的参数列表
     *
     * @param stepCode 步骤编码
     * @return 支持的参数列表
     */
    List<String> getSupportedParams(String stepCode);

    /**
     * 刷新任务配置缓存
     */
    void refreshTaskConfigCache();

    /**
     * 获取所有可用的步骤编码
     *
     * @return 步骤编码列表
     */
    List<String> getAvailableStepCodes();
}
