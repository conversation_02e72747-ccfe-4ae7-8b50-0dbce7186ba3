package com.ruoyi.evaluate.evaluatePlan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;

/**
 * 暂存步骤请求参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "暂存步骤请求参数")
public class DraftStepRequest {

    @ApiModelProperty(value = "评估计划ID", required = true, example = "123")
    @NotNull(message = "评估计划ID不能为空")
    @Positive(message = "评估计划ID必须为正数")
    private Long planId;

    @ApiModelProperty(value = "步骤名称", required = true, example = "step1")
    @NotBlank(message = "步骤名称不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,50}$", message = "步骤名称只能包含字母、数字、下划线和横线，长度1-50位")
    private String stepCode;

    /**
     * 生成暂存键
     *
     * @return 暂存键
     */
    public String generateDraftKey() {
        return planId + "_" + stepCode;
    }

    /**
     * 校验参数有效性
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return planId != null && planId > 0 && 
               stepCode != null && !stepCode.trim().isEmpty() &&
               stepCode.matches("^[a-zA-Z0-9_-]{1,50}$");
    }

    /**
     * 获取参数描述
     *
     * @return 参数描述
     */
    public String getDescription() {
        return String.format("评估计划[%d]的步骤[%s]", planId, stepCode);
    }
}
