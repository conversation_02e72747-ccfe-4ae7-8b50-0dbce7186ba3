package com.ruoyi.dataItem.service.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataSystemMapper;
import com.ruoyi.dataItem.domain.DataSystem;
import com.ruoyi.dataItem.service.IDataSystemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 数据承载系统Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class DataSystemServiceImpl extends ServiceImpl<DataSystemMapper, DataSystem> implements IDataSystemService {

    @Override
    public Long getDataSystemIdByName(String systemName, Long orgId) {
        DataSystem ds = this.lambdaQuery()
            .eq(DataSystem::getSystemName, systemName)
            .eq(DataSystem::getOrgId, orgId)
            .one();
        return ds != null ? ds.getId() : null;
    }

    @Override
    public Long insertDataSystem(String systemName, Long orgId) {
        DataSystem ds = new DataSystem();
        ds.setSystemName(systemName);
        ds.setOrgId(orgId);
        this.save(ds);
        return ds.getId();
    }
}
