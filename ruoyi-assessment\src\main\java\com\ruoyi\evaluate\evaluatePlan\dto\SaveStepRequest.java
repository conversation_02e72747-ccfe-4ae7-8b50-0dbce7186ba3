package com.ruoyi.evaluate.evaluatePlan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 暂存保存请求参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "暂存保存请求参数")
public class SaveStepRequest extends DraftStepRequest {

    @ApiModelProperty(value = "步骤数据内容", required = true)
    @NotNull(message = "步骤数据不能为空")
    private Object stepData;

    @ApiModelProperty(value = "数据描述", example = "基本信息填写")
    private String dataDescription;

    /**
     * 校验保存参数有效性
     *
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && stepData != null;
    }

    /**
     * 获取保存参数描述
     *
     * @return 参数描述
     */
    @Override
    public String getDescription() {
        String baseDesc = super.getDescription();
        if (dataDescription != null && !dataDescription.trim().isEmpty()) {
            return baseDesc + " - " + dataDescription;
        }
        return baseDesc;
    }
}
