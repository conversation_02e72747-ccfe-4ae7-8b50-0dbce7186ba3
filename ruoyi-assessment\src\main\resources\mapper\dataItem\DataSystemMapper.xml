<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataSystemMapper">
    
    <resultMap type="DataSystem" id="DataSystemResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="systemName"    column="system_name"    />
        <result property="mainFunction"    column="main_function"    />
        <result property="networkArchDesc"    column="network_arch_desc"    />
        <result property="networkArchImg"    column="network_arch_img"    />
        <result property="techArchDesc"    column="tech_arch_desc"    />
        <result property="techArchImg"    column="tech_arch_img"    />
        <result property="testUrl"    column="test_url"    />
        <result property="testAccount"    column="test_account"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataSystemVo">
        select id, org_id, system_name, main_function, network_arch_desc, network_arch_img, tech_arch_desc, tech_arch_img, test_url, test_account, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_data_system
    </sql>

    <select id="selectDataSystemList" parameterType="DataSystem" resultMap="DataSystemResult">
        <include refid="selectDataSystemVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="mainFunction != null  and mainFunction != ''"> and main_function = #{mainFunction}</if>
            <if test="networkArchDesc != null  and networkArchDesc != ''"> and network_arch_desc = #{networkArchDesc}</if>
            <if test="networkArchImg != null  and networkArchImg != ''"> and network_arch_img = #{networkArchImg}</if>
            <if test="techArchDesc != null  and techArchDesc != ''"> and tech_arch_desc = #{techArchDesc}</if>
            <if test="techArchImg != null  and techArchImg != ''"> and tech_arch_img = #{techArchImg}</if>
            <if test="testUrl != null  and testUrl != ''"> and test_url = #{testUrl}</if>
            <if test="testAccount != null  and testAccount != ''"> and test_account = #{testAccount}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDataSystemById" parameterType="Long" resultMap="DataSystemResult">
        <include refid="selectDataSystemVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataSystem" parameterType="DataSystem" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="systemName != null and systemName != ''">system_name,</if>
            <if test="mainFunction != null">main_function,</if>
            <if test="networkArchDesc != null">network_arch_desc,</if>
            <if test="networkArchImg != null">network_arch_img,</if>
            <if test="techArchDesc != null">tech_arch_desc,</if>
            <if test="techArchImg != null">tech_arch_img,</if>
            <if test="testUrl != null">test_url,</if>
            <if test="testAccount != null">test_account,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="systemName != null and systemName != ''">#{systemName},</if>
            <if test="mainFunction != null">#{mainFunction},</if>
            <if test="networkArchDesc != null">#{networkArchDesc},</if>
            <if test="networkArchImg != null">#{networkArchImg},</if>
            <if test="techArchDesc != null">#{techArchDesc},</if>
            <if test="techArchImg != null">#{techArchImg},</if>
            <if test="testUrl != null">#{testUrl},</if>
            <if test="testAccount != null">#{testAccount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataSystem" parameterType="DataSystem">
        update dsa_data_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="systemName != null and systemName != ''">system_name = #{systemName},</if>
            <if test="mainFunction != null">main_function = #{mainFunction},</if>
            <if test="networkArchDesc != null">network_arch_desc = #{networkArchDesc},</if>
            <if test="networkArchImg != null">network_arch_img = #{networkArchImg},</if>
            <if test="techArchDesc != null">tech_arch_desc = #{techArchDesc},</if>
            <if test="techArchImg != null">tech_arch_img = #{techArchImg},</if>
            <if test="testUrl != null">test_url = #{testUrl},</if>
            <if test="testAccount != null">test_account = #{testAccount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSystemById" parameterType="Long">
        delete from dsa_data_system where id = #{id}
    </delete>

    <delete id="deleteDataSystemByIds" parameterType="String">
        delete from dsa_data_system where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>