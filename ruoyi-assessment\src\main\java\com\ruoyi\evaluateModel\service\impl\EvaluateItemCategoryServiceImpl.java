package com.ruoyi.evaluateModel.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.evaluateModel.mapper.EvaluateItemCategoryMapper;
import com.ruoyi.evaluateModel.domain.EvaluateItemCategory;
import com.ruoyi.evaluateModel.service.IEvaluateItemCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 评估项分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Service
public class EvaluateItemCategoryServiceImpl extends ServiceImpl<EvaluateItemCategoryMapper, EvaluateItemCategory> implements IEvaluateItemCategoryService {

    /**
     * 批量更新排序
     *
     * @param categories 需要更新排序的分类列表
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(List<EvaluateItemCategory> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            log.warn("批量更新排序：分类列表为空");
            return false;
        }

        try {
            // 批量更新排序值
            for (EvaluateItemCategory category : categories) {
                if (category.getId() != null && category.getSort() != null) {
                    EvaluateItemCategory updateEntity = new EvaluateItemCategory();
                    updateEntity.setId(category.getId());
                    updateEntity.setSort(category.getSort());
                    this.updateById(updateEntity);
                }
            }

            log.info("批量更新排序成功，共更新{}条记录", categories.size());
            return true;
        } catch (Exception e) {
            log.error("批量更新排序失败", e);
            throw new RuntimeException("批量更新排序失败：" + e.getMessage());
        }
    }
}
