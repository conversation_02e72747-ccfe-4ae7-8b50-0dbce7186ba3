package com.ruoyi.evaluate.evaluatePlan.service.processor;

import com.ruoyi.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理器注册表
 * <p>
 * 提供处理器的注册、查找、统计等功能，支持按评估类型和步骤编码进行分类管理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class ProcessorRegistry {

    @Autowired
    private List<IStepDataProcessor> allProcessors;

    @Autowired
    private ProcessorPackageManager packageManager;

    /**
     * 按评估类型分组的处理器映射
     * Key: evaluateType, Value: Map<stepCode, IStepDataProcessor>
     */
    private final Map<String, Map<String, IStepDataProcessor>> processorsByType = new HashMap<>();

    /**
     * 通用处理器映射（evaluateType为null的处理器）
     * Key: stepCode, Value: IStepDataProcessor
     */
    private final Map<String, IStepDataProcessor> commonProcessors = new HashMap<>();

    /**
     * 默认处理器（兜底处理器）
     */
    private IStepDataProcessor defaultProcessor;

    /**
     * 注册表是否已初始化
     */
    private volatile boolean initialized = false;

    /**
     * 最后一次注册时间，用于检测是否需要重新注册
     */
    private volatile long lastRegistrationTime = 0;

    /**
     * 懒加载初始化注册表
     */
    private void ensureInitialized() {
        if (!initialized || shouldReregister()) {
            synchronized (this) {
                if (!initialized || shouldReregister()) {
                    log.info("开始{}处理器注册表...", initialized ? "重新初始化" : "初始化");

                    // 清空现有注册
                    clearRegistry();

                    // 重新注册
                    registerAllProcessors();
                    validateProcessorStructure();
                    logRegistryStatistics();

                    initialized = true;
                    lastRegistrationTime = System.currentTimeMillis();

                    log.info("处理器注册表{}完成", initialized ? "重新初始化" : "初始化");
                }
            }
        }
    }

    /**
     * 判断是否需要重新注册（用于热部署支持）
     */
    private boolean shouldReregister() {
        // 检查处理器列表是否发生变化
        return allProcessors.size() != getTotalRegisteredProcessors();
    }

    /**
     * 清空注册表
     */
    private void clearRegistry() {
        processorsByType.clear();
        commonProcessors.clear();
        defaultProcessor = null;
    }

    /**
     * 获取已注册的处理器总数
     */
    private int getTotalRegisteredProcessors() {
        int total = commonProcessors.size();
        total += processorsByType.values().stream()
                .mapToInt(Map::size)
                .sum();
        if (defaultProcessor != null) {
            total += 1;
        }
        return total;
    }

    /**
     * 注册所有处理器
     */
    private void registerAllProcessors() {
        for (IStepDataProcessor processor : allProcessors) {
            registerProcessor(processor);
        }
    }

    /**
     * 注册单个处理器
     */
    private void registerProcessor(IStepDataProcessor processor) {
        String evaluateType = processor.getEvaluateType();
        String stepCode = processor.getStepCode();
        String className = processor.getClass().getSimpleName();

        try {
            if (evaluateType == null && stepCode == null) {
                // 默认处理器
                defaultProcessor = processor;
                log.debug("注册默认处理器: {}", className);
            } else if (evaluateType == null) {
                // 通用处理器
                commonProcessors.put(stepCode, processor);
                log.debug("注册通用处理器: {} -> {}", stepCode, className);
            } else {
                // 特定类型处理器
                processorsByType
                        .computeIfAbsent(evaluateType, k -> new HashMap<>())
                        .put(stepCode, processor);
                log.debug("注册特定处理器: {}:{} -> {}", evaluateType, stepCode, className);
            }
        } catch (Exception e) {
            log.error("注册处理器失败: {}", className, e);
        }
    }

    /**
     * 根据评估类型和步骤编码查找处理器
     */
    public IStepDataProcessor findProcessor(String evaluateType, String stepCode) {
        // 确保注册表已初始化（懒加载）
        ensureInitialized();

        // 1. 精确匹配：特定评估类型的特定步骤
        IStepDataProcessor processor = findSpecificProcessor(evaluateType, stepCode);
        if (processor != null) {
            log.debug("找到特定处理器: {}:{} -> {}", evaluateType, stepCode, processor.getClass().getSimpleName());
            return processor;
        }

        throw new ServiceException("未找到支持的步骤数据处理器，评估类型: " + evaluateType + ", 步骤编码: " + stepCode);

        // 2. 通用匹配：通用步骤处理器
        /*processor = findCommonProcessor(stepCode);
        if (processor != null) {
            log.debug("找到通用处理器: {} -> {}", stepCode, processor.getClass().getSimpleName());
            return processor;
        }

        // 3. 默认处理器
        if (defaultProcessor != null) {
            log.debug("使用默认处理器: {}", defaultProcessor.getClass().getSimpleName());
            return defaultProcessor;
        }

        log.warn("未找到任何匹配的处理器: {}:{}", evaluateType, stepCode);*/
        // return null;
    }

    /**
     * 查找特定处理器
     */
    private IStepDataProcessor findSpecificProcessor(String evaluateType, String stepCode) {
        Map<String, IStepDataProcessor> typeProcessors = processorsByType.get(evaluateType);
        return typeProcessors != null ? typeProcessors.get(stepCode) : null;
    }

    /**
     * 查找通用处理器
     */
    private IStepDataProcessor findCommonProcessor(String stepCode) {
        return commonProcessors.get(stepCode);
    }

    /**
     * 获取指定评估类型的所有处理器
     */
    public Map<String, IStepDataProcessor> getProcessorsByType(String evaluateType) {
        ensureInitialized();
        return processorsByType.getOrDefault(evaluateType, Collections.emptyMap());
    }

    /**
     * 获取所有通用处理器
     */
    public Map<String, IStepDataProcessor> getCommonProcessors() {
        ensureInitialized();
        return new HashMap<>(commonProcessors);
    }

    /**
     * 获取支持的评估类型列表
     */
    public Set<String> getSupportedEvaluateTypes() {
        ensureInitialized();
        return new HashSet<>(processorsByType.keySet());
    }

    /**
     * 获取指定评估类型支持的步骤列表
     */
    public Set<String> getSupportedStepCodes(String evaluateType) {
        ensureInitialized();
        Map<String, IStepDataProcessor> typeProcessors = processorsByType.get(evaluateType);
        if (typeProcessors != null) {
            return new HashSet<>(typeProcessors.keySet());
        }
        return Collections.emptySet();
    }

    /**
     * 验证处理器结构
     */
    private void validateProcessorStructure() {
        List<String> issues = packageManager.validatePackageStructure(allProcessors);
        if (!issues.isEmpty()) {
            log.warn("发现处理器结构问题:");
            issues.forEach(issue -> log.warn("  - {}", issue));
        }
    }

    /**
     * 记录注册表统计信息
     */
    private void logRegistryStatistics() {
        log.info("=== 处理器注册表统计 ===");
        log.info("特定处理器: {} 种评估类型", processorsByType.size());

        processorsByType.forEach((type, processors) ->
                log.info("  {}: {} 个步骤处理器", type, processors.size())
        );

        log.info("通用处理器: {} 个", commonProcessors.size());
        log.info("默认处理器: {}", defaultProcessor != null ? "已配置" : "未配置");
        log.info("总处理器数: {}", allProcessors.size());

        // 生成详细报告
        String report = packageManager.generatePackageStructureReport(allProcessors);
        log.debug("处理器包结构报告:\n{}", report);
    }

    /**
     * 获取注册表状态信息
     */
    public Map<String, Object> getRegistryStatus() {
        ensureInitialized();

        Map<String, Object> status = new HashMap<>();

        status.put("totalProcessors", allProcessors.size());
        status.put("specificProcessors", processorsByType.size());
        status.put("commonProcessors", commonProcessors.size());
        status.put("hasDefaultProcessor", defaultProcessor != null);
        status.put("supportedEvaluateTypes", getSupportedEvaluateTypes());
        status.put("initialized", initialized);
        status.put("lastRegistrationTime", lastRegistrationTime);

        // 详细的处理器映射
        Map<String, Object> processorMapping = new HashMap<>();
        processorsByType.forEach((type, processors) -> {
            Map<String, String> stepMapping = processors.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().getClass().getSimpleName()
                    ));
            processorMapping.put(type, stepMapping);
        });
        status.put("processorMapping", processorMapping);

        return status;
    }

    /**
     * 手动刷新注册表（用于开发时热部署）
     */
    public void refreshRegistry() {
        log.info("手动刷新处理器注册表...");
        initialized = false;
        ensureInitialized();
        log.info("处理器注册表刷新完成");
    }

    /**
     * 强制重新注册所有处理器
     */
    public void forceReregister() {
        log.info("强制重新注册所有处理器...");
        synchronized (this) {
            clearRegistry();
            registerAllProcessors();
            validateProcessorStructure();
            logRegistryStatistics();
            initialized = true;
            lastRegistrationTime = System.currentTimeMillis();
        }
        log.info("强制重新注册完成");
    }

    /**
     * 获取所有处理器（用于监控）
     */
    public List<IStepDataProcessor> getAllProcessors() {
        return new ArrayList<>(allProcessors);
    }
}
