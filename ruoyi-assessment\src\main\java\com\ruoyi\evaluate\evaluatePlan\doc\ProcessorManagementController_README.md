# 处理器管理控制器使用指南

## 概述

`ProcessorManagementController` 是一个专门用于管理步骤数据处理器的控制器，提供了处理器的手动注册、刷新、状态查询等管理功能。主要用于开发环境的处理器热重载和运维管理。

## 核心功能

### 1. 处理器状态管理
- 获取处理器注册表状态
- 生成处理器状态报告
- 查看所有已注册的处理器列表

### 2. 处理器注册管理
- 手动刷新处理器注册表
- 强制重新注册所有处理器
- 手动注册单个处理器
- 批量注册多个处理器

### 3. 处理器查询功能
- 查找指定的处理器
- 获取支持的评估类型列表
- 获取指定评估类型支持的步骤列表

### 4. 处理器测试功能
- 测试处理器功能
- 验证处理器匹配逻辑

## API接口说明

### 1. 获取处理器状态

```http
GET /evaluate/processor/status
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取处理器状态成功",
  "data": {
    "totalProcessors": 15,
    "specificProcessors": 2,
    "commonProcessors": 3,
    "hasDefaultProcessor": true,
    "initialized": true,
    "lastRegistrationTime": 1690876543210,
    "hotReloadEnabled": true,
    "hotReloadHelper": "已启用"
  }
}
```

### 2. 获取处理器状态报告

```http
GET /evaluate/processor/report
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "生成状态报告成功",
  "data": "=== 处理器热重载状态报告 ===\n总处理器数: 15\n特定处理器: 2\n通用处理器: 3\n默认处理器: 已配置\n注册表状态: 已初始化\n最后注册时间: 1690876543210\n"
}
```

### 3. 手动刷新处理器注册表

```http
POST /evaluate/processor/refresh
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "处理器注册表刷新成功",
  "data": {
    "totalProcessors": 15,
    "specificProcessors": 2,
    "commonProcessors": 3,
    "hasDefaultProcessor": true
  }
}
```

### 4. 强制重新注册所有处理器

```http
POST /evaluate/processor/reregister
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "强制重新注册成功",
  "data": {
    "totalProcessors": 15,
    "specificProcessors": 2,
    "commonProcessors": 3,
    "hasDefaultProcessor": true
  }
}
```

### 5. 测试处理器功能

```http
POST /evaluate/processor/test
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "处理器功能测试完成",
  "data": {
    "testCases": [
      {
        "description": "数据安全-创建计划",
        "evaluateType": "data_security_plan",
        "stepCode": "create_plan",
        "success": true,
        "processorClass": "CreatePlanProcessor",
        "message": "找到处理器"
      }
    ],
    "totalTests": 4,
    "successCount": 3
  }
}
```

### 6. 获取支持的评估类型列表

```http
GET /evaluate/processor/types
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取支持的评估类型成功",
  "data": {
    "supportedTypes": ["data_security_plan", "basic_info"],
    "count": 2
  }
}
```

### 7. 获取指定评估类型支持的步骤列表

```http
GET /evaluate/processor/steps/{evaluateType}
```

**请求示例：**
```http
GET /evaluate/processor/steps/data_security_plan
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取支持的步骤列表成功",
  "data": {
    "evaluateType": "data_security_plan",
    "supportedSteps": ["create_plan", "evaluate_scope", "system_check"],
    "count": 3
  }
}
```

### 8. 查找指定处理器

```http
GET /evaluate/processor/find?evaluateType={evaluateType}&stepCode={stepCode}
```

**请求示例：**
```http
GET /evaluate/processor/find?evaluateType=data_security_plan&stepCode=create_plan
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "处理器查找完成",
  "data": {
    "found": true,
    "processorClass": "CreatePlanProcessor",
    "processorName": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurity.CreatePlanProcessor",
    "evaluateType": "data_security_plan",
    "stepCode": "create_plan",
    "searchCriteria": {
      "evaluateType": "data_security_plan",
      "stepCode": "create_plan"
    }
  }
}
```

### 9. 手动注册处理器

```http
POST /evaluate/processor/register
Content-Type: application/json
```

**请求体示例：**
```json
{
  "operationType": "REGISTER",
  "processorClassName": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.custom.CustomProcessor",
  "evaluateType": "custom_type",
  "stepCode": "custom_step",
  "description": "自定义处理器",
  "forceRegister": false,
  "operator": "admin"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "手动注册处理器成功",
  "data": {
    "action": "MANUAL_REGISTER",
    "processorClassName": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.custom.CustomProcessor",
    "evaluateType": "custom_type",
    "stepCode": "custom_step",
    "status": "SIMULATED",
    "message": "手动注册功能已记录，实际注册需要重启应用或使用热重载功能"
  }
}
```

### 10. 批量注册处理器

```http
POST /evaluate/processor/batch-register
Content-Type: application/json
```

**请求体示例：**
```json
{
  "operationType": "BATCH_REGISTER",
  "processors": [
    {
      "className": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.custom.CustomProcessor1",
      "evaluateType": "custom_type",
      "stepCode": "step1",
      "description": "自定义处理器1",
      "priority": 1,
      "enabled": true
    },
    {
      "className": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.custom.CustomProcessor2",
      "evaluateType": "custom_type",
      "stepCode": "step2",
      "description": "自定义处理器2",
      "priority": 2,
      "enabled": true
    }
  ],
  "operator": "admin"
}
```

### 11. 获取所有已注册的处理器列表

```http
GET /evaluate/processor/list
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取处理器列表成功",
  "data": {
    "processors": [
      {
        "className": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurity.CreatePlanProcessor",
        "simpleName": "CreatePlanProcessor",
        "evaluateType": "data_security_plan",
        "stepCode": "create_plan",
        "packageName": "com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurity"
      }
    ],
    "totalCount": 15
  }
}
```

## 权限配置

控制器中的各个接口需要相应的权限：

- `evaluate:processor:status` - 查看处理器状态
- `evaluate:processor:report` - 生成状态报告
- `evaluate:processor:refresh` - 刷新注册表
- `evaluate:processor:reregister` - 重新注册
- `evaluate:processor:test` - 测试处理器
- `evaluate:processor:types` - 查看评估类型
- `evaluate:processor:steps` - 查看步骤列表
- `evaluate:processor:find` - 查找处理器
- `evaluate:processor:register` - 手动注册
- `evaluate:processor:batchRegister` - 批量注册
- `evaluate:processor:list` - 查看处理器列表

## 配置说明

### 1. 启用/禁用控制器

通过配置属性控制控制器是否启用：

```properties
# 启用处理器管理控制器（默认启用）
processor.management.enabled=true
```

### 2. 热重载功能

热重载功能通过 `ProcessorHotReloadHelper` 提供，需要配置：

```properties
# 启用处理器热重载功能
processor.hot-reload.enabled=true
```

## 使用场景

### 1. 开发环境
- 开发新处理器后，使用刷新功能重新加载
- 测试处理器匹配逻辑是否正确
- 查看当前注册的处理器状态

### 2. 运维管理
- 监控处理器注册状态
- 排查处理器匹配问题
- 生成处理器状态报告

### 3. 故障排查
- 查找特定处理器是否正确注册
- 测试处理器功能是否正常
- 强制重新注册解决注册问题

## 注意事项

1. **权限控制**：所有接口都需要相应的权限才能访问
2. **环境限制**：建议主要在开发和测试环境使用
3. **手动注册限制**：当前的手动注册是模拟实现，实际的动态注册需要更复杂的类加载机制
4. **热重载依赖**：部分功能依赖 `ProcessorHotReloadHelper`，需要确保相关配置正确
5. **日志记录**：所有操作都会记录详细的日志，便于追踪和调试
