# 若依数据安全评估平台 Docker 部署

## 📁 目录结构

```
deploy/
├── Dockerfile                    # 应用Docker镜像构建文件
├── docker-compose.yml           # Docker Compose主配置文件
├── .env                         # 环境变量配置
├── .dockerignore               # Docker构建忽略文件
├── deploy.sh                   # Linux/macOS部署脚本
├── deploy.bat                  # Windows部署脚本
├── ruoyi_admin.jar             # 应用JAR包（需要您提供）
├── mysql/
│   └── conf/
│       └── my.cnf             # MySQL配置文件
├── redis/
│   └── redis.conf             # Redis配置文件
├── nginx/
│   └── conf.d/
│       └── default.conf       # Nginx配置文件
├── sql/                       # 数据库初始化脚本目录
└── docs/                      # 详细文档目录
```

## 🚀 快速开始

### 1. 准备JAR包
将您编译好的JAR包命名为 `ruoyi_admin.jar` 并放在当前目录下

### 2. 检查JAR包
```bash
# Linux/macOS
./deploy.sh check

# Windows
deploy.bat check
```

### 3. 启动服务
```bash
# Linux/macOS
./deploy.sh start

# Windows
deploy.bat start
```

### 4. 访问应用
- 地址: http://localhost:8080
- 账号: admin
- 密码: admin123

## 🔧 服务组件

- **ruoyi-app**: Spring Boot应用 (端口: 8080)
- **mysql**: MySQL 5.7数据库 (端口: 3306)
- **redis**: Redis 7缓存 (端口: 6379)
- **nginx**: Nginx反向代理 (端口: 80, 443)

## 📋 常用命令

```bash
# 检查JAR包
./deploy.sh check

# 查看服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 重启服务
./deploy.sh restart

# 停止服务
./deploy.sh stop
```

## 📖 详细文档

- [DOCKER_DEPLOY.md](../DOCKER_DEPLOY.md) - 完整的部署指南
- [JAR_DEPLOYMENT_GUIDE.md](../JAR_DEPLOYMENT_GUIDE.md) - JAR包部署说明
- [MYSQL_5.7_NOTES.md](../MYSQL_5.7_NOTES.md) - MySQL 5.7配置说明

## ⚠️ 注意事项

1. **JAR包命名**: 必须命名为 `ruoyi_admin.jar`
2. **目录位置**: 所有操作都在 `deploy` 目录下进行
3. **端口占用**: 确保8080、3306、6379端口未被占用
4. **权限设置**: Linux/macOS下需要给脚本执行权限：`chmod +x deploy.sh`

## 🆘 故障排除

如果遇到问题，请：
1. 检查JAR包是否存在且命名正确
2. 查看服务日志：`./deploy.sh logs`
3. 检查端口是否被占用
4. 参考详细文档进行排查
