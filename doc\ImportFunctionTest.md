# 导入功能测试指南

## 测试准备

### 1. 创建测试Excel文件

#### 被评估单位基本信息测试数据 (company_test.xlsx)
```
公司名称 | 单位简称 | 公司地址 | 邮政编码 | 联系人姓名 | 职务 | 部门 | 电话 | 手机号码 | 邮箱
测试公司A | 公司A | 北京市朝阳区 | 100000 | 张三 | 经理 | 技术部 | 010-12345678 | 13800138000 | <EMAIL>
测试公司B | 公司B | 上海市浦东区 | 200000 | 李四 | 总监 | 运营部 | 021-87654321 | 13900139000 | <EMAIL>
```

#### 部门信息测试数据 (dept_test.xlsx)
```
所属单位ID | 部门名称 | 部门职责 | 部门负责人 | 状态
1 | 技术部 | 负责系统开发和维护 | 张三 | 1
1 | 运营部 | 负责业务运营 | 李四 | 1
2 | 财务部 | 负责财务管理 | 王五 | 1
```

#### 人员信息测试数据 (member_test.xlsx)
```
所属单位ID | 人员姓名 | 岗位或角色名称 | 所属部门 | 岗位职责 | 是否专职 | 国籍
1 | 张三 | 技术经理 | 技术部 | 负责技术团队管理 | 是 | 中国
1 | 李四 | 运营总监 | 运营部 | 负责运营策略制定 | 是 | 中国
2 | 王五 | 财务经理 | 财务部 | 负责财务核算 | 是 | 中国
```

#### 文档信息测试数据 (documents_test.xlsx)
```
所属单位ID | 文档名称 | 主要内容 | 使用范围 | 状态
1 | 数据安全管理制度 | 规范数据安全管理流程 | 全公司 | 1
1 | 技术开发规范 | 软件开发技术标准 | 技术部 | 1
2 | 财务管理制度 | 财务核算和报告规范 | 财务部 | 1
```

## 测试步骤

### 1. 单独导入测试

#### 测试被评估单位基本信息导入
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importCompanyInfo" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@company_test.xlsx"
```

#### 测试部门信息导入
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importDeptInfo" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@dept_test.xlsx"
```

#### 测试人员信息导入
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importMemberInfo" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@member_test.xlsx"
```

#### 测试文档信息导入
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importDocuments" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@documents_test.xlsx"
```

### 2. 批量导入测试

```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/batchImport" \
  -H "Content-Type: multipart/form-data" \
  -F "companyFile=@company_test.xlsx" \
  -F "deptFile=@dept_test.xlsx" \
  -F "memberFile=@member_test.xlsx" \
  -F "documentsFile=@documents_test.xlsx"
```

### 3. 模板下载测试

#### 下载被评估单位基本信息模板
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importCompanyTemplate" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output company_template.xlsx
```

#### 下载部门信息模板
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importDeptTemplate" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output dept_template.xlsx
```

#### 下载人员信息模板
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importMemberTemplate" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output member_template.xlsx
```

#### 下载文档信息模板
```bash
curl -X POST "http://localhost:8080/targetCompany/companyInfo/importDocumentsTemplate" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output documents_template.xlsx
```

## 预期结果

### 成功导入响应示例
```json
{
  "code": 200,
  "msg": "导入完成，成功：2条，失败：0条",
  "data": null
}
```

### 部分失败导入响应示例
```json
{
  "code": 200,
  "msg": "导入完成，成功：2条，失败：1条。失败原因：第3行导入失败：所属单位ID不能为空；",
  "data": null
}
```

### 批量导入响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 10,
    "totalFailure": 0,
    "message": "批量导入完成。总计成功：10条，失败：0条。详情：公司信息：成功2条，失败0条；部门信息：成功3条，失败0条；人员信息：成功3条，失败0条；文档信息：成功3条，失败0条；"
  }
}
```

## 错误处理测试

### 1. 测试空文件
上传空的Excel文件，应该返回成功但导入0条记录。

### 2. 测试格式错误文件
上传非Excel格式文件，应该返回错误信息。

### 3. 测试必需字段缺失
在Excel中删除必需字段的值，应该返回相应的错误信息。

### 4. 测试外键约束
在部门、人员、文档信息中使用不存在的orgId，应该返回外键约束错误。

## 性能测试

### 1. 大文件测试
创建包含1000条记录的Excel文件进行导入测试。

### 2. 并发测试
同时进行多个导入操作，测试系统的并发处理能力。

## 验证数据

导入完成后，可以通过以下接口验证数据是否正确导入：

```bash
# 查询被评估单位列表
curl -X GET "http://localhost:8080/targetCompany/companyInfo/list"

# 查询部门信息列表
curl -X GET "http://localhost:8080/targetDept/info/list"

# 查询人员信息列表
curl -X GET "http://localhost:8080/targetMember/info/list"

# 查询文档信息列表
curl -X GET "http://localhost:8080/targetDocuments/documents/list"
```
