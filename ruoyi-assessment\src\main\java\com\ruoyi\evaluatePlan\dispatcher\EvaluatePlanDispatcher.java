package com.ruoyi.evaluatePlan.dispatcher;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;
import com.ruoyi.evaluatePlan.domain.EvaluateTaskRequest;
import com.ruoyi.evaluatePlan.domain.EvaluateStepRequest;
import com.ruoyi.evaluatePlan.strategy.EvaluateStrategy;
import com.ruoyi.evaluatePlan.strategy.ProcessAwareEvaluateStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 评估计划分发器
 * 根据评估类型分发到对应的策略处理器
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Component
public class EvaluatePlanDispatcher {

    @Autowired
    private List<EvaluateStrategy> evaluateStrategies;

    /** 策略映射表 */
    private final Map<String, EvaluateStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        if (evaluateStrategies != null) {
            for (EvaluateStrategy strategy : evaluateStrategies) {
                String evaluateType = strategy.getEvaluateType();
                if (StringUtils.hasText(evaluateType)) {
                    strategyMap.put(evaluateType, strategy);
                    log.info("注册评估策略: {} -> {}", evaluateType, strategy.getStrategyName());
                }
            }
        }
        log.info("评估策略初始化完成，共注册 {} 个策略", strategyMap.size());
    }

    /**
     * 分发评估请求
     * @param request 评估请求
     * @return 评估响应
     */
    public EvaluatePlanResponse dispatch(EvaluatePlanRequest request) {
        try {
            // 参数校验
            if (request == null || !StringUtils.hasText(request.getEvaluateType())) {
                throw new ServiceException("评估类型不能为空");
            }

            // 获取对应策略
            EvaluateStrategy strategy = getStrategy(request.getEvaluateType());
            
            // 验证请求参数
            if (!strategy.validateRequest(request)) {
                throw new ServiceException("评估请求参数验证失败");
            }

            log.info("开始执行评估计划，类型: {}, 策略: {}", 
                    request.getEvaluateType(), strategy.getStrategyName());

            // 执行评估
            EvaluatePlanResponse response = strategy.executeEvaluate(request);
            
            log.info("评估计划执行完成，计划ID: {}, 结果: {}", 
                    request.getPlanId(), response.getSuccess() ? "成功" : "失败");
            
            return response;
            
        } catch (Exception e) {
            log.error("评估计划执行异常: {}", e.getMessage(), e);
            return EvaluatePlanResponse.failure(request.getPlanId(), "评估执行异常: " + e.getMessage());
        }
    }

    /**
     * 获取评估进度
     * @param evaluateType 评估类型
     * @param planId 计划ID
     * @return 进度百分比
     */
    public Integer getProgress(String evaluateType, Long planId) {
        try {
            EvaluateStrategy strategy = getStrategy(evaluateType);
            return strategy.getProgress(planId);
        } catch (Exception e) {
            log.error("获取评估进度异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 生成评估报告
     * @param request 评估请求
     * @return 报告内容
     */
    public String generateReport(EvaluatePlanRequest request) {
        try {
            EvaluateStrategy strategy = getStrategy(request.getEvaluateType());
            return strategy.generateReport(request);
        } catch (Exception e) {
            log.error("生成评估报告异常: {}", e.getMessage(), e);
            throw new ServiceException("生成评估报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取策略实例
     * @param evaluateType 评估类型
     * @return 策略实例
     */
    private EvaluateStrategy getStrategy(String evaluateType) {
        EvaluateStrategy strategy = strategyMap.get(evaluateType);
        if (strategy == null) {
            throw new ServiceException("不支持的评估类型: " + evaluateType);
        }
        return strategy;
    }

    /**
     * 获取所有支持的评估类型
     * @return 评估类型列表
     */
    public Map<String, String> getSupportedTypes() {
        Map<String, String> types = new HashMap<>();
        for (Map.Entry<String, EvaluateStrategy> entry : strategyMap.entrySet()) {
            types.put(entry.getKey(), entry.getValue().getStrategyName());
        }
        return types;
    }

    /**
     * 执行指定任务内容
     * @param taskRequest 任务执行请求
     * @return 任务执行响应
     */
    public EvaluatePlanResponse executeTask(EvaluateTaskRequest taskRequest) {
        try {
            // 参数校验
            if (taskRequest == null || !StringUtils.hasText(taskRequest.getEvaluateType())) {
                throw new ServiceException("评估类型不能为空");
            }
            if (!StringUtils.hasText(taskRequest.getTaskType())) {
                throw new ServiceException("任务类型不能为空");
            }

            // 获取对应策略
            EvaluateStrategy strategy = getStrategy(taskRequest.getEvaluateType());

            log.info("开始执行任务内容，评估类型: {}, 任务类型: {}, 计划ID: {}",
                    taskRequest.getEvaluateType(), taskRequest.getTaskType(), taskRequest.getPlanId());

            // 构建评估请求
            EvaluatePlanRequest planRequest = buildPlanRequestFromTask(taskRequest);

            // 执行任务内容
            EvaluatePlanResponse response = strategy.executeEvaluate(planRequest);

            // 设置任务相关信息
            response.getExtData().put("taskType", taskRequest.getTaskType());
            response.getExtData().put("taskParams", taskRequest.getTaskParams());

            log.info("任务内容执行完成，计划ID: {}, 任务类型: {}, 结果: {}",
                    taskRequest.getPlanId(), taskRequest.getTaskType(), response.getSuccess() ? "成功" : "失败");

            return response;

        } catch (Exception e) {
            log.error("任务内容执行异常: {}", e.getMessage(), e);
            return EvaluatePlanResponse.failure(taskRequest.getPlanId(), "任务内容执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行指定步骤任务
     * @param stepRequest 步骤执行请求
     * @return 步骤执行响应
     */
    public EvaluatePlanResponse executeStep(EvaluateStepRequest stepRequest) {
        try {
            // 参数校验
            if (stepRequest == null || !StringUtils.hasText(stepRequest.getEvaluateType())) {
                throw new ServiceException("评估类型不能为空");
            }
            if (!StringUtils.hasText(stepRequest.getStepCode())) {
                throw new ServiceException("步骤编码不能为空");
            }

            // 获取对应策略
            EvaluateStrategy strategy = getStrategy(stepRequest.getEvaluateType());

            log.info("开始执行步骤任务，评估类型: {}, 步骤编码: {}, 流程实例ID: {}",
                    stepRequest.getEvaluateType(), stepRequest.getStepCode(), stepRequest.getProcessInstanceId());

            // 如果策略支持流程感知，使用步骤执行方法
            if (strategy instanceof ProcessAwareEvaluateStrategy) {
                ProcessAwareEvaluateStrategy processStrategy = (ProcessAwareEvaluateStrategy) strategy;

                // 构建评估请求
                EvaluatePlanRequest planRequest = buildPlanRequestFromStep(stepRequest);

                // 执行指定步骤
                EvaluatePlanResponse response = processStrategy.executeStep(
                        planRequest, stepRequest.getStepCode(), stepRequest.getProcessInstanceId());

                // 设置步骤相关信息
                response.getExtData().put("stepCode", stepRequest.getStepCode());
                response.getExtData().put("stepParams", stepRequest.getStepParams());

                log.info("步骤任务执行完成，计划ID: {}, 步骤编码: {}, 结果: {}",
                        stepRequest.getPlanId(), stepRequest.getStepCode(), response.getSuccess() ? "成功" : "失败");

                return response;
            } else {
                throw new ServiceException("该评估类型不支持步骤级别的任务执行");
            }

        } catch (Exception e) {
            log.error("步骤任务执行异常: {}", e.getMessage(), e);
            return EvaluatePlanResponse.failure(stepRequest.getPlanId(), "步骤任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 获取可用任务列表
     * @param evaluateType 评估类型
     * @param planId 计划ID
     * @return 任务列表
     */
    public Map<String, Object> getAvailableTasks(String evaluateType, Long planId) {
        try {
            EvaluateStrategy strategy = getStrategy(evaluateType);
            Map<String, Object> result = new HashMap<>();

            // 基础任务信息
            result.put("evaluateType", evaluateType);
            result.put("strategyName", strategy.getStrategyName());
            result.put("planId", planId);

            // 数据安全评估策略支持流程感知，获取详细任务信息
            if (strategy instanceof ProcessAwareEvaluateStrategy) {
                // 如果是数据安全评估策略，使用专门的任务管理器
                if ("data_security_plan".equals(evaluateType)) {
                    // 通过Spring上下文获取任务管理器（简化处理）
                    List<Map<String, Object>> tasks = getDataSecurityTasks();
                    result.put("tasks", tasks);
                    result.put("totalCount", tasks.size());
                } else {
                    // 其他流程感知策略的默认任务
                    List<Map<String, Object>> tasks = getDefaultProcessAwareTasks();
                    result.put("tasks", tasks);
                    result.put("totalCount", tasks.size());
                }
            } else {
                // 基础策略只返回完整评估任务
                List<Map<String, Object>> tasks = new ArrayList<>();
                Map<String, Object> task = new HashMap<>();
                task.put("taskType", "full_evaluate");
                task.put("taskName", "完整评估");
                task.put("description", "执行完整的评估流程");
                task.put("status", "available");
                task.put("estimatedDuration", "2-3小时");
                tasks.add(task);

                result.put("tasks", tasks);
                result.put("totalCount", 1);
            }

            return result;

        } catch (Exception e) {
            log.error("获取可用任务列表异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取任务列表失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取步骤状态
     * @param evaluateType 评估类型
     * @param planId 计划ID
     * @param stepCode 步骤编码
     * @return 步骤状态信息
     */
    public Map<String, Object> getStepStatus(String evaluateType, Long planId, String stepCode) {
        try {
            EvaluateStrategy strategy = getStrategy(evaluateType);
            Map<String, Object> result = new HashMap<>();

            // 基础信息
            result.put("evaluateType", evaluateType);
            result.put("strategyName", strategy.getStrategyName());
            result.put("planId", planId);
            result.put("stepCode", stepCode);

            // 如果策略支持流程感知，获取详细步骤状态
            if (strategy instanceof ProcessAwareEvaluateStrategy) {
                ProcessAwareEvaluateStrategy processStrategy = (ProcessAwareEvaluateStrategy) strategy;

                // 模拟步骤状态（实际应该从数据库获取）
                if (StringUtils.hasText(stepCode)) {
                    // 获取指定步骤状态
                    Map<String, Object> stepInfo = new HashMap<>();
                    stepInfo.put("stepCode", stepCode);
                    stepInfo.put("stepName", getStepNameByCode(stepCode));
                    stepInfo.put("status", "completed"); // 0-待执行 1-执行中 2-已完成 3-失败
                    stepInfo.put("progress", 100);
                    stepInfo.put("canSkip", processStrategy.canSkipStep(stepCode, planId));
                    stepInfo.put("estimatedDuration", processStrategy.getEstimatedStepDuration(stepCode));
                    stepInfo.put("dependencies", processStrategy.getStepDependencies(stepCode));

                    result.put("stepInfo", stepInfo);
                } else {
                    // 获取所有步骤状态
                    List<Map<String, Object>> allSteps = new ArrayList<>();
                    String[] stepCodes = {"data_asset_identify", "data_classify", "risk_identify", "risk_assessment", "control_measures", "report_generate"};

                    for (String code : stepCodes) {
                        Map<String, Object> stepInfo = new HashMap<>();
                        stepInfo.put("stepCode", code);
                        stepInfo.put("stepName", getStepNameByCode(code));
                        stepInfo.put("status", "completed");
                        stepInfo.put("progress", 100);
                        stepInfo.put("canSkip", processStrategy.canSkipStep(code, planId));
                        allSteps.add(stepInfo);
                    }

                    result.put("allSteps", allSteps);
                    result.put("totalSteps", allSteps.size());
                }
            } else {
                // 基础策略返回整体状态
                result.put("overallStatus", "completed");
                result.put("overallProgress", strategy.getProgress(planId));
            }

            return result;

        } catch (Exception e) {
            log.error("获取步骤状态异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取步骤状态失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 构建评估请求（从任务请求）
     */
    private EvaluatePlanRequest buildPlanRequestFromTask(EvaluateTaskRequest taskRequest) {
        EvaluatePlanRequest planRequest = new EvaluatePlanRequest();
        planRequest.setPlanId(taskRequest.getPlanId());
        planRequest.setEvaluateType(taskRequest.getEvaluateType());
        planRequest.setTitle(taskRequest.getTaskTitle());
        planRequest.setDescription(taskRequest.getTaskDescription());
        planRequest.setTargetCompanyId(taskRequest.getTargetCompanyId());
        planRequest.setEvaluateCompanyId(taskRequest.getEvaluateCompanyId());
        planRequest.setExtParams(taskRequest.getExtParams());
        planRequest.setCreateBy(taskRequest.getOperator());
        return planRequest;
    }

    /**
     * 构建评估请求（从步骤请求）
     */
    private EvaluatePlanRequest buildPlanRequestFromStep(EvaluateStepRequest stepRequest) {
        EvaluatePlanRequest planRequest = new EvaluatePlanRequest();
        planRequest.setPlanId(stepRequest.getPlanId());
        planRequest.setEvaluateType(stepRequest.getEvaluateType());
        planRequest.setTitle(stepRequest.getStepName());
        planRequest.setDescription(stepRequest.getStepDescription());
        planRequest.setTargetCompanyId(stepRequest.getTargetCompanyId());
        planRequest.setEvaluateCompanyId(stepRequest.getEvaluateCompanyId());
        planRequest.setExtParams(stepRequest.getExtParams());
        planRequest.setCreateBy(stepRequest.getOperator());
        return planRequest;
    }

    /**
     * 获取数据安全评估任务列表
     */
    private List<Map<String, Object>> getDataSecurityTasks() {
        List<Map<String, Object>> tasks = new ArrayList<>();

        // 数据收集任务
        Map<String, Object> task1 = new HashMap<>();
        task1.put("taskType", "data_collection");
        task1.put("taskName", "数据收集");
        task1.put("description", "收集评估所需的基础数据资产信息");
        task1.put("status", "available");
        task1.put("estimatedDuration", "30分钟");
        task1.put("supportedParams", Arrays.asList("dataScope", "includeBackup", "timeRange"));
        tasks.add(task1);

        // 风险分析任务
        Map<String, Object> task2 = new HashMap<>();
        task2.put("taskType", "risk_analysis");
        task2.put("taskName", "风险分析");
        task2.put("description", "分析识别的数据安全风险点");
        task2.put("status", "available");
        task2.put("estimatedDuration", "45分钟");
        task2.put("supportedParams", Arrays.asList("analysisDepth", "includeThirdParty", "riskCategories"));
        tasks.add(task2);

        // 控制措施建议任务
        Map<String, Object> task3 = new HashMap<>();
        task3.put("taskType", "control_measures");
        task3.put("taskName", "控制措施建议");
        task3.put("description", "提出数据安全控制措施和整改建议");
        task3.put("status", "available");
        task3.put("estimatedDuration", "40分钟");
        task3.put("supportedParams", Arrays.asList("measureTypes", "priorityLevel", "implementationPlan"));
        tasks.add(task3);

        // 报告生成任务
        Map<String, Object> task4 = new HashMap<>();
        task4.put("taskType", "report_generation");
        task4.put("taskName", "报告生成");
        task4.put("description", "生成数据安全风险评估报告");
        task4.put("status", "available");
        task4.put("estimatedDuration", "15分钟");
        task4.put("supportedParams", Arrays.asList("reportFormat", "includeCharts", "detailLevel"));
        tasks.add(task4);

        return tasks;
    }

    /**
     * 获取默认流程感知任务列表
     */
    private List<Map<String, Object>> getDefaultProcessAwareTasks() {
        List<Map<String, Object>> tasks = new ArrayList<>();

        Map<String, Object> task1 = new HashMap<>();
        task1.put("taskType", "data_collection");
        task1.put("taskName", "数据收集");
        task1.put("description", "收集评估所需的基础数据");
        task1.put("status", "available");
        task1.put("estimatedDuration", "30分钟");
        tasks.add(task1);

        Map<String, Object> task2 = new HashMap<>();
        task2.put("taskType", "analysis");
        task2.put("taskName", "分析评估");
        task2.put("description", "分析收集的数据");
        task2.put("status", "available");
        task2.put("estimatedDuration", "45分钟");
        tasks.add(task2);

        Map<String, Object> task3 = new HashMap<>();
        task3.put("taskType", "report_generation");
        task3.put("taskName", "报告生成");
        task3.put("description", "生成评估报告");
        task3.put("status", "available");
        task3.put("estimatedDuration", "15分钟");
        tasks.add(task3);

        return tasks;
    }

    /**
     * 根据步骤编码获取步骤名称
     */
    private String getStepNameByCode(String stepCode) {
        Map<String, String> stepNames = new HashMap<>();
        stepNames.put("data_asset_identify", "数据资产识别");
        stepNames.put("data_classify", "数据分类分级");
        stepNames.put("risk_identify", "风险识别分析");
        stepNames.put("risk_assessment", "风险等级评定");
        stepNames.put("control_measures", "控制措施建议");
        stepNames.put("report_generate", "报告生成");
        return stepNames.getOrDefault(stepCode, stepCode);
    }
}
