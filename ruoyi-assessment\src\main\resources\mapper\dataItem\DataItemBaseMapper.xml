<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemBaseMapper">
    
    <resultMap type="DataItemBase" id="DataItemBaseResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="dataItemId"    column="data_item_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPosition"    column="contact_position"    />
        <result property="orgDept"    column="org_dept"    />
        <result property="mobile"    column="mobile"    />
        <result property="email"    column="email"    />
        <result property="dataFlowImg"    column="data_flow_img"    />
        <result property="assessmentImg"    column="assessment_img"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataItemBaseVo">
        select id, org_id, data_item_id, contact_name, contact_position, org_dept, mobile, email, data_flow_img, assessment_img, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_data_item_base
    </sql>

    <select id="selectDataItemBaseList" parameterType="DataItemBase" resultMap="DataItemBaseResult">
        <include refid="selectDataItemBaseVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="dataItemId != null "> and data_item_id = #{dataItemId}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPosition != null  and contactPosition != ''"> and contact_position = #{contactPosition}</if>
            <if test="orgDept != null  and orgDept != ''"> and org_dept = #{orgDept}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="dataFlowImg != null  and dataFlowImg != ''"> and data_flow_img = #{dataFlowImg}</if>
            <if test="assessmentImg != null  and assessmentImg != ''"> and assessment_img = #{assessmentImg}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDataItemBaseById" parameterType="Long" resultMap="DataItemBaseResult">
        <include refid="selectDataItemBaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemBase" parameterType="DataItemBase" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="dataItemId != null">data_item_id,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPosition != null">contact_position,</if>
            <if test="orgDept != null">org_dept,</if>
            <if test="mobile != null">mobile,</if>
            <if test="email != null">email,</if>
            <if test="dataFlowImg != null">data_flow_img,</if>
            <if test="assessmentImg != null">assessment_img,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="dataItemId != null">#{dataItemId},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPosition != null">#{contactPosition},</if>
            <if test="orgDept != null">#{orgDept},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="email != null">#{email},</if>
            <if test="dataFlowImg != null">#{dataFlowImg},</if>
            <if test="assessmentImg != null">#{assessmentImg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataItemBase" parameterType="DataItemBase">
        update dsa_data_item_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="dataItemId != null">data_item_id = #{dataItemId},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPosition != null">contact_position = #{contactPosition},</if>
            <if test="orgDept != null">org_dept = #{orgDept},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="email != null">email = #{email},</if>
            <if test="dataFlowImg != null">data_flow_img = #{dataFlowImg},</if>
            <if test="assessmentImg != null">assessment_img = #{assessmentImg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemBaseById" parameterType="Long">
        delete from dsa_data_item_base where id = #{id}
    </delete>

    <delete id="deleteDataItemBaseByIds" parameterType="String">
        delete from dsa_data_item_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="physicalDeleteByOrgId" parameterType="Long">
        delete from dsa_data_item_base where org_id = #{orgId}
    </delete>

    <delete id="deleteByDataItemIds" parameterType="java.util.List">
        delete from dsa_data_item_base where data_item_id in
        <foreach collection="dataItemIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>