package com.ruoyi.evaluate.evaluatePlan.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/29 10:35
 * @description
 */
@Data
public class EvaluatePlanTaskDto {
    /** 所属单位ID */
    private Long orgId;

    /** 评估单位ID */
    private Long evaluateOrgId;

    /** 评估模型id */
    private Long modelId;

    /** 报告编号 */
    private String reportNo;

    /** 任务名称 */
    private String name;

    /** 计划开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;

    /** 计划结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deadline;

    /** 任务描述 */
    private String taskDescription;

    /** 父任务ID（用于任务层级关系） */
    private Long originTaskId;

    /** 下一步负责人 */
    private String assignee;

    /** 下一步负责部门 */
    private String stepDept;

    /** 下一步截止日期（相对天数） */
    private Integer deadlineDays;

    /** 是否设置任务截止日期（0-否，1-是） */
    private Integer setTaskDeadline;


}
