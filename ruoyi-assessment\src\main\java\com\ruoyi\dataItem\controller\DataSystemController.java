package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.group.ListGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataSystem;
import com.ruoyi.dataItem.service.IDataSystemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据承载系统Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/dataItem/system")
@Api(value = "数据承载系统控制器", tags = {"数据承载系统管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataSystemController extends BaseController
{
    private final IDataSystemService dataSystemService;

    /**
     * 查询数据承载系统列表
     */
    @ApiOperation("查询数据承载系统列表")
    @PreAuthorize("@ss.hasPermi('dataItem:system:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) DataSystem dataSystem) {
        startPage();
        List<DataSystem> list = dataSystemService.list(new QueryWrapper<DataSystem>(dataSystem));
        return getDataTable(list);
    }

    /**
     * 获取数据承载系统详细信息
     */
    @ApiOperation("获取数据承载系统详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:system:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataSystemService.getById(id));
    }

    /**
     * 新增数据承载系统
     */
    @ApiOperation("新增数据承载系统")
    @PreAuthorize("@ss.hasPermi('dataItem:system:add')")
    @Log(title = "数据承载系统", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataSystem dataSystem) {
        return toAjax(dataSystemService.save(dataSystem));
    }

    /**
     * 修改数据承载系统
     */
    @ApiOperation("修改数据承载系统")
    @PreAuthorize("@ss.hasPermi('dataItem:system:edit')")
    @Log(title = "数据承载系统", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataSystem dataSystem) {
        return toAjax(dataSystemService.updateById(dataSystem));
    }

    /**
     * 删除数据承载系统
     */
    @ApiOperation("删除数据承载系统")
    @PreAuthorize("@ss.hasPermi('dataItem:system:remove')")
    @Log(title = "数据承载系统", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataSystemService.removeByIds(Arrays.asList(ids)));
    }
}