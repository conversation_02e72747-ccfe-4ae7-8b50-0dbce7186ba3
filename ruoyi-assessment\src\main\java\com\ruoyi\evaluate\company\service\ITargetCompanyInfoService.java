package com.ruoyi.evaluate.company.service;

import com.ruoyi.evaluate.company.domain.TargetCompanyInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

/**
 * 被评估单位Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ITargetCompanyInfoService extends IService<TargetCompanyInfo> {

    /**
     * 导入包含5个sheet的Excel文件
     * Sheet0: 公司信息的另一部分
     * Sheet1: 公司信息的一部分
     * Sheet2: 部门信息
     * Sheet3: 人员信息
     * Sheet4: 文档信息
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> importZip(MultipartFile file) throws Exception;
}
