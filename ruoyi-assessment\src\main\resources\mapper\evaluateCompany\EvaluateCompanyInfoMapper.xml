<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateCompany.mapper.EvaluateCompanyInfoMapper">
    
    <resultMap type="EvaluateCompanyInfo" id="EvaluateCompanyInfoResult">
        <result property="id"    column="id"    />
        <result property="evaluateCompany"    column="evaluate_company"    />
        <result property="orgCode"    column="org_code"    />
        <result property="address"    column="address"    />
        <result property="postalCode"    column="postal_code"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactJob"    column="contact_job"    />
        <result property="contactDept"    column="contact_dept"    />
        <result property="contactTel"    column="contact_tel"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="editorSignImg"    column="editor_sign_img"    />
        <result property="auditorSignImg"    column="auditor_sign_img"    />
        <result property="approveSignImg"    column="approve_sign_img"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateCompanyInfoVo">
        select id, evaluate_company, org_code, address, postal_code, contact_name, contact_job, contact_dept, contact_tel, contact_phone, contact_email, editor_sign_img, auditor_sign_img, approve_sign_img, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_company_info
    </sql>

    <select id="selectEvaluateCompanyInfoList" parameterType="EvaluateCompanyInfo" resultMap="EvaluateCompanyInfoResult">
        <include refid="selectEvaluateCompanyInfoVo"/>
        <where>  
            <if test="evaluateCompany != null  and evaluateCompany != ''"> and evaluate_company = #{evaluateCompany}</if>
            <if test="orgCode != null  and orgCode != ''"> and org_code = #{orgCode}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="postalCode != null  and postalCode != ''"> and postal_code = #{postalCode}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactJob != null  and contactJob != ''"> and contact_job = #{contactJob}</if>
            <if test="contactDept != null  and contactDept != ''"> and contact_dept = #{contactDept}</if>
            <if test="contactTel != null  and contactTel != ''"> and contact_tel = #{contactTel}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="contactEmail != null  and contactEmail != ''"> and contact_email = #{contactEmail}</if>
            <if test="editorSignImg != null  and editorSignImg != ''"> and editor_sign_img = #{editorSignImg}</if>
            <if test="auditorSignImg != null  and auditorSignImg != ''"> and auditor_sign_img = #{auditorSignImg}</if>
            <if test="approveSignImg != null  and approveSignImg != ''"> and approve_sign_img = #{approveSignImg}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateCompanyInfoById" parameterType="Long" resultMap="EvaluateCompanyInfoResult">
        <include refid="selectEvaluateCompanyInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateCompanyInfo" parameterType="EvaluateCompanyInfo" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_company_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="evaluateCompany != null and evaluateCompany != ''">evaluate_company,</if>
            <if test="orgCode != null and orgCode != ''">org_code,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="postalCode != null and postalCode != ''">postal_code,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactJob != null and contactJob != ''">contact_job,</if>
            <if test="contactDept != null and contactDept != ''">contact_dept,</if>
            <if test="contactTel != null and contactTel != ''">contact_tel,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email,</if>
            <if test="editorSignImg != null">editor_sign_img,</if>
            <if test="auditorSignImg != null">auditor_sign_img,</if>
            <if test="approveSignImg != null">approve_sign_img,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="evaluateCompany != null and evaluateCompany != ''">#{evaluateCompany},</if>
            <if test="orgCode != null and orgCode != ''">#{orgCode},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="postalCode != null and postalCode != ''">#{postalCode},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactJob != null and contactJob != ''">#{contactJob},</if>
            <if test="contactDept != null and contactDept != ''">#{contactDept},</if>
            <if test="contactTel != null and contactTel != ''">#{contactTel},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">#{contactEmail},</if>
            <if test="editorSignImg != null">#{editorSignImg},</if>
            <if test="auditorSignImg != null">#{auditorSignImg},</if>
            <if test="approveSignImg != null">#{approveSignImg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateCompanyInfo" parameterType="EvaluateCompanyInfo">
        update dsa_evaluate_company_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="evaluateCompany != null and evaluateCompany != ''">evaluate_company = #{evaluateCompany},</if>
            <if test="orgCode != null and orgCode != ''">org_code = #{orgCode},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="postalCode != null and postalCode != ''">postal_code = #{postalCode},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactJob != null and contactJob != ''">contact_job = #{contactJob},</if>
            <if test="contactDept != null and contactDept != ''">contact_dept = #{contactDept},</if>
            <if test="contactTel != null and contactTel != ''">contact_tel = #{contactTel},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="editorSignImg != null">editor_sign_img = #{editorSignImg},</if>
            <if test="auditorSignImg != null">auditor_sign_img = #{auditorSignImg},</if>
            <if test="approveSignImg != null">approve_sign_img = #{approveSignImg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateCompanyInfoById" parameterType="Long">
        delete from dsa_evaluate_company_info where id = #{id}
    </delete>

    <delete id="deleteEvaluateCompanyInfoByIds" parameterType="String">
        delete from dsa_evaluate_company_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>