<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.process.mapper.ProcessStepInstanceMapper">
    
    <resultMap type="ProcessStepInstance" id="ProcessStepInstanceResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="stepDefinitionId"    column="step_definition_id"    />
        <result property="stepName"    column="step_name"    />
        <result property="status"    column="status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="durationMs"    column="duration_ms"    />
        <result property="operator"    column="operator"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProcessStepInstanceVo">
        select id, process_instance_id, step_definition_id, step_name, status, start_time, end_time, duration_ms, operator, remark, create_by, create_time, update_by, update_time, del_flag from dsa_process_step_instance
    </sql>

    <select id="selectProcessStepInstanceList" parameterType="ProcessStepInstance" resultMap="ProcessStepInstanceResult">
        <include refid="selectProcessStepInstanceVo"/>
        <where>  
            <if test="processInstanceId != null "> and process_instance_id = #{processInstanceId}</if>
            <if test="stepDefinitionId != null "> and step_definition_id = #{stepDefinitionId}</if>
            <if test="stepName != null  and stepName != ''"> and step_name like concat('%', #{stepName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="durationMs != null "> and duration_ms = #{durationMs}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
        </where>
    </select>
    
    <select id="selectProcessStepInstanceById" parameterType="Long" resultMap="ProcessStepInstanceResult">
        <include refid="selectProcessStepInstanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertProcessStepInstance" parameterType="ProcessStepInstance" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_process_step_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processInstanceId != null">process_instance_id,</if>
            <if test="stepDefinitionId != null">step_definition_id,</if>
            <if test="stepName != null and stepName != ''">step_name,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="durationMs != null">duration_ms,</if>
            <if test="operator != null">operator,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processInstanceId != null">#{processInstanceId},</if>
            <if test="stepDefinitionId != null">#{stepDefinitionId},</if>
            <if test="stepName != null and stepName != ''">#{stepName},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="durationMs != null">#{durationMs},</if>
            <if test="operator != null">#{operator},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateProcessStepInstance" parameterType="ProcessStepInstance">
        update dsa_process_step_instance
        <trim prefix="SET" suffixOverrides=",">
            <if test="processInstanceId != null">process_instance_id = #{processInstanceId},</if>
            <if test="stepDefinitionId != null">step_definition_id = #{stepDefinitionId},</if>
            <if test="stepName != null and stepName != ''">step_name = #{stepName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="durationMs != null">duration_ms = #{durationMs},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessStepInstanceById" parameterType="Long">
        delete from dsa_process_step_instance where id = #{id}
    </delete>

    <delete id="deleteProcessStepInstanceByIds" parameterType="String">
        delete from dsa_process_step_instance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>