# 评估计划步骤数据控制器架构说明

## 项目概述

根据您的需求，我们在 `EvaluatePlanTaskController` 同级创建了一个新的控制器 `EvaluatePlanStepDataController`，专门用于获取评估计划中不同步骤的数据。该控制器支持每个步骤的处理逻辑拆分和单独处理，每个步骤都有不同的数据结构。

## 架构设计

### 1. 核心组件

```
EvaluatePlanStepDataController (控制器层)
    ↓
IEvaluatePlanStepDataService (服务接口)
    ↓
EvaluatePlanStepDataServiceImpl (服务实现)
    ↓
StepDataProcessorFactory (处理器工厂)
    ↓
IStepDataProcessor (处理器接口)
    ↓
AbstractStepDataProcessor (抽象处理器)
    ↓
具体步骤处理器实现类
```

### 2. 设计模式

- **策略模式**: 不同步骤使用不同的处理策略
- **工厂模式**: 通过工厂类管理和分发处理器
- **模板方法模式**: 抽象处理器提供通用模板
- **依赖注入**: Spring自动注册和管理处理器

## 文件结构

### 控制器层
- `EvaluatePlanStepDataController.java` - 主控制器
- `EvaluatePlanStepDataControllerTest.java` - 测试控制器
- `EvaluatePlanStepDataControllerUsageExample.md` - 使用说明

### 服务层
- `IEvaluatePlanStepDataService.java` - 服务接口
- `impl/EvaluatePlanStepDataServiceImpl.java` - 服务实现

### 处理器层
- `processor/IStepDataProcessor.java` - 处理器接口
- `processor/StepDataProcessorFactory.java` - 处理器工厂
- `processor/AbstractStepDataProcessor.java` - 抽象处理器
- `processor/impl/DataAssetIdentifyProcessor.java` - 数据资产识别处理器
- `processor/impl/RiskIdentifyProcessor.java` - 风险识别处理器
- `processor/impl/DefaultStepDataProcessor.java` - 默认处理器

## 核心功能

### 1. 步骤数据获取
- **接口**: `GET /evaluatePlan/stepData/{planId}/{stepCode}`
- **功能**: 获取指定评估计划的指定步骤的详细数据
- **特点**: 每个步骤返回不同的数据结构

### 2. 数据概览
- **接口**: `GET /evaluatePlan/stepData/overview/{planId}`
- **功能**: 获取评估计划所有步骤的数据概览
- **特点**: 提供整体进度和状态信息

### 3. 数据结构定义
- **接口**: `GET /evaluatePlan/stepData/schema/{stepCode}`
- **功能**: 获取指定步骤的数据结构定义
- **特点**: 便于前端动态构建界面

### 4. 状态监控
- **接口**: `GET /evaluatePlan/stepData/status/{planId}/{stepCode}`
- **功能**: 获取步骤数据处理状态
- **特点**: 实时监控处理进度

### 5. 统计分析
- **接口**: `GET /evaluatePlan/stepData/statistics/{planId}/{stepCode}`
- **功能**: 获取步骤数据统计信息
- **特点**: 提供详细的数据分析

### 6. 类型查询
- **接口**: `GET /evaluatePlan/stepData/types`
- **功能**: 获取支持的步骤类型列表
- **特点**: 动态发现可用的处理器

## 步骤处理器实现

### 1. 数据资产识别处理器 (DataAssetIdentifyProcessor)
```java
@Component
public class DataAssetIdentifyProcessor extends AbstractStepDataProcessor {
    // 处理数据资产识别步骤的特定逻辑
    // 返回资产清单、分类统计、识别进度等数据
}
```

**数据结构特点**:
- 资产清单 (assetList)
- 统计信息 (statistics)
- 资产分类 (assetCategories)
- 识别进度 (identifyProgress)

### 2. 风险识别处理器 (RiskIdentifyProcessor)
```java
@Component
public class RiskIdentifyProcessor extends AbstractStepDataProcessor {
    // 处理风险识别分析步骤的特定逻辑
    // 返回风险清单、威胁分析、脆弱性评估等数据
}
```

**数据结构特点**:
- 风险清单 (riskList)
- 威胁分析 (threatAnalysis)
- 风险统计 (riskStatistics)
- 风险矩阵 (riskMatrix)
- 脆弱性分析 (vulnerabilityAnalysis)

### 3. 默认处理器 (DefaultStepDataProcessor)
```java
@Component
@Order(Integer.MAX_VALUE)
public class DefaultStepDataProcessor implements IStepDataProcessor {
    // 兜底处理器，处理没有专用处理器的步骤
    // 提供基础的通用数据结构
}
```

## 扩展机制

### 1. 添加新步骤处理器
```java
@Component
public class NewStepProcessor extends AbstractStepDataProcessor {
    
    @Override
    public String getStepCode() {
        return "new_step_code";
    }
    
    @Override
    protected Map<String, Object> processSpecificStepData(...) {
        // 实现具体的数据处理逻辑
    }
    
    // 实现其他必要方法...
}
```

### 2. 支持特定评估类型
```java
@Override
public String getEvaluateType() {
    return "specific_evaluate_type"; // 或返回null支持所有类型
}
```

### 3. 自动注册机制
- Spring会自动扫描并注册所有实现了 `IStepDataProcessor` 的Bean
- `StepDataProcessorFactory` 会自动管理这些处理器
- 支持运行时动态发现和调用

## 数据结构差异示例

### 数据资产识别步骤
```json
{
  "stepCode": "data_asset_identify",
  "assetList": [...],
  "statistics": {
    "totalAssets": 150,
    "assetsByType": {...}
  },
  "identifyProgress": 75.0
}
```

### 风险识别步骤
```json
{
  "stepCode": "risk_identify",
  "riskList": [...],
  "threatAnalysis": [...],
  "riskMatrix": {...},
  "vulnerabilityAnalysis": {...}
}
```

## 技术特点

### 1. 类型安全
- 使用泛型和接口确保类型安全
- 编译时检查，减少运行时错误

### 2. 可扩展性
- 插件化架构，易于添加新的步骤处理器
- 支持不同评估类型的差异化处理

### 3. 可维护性
- 清晰的分层架构
- 单一职责原则
- 完善的文档和示例

### 4. 性能优化
- 懒加载机制
- 缓存支持
- 异常隔离

## 使用建议

### 1. 开发新步骤处理器
1. 继承 `AbstractStepDataProcessor`
2. 实现必要的抽象方法
3. 添加 `@Component` 注解
4. 定义步骤编码和评估类型

### 2. 测试验证
1. 使用 `EvaluatePlanStepDataControllerTest` 进行基础测试
2. 编写单元测试验证处理器逻辑
3. 集成测试验证完整流程

### 3. 生产部署
1. 移除测试控制器
2. 配置适当的权限控制
3. 监控性能和错误日志

## 总结

这个新的控制器架构实现了您要求的功能：
- ✅ 获取评估计划的不同步骤数据
- ✅ 每个步骤有不同的处理逻辑和数据结构
- ✅ 支持步骤处理逻辑的拆分和单独处理
- ✅ 可扩展的架构设计
- ✅ 完整的文档和示例

该架构具有良好的可扩展性和可维护性，可以轻松添加新的步骤处理器，满足不同业务场景的需求。
