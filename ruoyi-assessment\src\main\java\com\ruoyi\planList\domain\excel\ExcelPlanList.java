package com.ruoyi.planList.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelFieldValidate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 评估计划清单对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class ExcelPlanList {
    /**
     *  ExcelProperty -> index 从0开始计数
     */

    private String serialNo;

    /**
     * 单位名称
     */
    @ExcelProperty(index = 1)
    private String orgName;

    /**
     * 数据项名称
     */
    @ExcelProperty(index = 2)
    private String dataItemName;

    /**
     * 数据类型
     */
    @ExcelProperty(index = 3)
    private String dataType;

    /**
     * 数据处理活动
     */
    @ExcelProperty(index = 4)
    private String dataProcessingActivity;

    /**
     * 数据级别
     */
    @ExcelProperty(index = 5)
    private String dataLevel;

    /**
     * 涉及信息系统名称
     */
    @ExcelProperty(index = 6)
    private String infoSystemName;

    /**
     * 重要系统类别
     */
    @ExcelProperty(index = 7)
    private String importantSystemCategory;

    /**
     * 业务条线
     */
    @ExcelProperty(index = 8)
    private String businessLine;

    /**
     * 计划评估完成时间
     */
    @ExcelProperty(index = 9)
    private String plannedEvalTime;

    /**
     * 实际评估完成时间
     */
    @ExcelProperty(index = 10)
    private String actualEvalTime;

    /**
     * 是否评估完成
     */
    @ExcelProperty(index = 11)
    private String isEvalCompleted;

    /**
     * 不合规数量
     */
    @ExcelProperty(index = 12)
    private Integer nonComplianceCount;

    /**
     * 可能性等级
     */
    @ExcelProperty(index = 13)
    private String possibilityLevel;

    /**
     * 安全影响度
     */
    @ExcelProperty(index = 14)
    private String securityImpact;

    /**
     * 可能性等级
     */
    @ExcelProperty(index = 15)
    private String riskLevel;

    /**
     * 在报送工信部综合的企业鉴定整体报告中
     */
    @ExcelProperty(index = 16)
    private String inMiitReport;

    /**
     * 评估方式
     */
    @ExcelProperty(index = 17)
    private String evalMethod;

    /**
     * 评估机构
     */
    @ExcelProperty(index = 18)
    private String evalAgency;

    /**
     * 评估负责人
     */
    @ExcelProperty(index = 19)
    private String evalResponsible;

    /**
     * 备注
     */
    @ExcelProperty(index = 20)
    private String remark;

}
