package com.ruoyi.process.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.process.mapper.ProcessStepRollbackMapper;
import com.ruoyi.process.domain.ProcessStepRollback;
import com.ruoyi.process.service.IProcessStepRollbackService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 流程回退记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class ProcessStepRollbackServiceImpl extends ServiceImpl<ProcessStepRollbackMapper, ProcessStepRollback> implements IProcessStepRollbackService {

}
