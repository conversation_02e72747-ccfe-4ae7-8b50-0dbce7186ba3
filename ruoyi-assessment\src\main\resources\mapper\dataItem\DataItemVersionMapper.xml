<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataItem.mapper.DataItemVersionMapper">
    
    <resultMap type="DataItemVersion" id="DataItemVersionResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="versionCode"    column="version_code"    />
        <result property="versionDesc"    column="version_desc"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDataItemVersionVo">
        select id, org_id, version_code, version_desc, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_data_item_version
    </sql>

    <select id="selectDataItemVersionList" parameterType="DataItemVersion" resultMap="DataItemVersionResult">
        <include refid="selectDataItemVersionVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="versionCode != null  and versionCode != ''"> and version_code = #{versionCode}</if>
            <if test="versionDesc != null  and versionDesc != ''"> and version_desc = #{versionDesc}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDataItemVersionById" parameterType="Long" resultMap="DataItemVersionResult">
        <include refid="selectDataItemVersionVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataItemVersion" parameterType="DataItemVersion" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_data_item_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="versionCode != null and versionCode != ''">version_code,</if>
            <if test="versionDesc != null">version_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="versionCode != null and versionCode != ''">#{versionCode},</if>
            <if test="versionDesc != null">#{versionDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDataItemVersion" parameterType="DataItemVersion">
        update dsa_data_item_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="versionCode != null and versionCode != ''">version_code = #{versionCode},</if>
            <if test="versionDesc != null">version_desc = #{versionDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataItemVersionById" parameterType="Long">
        delete from dsa_data_item_version where id = #{id}
    </delete>

    <delete id="deleteDataItemVersionByIds" parameterType="String">
        delete from dsa_data_item_version where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>