#!/bin/bash

# 若依数据安全评估平台Docker部署便捷脚本
# 此脚本在项目根目录执行，会自动处理JAR包复制和部署操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
JAR_SOURCE="ruoyi-admin/target/ruoyi-admin.jar"
JAR_TARGET="deploy/ruoyi_admin.jar"
DEPLOY_DIR="deploy"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查是否在项目根目录
check_root_directory() {
    if [ ! -f "pom.xml" ] || [ ! -d "ruoyi-admin" ] || [ ! -d "deploy" ]; then
        print_message $RED "错误: 请在项目根目录下执行此脚本"
        print_message $YELLOW "项目根目录应包含: pom.xml, ruoyi-admin/, deploy/ 目录"
        exit 1
    fi
}

# 构建JAR包
build_jar() {
    print_message $BLUE "开始构建JAR包..."
    
    if ! command -v mvn &> /dev/null; then
        print_message $RED "错误: Maven未安装，请先安装Maven"
        exit 1
    fi
    
    mvn clean package -DskipTests
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "JAR包构建成功"
    else
        print_message $RED "JAR包构建失败"
        exit 1
    fi
}

# 复制JAR包到部署目录
copy_jar() {
    print_message $BLUE "复制JAR包到部署目录..."
    
    if [ ! -f "$JAR_SOURCE" ]; then
        print_message $RED "错误: 源JAR包不存在: $JAR_SOURCE"
        print_message $YELLOW "请先构建项目: mvn clean package -DskipTests"
        exit 1
    fi
    
    cp "$JAR_SOURCE" "$JAR_TARGET"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "JAR包复制成功: $JAR_TARGET"
    else
        print_message $RED "JAR包复制失败"
        exit 1
    fi
}

# 执行部署命令
execute_deploy_command() {
    local command=$1
    shift
    local args="$@"
    
    print_message $BLUE "切换到部署目录: $DEPLOY_DIR"
    cd "$DEPLOY_DIR"
    
    print_message $BLUE "执行部署命令: $command $args"
    chmod +x deploy.sh
    ./deploy.sh "$command" $args
}

# 显示帮助信息
show_help() {
    echo "若依数据安全评估平台Docker部署便捷脚本"
    echo ""
    echo "此脚本在项目根目录执行，提供以下功能："
    echo "1. 自动构建JAR包"
    echo "2. 复制JAR包到部署目录"
    echo "3. 执行Docker部署命令"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  build-and-start  构建JAR包并启动服务"
    echo "  build-jar        仅构建JAR包"
    echo "  copy-jar         仅复制JAR包到部署目录"
    echo "  start            启动服务（需要JAR包已存在）"
    echo "  stop             停止服务"
    echo "  restart          重启服务"
    echo "  status           查看服务状态"
    echo "  logs [服务]      查看日志"
    echo "  clean            清理所有数据"
    echo "  help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build-and-start  # 构建并启动（推荐）"
    echo "  $0 build-jar        # 仅构建JAR包"
    echo "  $0 status           # 查看服务状态"
    echo "  $0 logs ruoyi-app   # 查看应用日志"
    echo ""
    echo "注意:"
    echo "  - 请在项目根目录下执行此脚本"
    echo "  - 首次部署建议使用 build-and-start 命令"
}

# 主函数
main() {
    check_root_directory
    
    case "$1" in
        "build-and-start")
            build_jar
            copy_jar
            execute_deploy_command "start"
            ;;
        "build-jar")
            build_jar
            copy_jar
            ;;
        "copy-jar")
            copy_jar
            ;;
        "start")
            copy_jar  # 确保JAR包是最新的
            execute_deploy_command "start"
            ;;
        "stop")
            execute_deploy_command "stop"
            ;;
        "restart")
            execute_deploy_command "restart"
            ;;
        "status")
            execute_deploy_command "status"
            ;;
        "logs")
            execute_deploy_command "logs" "$2"
            ;;
        "clean")
            execute_deploy_command "clean"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            if [ -z "$1" ]; then
                show_help
            else
                print_message $RED "错误: 未知命令 '$1'"
                echo ""
                show_help
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
