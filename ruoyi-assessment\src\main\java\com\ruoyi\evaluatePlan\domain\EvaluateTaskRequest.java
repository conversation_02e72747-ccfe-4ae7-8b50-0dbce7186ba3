package com.ruoyi.evaluatePlan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

/**
 * 评估任务内容执行请求对象
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class EvaluateTaskRequest {

    /** 计划ID */
    @NotNull(message = "计划ID不能为空")
    private Long planId;

    /** 评估类型编码 */
    @NotBlank(message = "评估类型不能为空")
    private String evaluateType;

    /** 任务类型编码 */
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    /** 任务标题 */
    private String taskTitle;

    /** 任务描述 */
    private String taskDescription;

    /** 被评估单位ID */
    private Long targetCompanyId;

    /** 评估单位ID */
    private Long evaluateCompanyId;

    /** 任务内容参数 */
    private Map<String, Object> taskParams;

    /** 任务配置参数 */
    private Map<String, Object> taskConfig;

    /** 执行模式 sync-同步 async-异步 */
    private String executeMode = "sync";

    /** 优先级 1-低 2-中 3-高 */
    private Integer priority = 2;

    /** 超时时间（秒） */
    private Integer timeout;

    /** 重试次数 */
    private Integer retryCount = 0;

    /** 依赖任务列表 */
    private String[] dependentTasks;

    /** 扩展参数 */
    private Map<String, Object> extParams;

    /** 操作人 */
    private String operator;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 备注 */
    private String remark;
}
