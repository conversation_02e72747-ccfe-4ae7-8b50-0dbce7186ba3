package com.ruoyi.process.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.service.IProcessInstanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程实例Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/process/instance")
@Api(value = "流程实例控制器", tags = {"流程实例管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessInstanceController extends BaseController
{
    private final IProcessInstanceService processInstanceService;

    /**
     * 查询流程实例列表
     */
    @ApiOperation("查询流程实例列表")
    @PreAuthorize("@ss.hasPermi('process:instance:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessInstance processInstance) {
        startPage();
        List<ProcessInstance> list = processInstanceService.list(new QueryWrapper<ProcessInstance>(processInstance));
        return getDataTable(list);
    }

    /**
     * 获取流程实例详细信息
     */
    @ApiOperation("获取流程实例详细信息")
    @PreAuthorize("@ss.hasPermi('process:instance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processInstanceService.getById(id));
    }

    /**
     * 新增流程实例
     */
    @ApiOperation("新增流程实例")
    @PreAuthorize("@ss.hasPermi('process:instance:add')")
    @Log(title = "流程实例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessInstance processInstance) {
        return toAjax(processInstanceService.save(processInstance));
    }

    /**
     * 修改流程实例
     */
    @ApiOperation("修改流程实例")
    @PreAuthorize("@ss.hasPermi('process:instance:edit')")
    @Log(title = "流程实例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessInstance processInstance) {
        return toAjax(processInstanceService.updateById(processInstance));
    }

    /**
     * 删除流程实例
     */
    @ApiOperation("删除流程实例")
    @PreAuthorize("@ss.hasPermi('process:instance:remove')")
    @Log(title = "流程实例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processInstanceService.removeByIds(Arrays.asList(ids)));
    }
}