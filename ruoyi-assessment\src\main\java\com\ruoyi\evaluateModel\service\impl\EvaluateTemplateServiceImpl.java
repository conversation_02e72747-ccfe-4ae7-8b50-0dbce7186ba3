package com.ruoyi.evaluateModel.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.Date;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.ConfigFileUploadUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.evaluateModel.mapper.EvaluateTemplateMapper;
import com.ruoyi.evaluateModel.domain.EvaluateTemplate;
import com.ruoyi.evaluateModel.service.IEvaluateTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 评估报告模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Service
public class EvaluateTemplateServiceImpl extends ServiceImpl<EvaluateTemplateMapper, EvaluateTemplate> implements IEvaluateTemplateService {

    /**
     * 复制评估报告模板
     *
     * @param srcTemplateId 源模板ID
     * @param newTemplate   新模板信息
     * @return 复制结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyTemplate(Long srcTemplateId, EvaluateTemplate newTemplate) {
        if (srcTemplateId == null) {
            throw new ServiceException("请选择源模板");
        }

        try {
            // 1. 获取源模板信息
            EvaluateTemplate srcTemplate = this.getById(srcTemplateId);
            if (srcTemplate == null) {
                throw new ServiceException("源模板不存在");
            }

            log.info("开始复制评估报告模板，源模板ID: {}, 源文件路径: {}", srcTemplateId, srcTemplate.getTemplateFile());

            // 2. 复制模板文件
            String newTemplateFilePath = copyTemplateFile(srcTemplate.getTemplateFile());
            log.info("模板文件复制成功，新文件路径: {}", newTemplateFilePath);

            // 3. 创建新的模板记录
            EvaluateTemplate copyTemplate = new EvaluateTemplate();
            BeanUtils.copyProperties(srcTemplate, copyTemplate);

            // 重置关键字段
            copyTemplate.setId(null);
            copyTemplate.setModelId(srcTemplate.getModelId());
            copyTemplate.setTemplateFile(newTemplateFilePath);
            copyTemplate.setCreateTime(new Date());
            copyTemplate.setCreateBy(SecurityUtils.getUsername());
            copyTemplate.setRemark(newTemplate.getRemark());
            copyTemplate.setUpdateTime(null);
            copyTemplate.setUpdateBy(null);

            // 4. 保存新模板记录
            boolean result = this.save(copyTemplate);

            if (result) {
                log.info("评估报告模板复制成功，源模板ID: {}, 新模板ID: {}", srcTemplateId, copyTemplate.getId());
            } else {
                log.error("评估报告模板数据库记录保存失败");
                // 如果数据库保存失败，删除已复制的文件
                deleteTemplateFile(newTemplateFilePath);
            }

            return result;
        } catch (Exception e) {
            log.error("复制评估报告模板失败，源模板ID: {}", srcTemplateId, e);
            throw new ServiceException("复制评估报告模板失败: " + e.getMessage());
        }
    }

    /**
     * 复制模板文件
     *
     * @param srcFilePath 源文件路径
     * @return 新文件路径
     */
    private String copyTemplateFile(String srcFilePath) throws IOException {
        if (StringUtils.isEmpty(srcFilePath)) {
            throw new ServiceException("源模板文件路径为空");
        }

        // 获取源文件的绝对路径
        String configPath = RuoYiConfig.getConfigPath();
        String srcAbsolutePath = configPath + StringUtils.substringAfter(srcFilePath, "/config");

        File srcFile = new File(srcAbsolutePath);
        if (!srcFile.exists()) {
            throw new ServiceException("源模板文件不存在: " + srcAbsolutePath);
        }

        // 生成新文件名和路径
        String fileExtension = FilenameUtils.getExtension(srcFile.getName());
        String newFileName = IdUtils.fastSimpleUUID() + "." + fileExtension;
        String datePath = DateUtils.datePath();
        String newRelativePath = "template/report/" + datePath + "/" + newFileName;
        log.info("newRelativePath: {}", newRelativePath);

        // 创建新文件的绝对路径
        File newFile = ConfigFileUploadUtils.getAbsoluteFile(configPath, newRelativePath);
        log.info("newFile: {}", newFile.getAbsolutePath());

        // 复制文件
        FileUtils.copyFile(srcFile, newFile);

        // 返回新文件的相对路径（用于存储到数据库）
        String newFilePath = ConfigFileUploadUtils.getPathFileName(configPath, newRelativePath);
        log.info("newFilePath: {}", newFilePath);
        return newFilePath;
    }

    /**
     * 删除模板文件
     *
     * @param filePath 文件路径
     */
    private void deleteTemplateFile(String filePath) {
        try {
            if (StringUtils.isNotEmpty(filePath)) {
                String configPath = RuoYiConfig.getConfigPath();
                String absolutePath = configPath + StringUtils.substringAfter(filePath, "/config");
                File file = new File(absolutePath);
                if (file.exists()) {
                    boolean deleted = file.delete();
                    log.info("删除模板文件: {}, 结果: {}", absolutePath, deleted);
                }
            }
        } catch (Exception e) {
            log.warn("删除模板文件失败: {}", filePath, e);
        }
    }
}
