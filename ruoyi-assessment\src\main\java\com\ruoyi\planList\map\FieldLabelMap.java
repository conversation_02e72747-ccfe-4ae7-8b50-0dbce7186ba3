package com.ruoyi.planList.map;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class FieldLabelMap {
    private final Map<String, Map<String, String>> fieldLabelToValue = new HashMap<>();
    private final Map<String, Map<String, String>> fieldValueToLabel = new HashMap<>();

    public FieldLabelMap() {
        // isEvalCompleted
        Map<String, String> isEvalCompletedMap = new HashMap<>();
        isEvalCompletedMap.put("已完成", "1");
        isEvalCompletedMap.put("未完成", "0");
        fieldLabelToValue.put("isEvalCompleted", isEvalCompletedMap);

        // riskLevel
        Map<String, String> riskLevelMap = new HashMap<>();
        riskLevelMap.put("是", "1");
        riskLevelMap.put("否", "0");
        riskLevelMap.put("1", "1");
        riskLevelMap.put("0", "0");
        fieldLabelToValue.put("nonComplianceCount", riskLevelMap);

        // inMiitReport
        Map<String, String> inMiitReportMap = new HashMap<>();
        inMiitReportMap.put("是", "1");
        inMiitReportMap.put("否", "0");
        inMiitReportMap.put("1", "1");
        inMiitReportMap.put("0", "0");
        fieldLabelToValue.put("inMiitReport", inMiitReportMap);

        // 反向映射
        fieldLabelToValue.forEach((field, map) -> {
            Map<String, String> reverse = new HashMap<>();
            map.forEach((k, v) -> reverse.put(v, k));
            fieldValueToLabel.put(field, reverse);
        });
    }

    public Map<String, Map<String, String>> getFieldLabelToValue() {
        return fieldLabelToValue;
    }

    public Map<String, Map<String, String>> getFieldValueToLabel() {
        return fieldValueToLabel;
    }
}
