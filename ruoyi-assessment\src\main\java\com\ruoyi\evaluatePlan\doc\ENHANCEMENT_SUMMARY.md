# 评估计划控制器增强总结

## 📋 需求分析

**原始需求**：在某种类型的某个评估计划任务下执行不同的任务内容

**现有架构不足**：
1. 只能执行完整的评估流程，无法单独执行某个具体任务
2. 无法为同一评估类型的不同任务传递不同的任务内容参数
3. 无法在同一评估计划下执行不同类型的任务内容
4. 缺少步骤级别的精细化控制

## 🛠️ 解决方案

### 1. 新增请求对象

#### EvaluateTaskRequest - 任务内容执行请求
```java
@Data
@Accessors(chain = true)
public class EvaluateTaskRequest {
    private Long planId;                    // 计划ID
    private String evaluateType;            // 评估类型编码
    private String taskType;                // 任务类型编码
    private String taskTitle;               // 任务标题
    private Map<String, Object> taskParams; // 任务内容参数
    private Map<String, Object> taskConfig; // 任务配置参数
    private String executeMode;             // 执行模式 sync/async
    // ... 其他字段
}
```

#### EvaluateStepRequest - 步骤任务执行请求
```java
@Data
@Accessors(chain = true)
public class EvaluateStepRequest {
    private Long planId;                    // 计划ID
    private String evaluateType;            // 评估类型编码
    private String stepCode;                // 步骤编码
    private Long processInstanceId;         // 流程实例ID
    private Map<String, Object> stepParams; // 步骤参数
    private String executeMode;             // 执行模式 manual/auto
    // ... 其他字段
}
```

### 2. 扩展控制器功能

#### 新增API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|----------|
| `/evaluatePlan/executeTask` | POST | 执行指定任务内容 |
| `/evaluatePlan/executeStep` | POST | 执行指定步骤任务 |
| `/evaluatePlan/taskList` | GET | 获取任务内容列表 |
| `/evaluatePlan/stepStatus` | GET | 获取步骤任务状态 |

#### 控制器增强代码
```java
@RestController
@RequestMapping("/evaluatePlan")
public class EvaluatePlanController extends BaseController {
    
    /**
     * 执行指定任务内容
     */
    @PostMapping("/executeTask")
    public AjaxResult executeTask(@Validated @RequestBody EvaluateTaskRequest taskRequest) {
        EvaluatePlanResponse response = evaluatePlanDispatcher.executeTask(taskRequest);
        return response.getSuccess() ? AjaxResult.success("任务内容执行成功", response) 
                                     : AjaxResult.error(response.getMessage());
    }
    
    /**
     * 执行指定步骤任务
     */
    @PostMapping("/executeStep")
    public AjaxResult executeStep(@Validated @RequestBody EvaluateStepRequest stepRequest) {
        EvaluatePlanResponse response = evaluatePlanDispatcher.executeStep(stepRequest);
        return response.getSuccess() ? AjaxResult.success("步骤任务执行成功", response) 
                                     : AjaxResult.error(response.getMessage());
    }
    
    // ... 其他新增方法
}
```

### 3. 扩展分发器功能

#### EvaluatePlanDispatcher 增强
```java
@Component
public class EvaluatePlanDispatcher {
    
    /**
     * 执行指定任务内容
     */
    public EvaluatePlanResponse executeTask(EvaluateTaskRequest taskRequest) {
        EvaluateStrategy strategy = getStrategy(taskRequest.getEvaluateType());
        EvaluatePlanRequest planRequest = buildPlanRequestFromTask(taskRequest);
        EvaluatePlanResponse response = strategy.executeEvaluate(planRequest);
        
        // 设置任务相关信息
        response.getExtData().put("taskType", taskRequest.getTaskType());
        response.getExtData().put("taskParams", taskRequest.getTaskParams());
        
        return response;
    }
    
    /**
     * 执行指定步骤任务
     */
    public EvaluatePlanResponse executeStep(EvaluateStepRequest stepRequest) {
        EvaluateStrategy strategy = getStrategy(stepRequest.getEvaluateType());
        
        if (strategy instanceof ProcessAwareEvaluateStrategy) {
            ProcessAwareEvaluateStrategy processStrategy = (ProcessAwareEvaluateStrategy) strategy;
            EvaluatePlanRequest planRequest = buildPlanRequestFromStep(stepRequest);
            return processStrategy.executeStep(planRequest, stepRequest.getStepCode(), 
                                             stepRequest.getProcessInstanceId());
        } else {
            throw new ServiceException("该评估类型不支持步骤级别的任务执行");
        }
    }
    
    // ... 其他新增方法
}
```

### 4. 新增流程感知策略

#### ProcessAwareEvaluateStrategy 接口
```java
public interface ProcessAwareEvaluateStrategy extends EvaluateStrategy {
    
    /**
     * 执行指定步骤
     */
    EvaluatePlanResponse executeStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId);
    
    /**
     * 验证步骤执行条件
     */
    boolean validateStepCondition(EvaluatePlanRequest request, String stepCode, Long processInstanceId);
    
    /**
     * 获取步骤执行结果
     */
    Object getStepResult(String stepCode, Long processInstanceId);
    
    /**
     * 处理步骤回退
     */
    boolean handleStepRollback(EvaluatePlanRequest request, String fromStepCode, 
                              String toStepCode, Long processInstanceId, String reason);
    
    // ... 其他方法
}
```

#### EnhancedDataSecurityPlanStrategy 实现
```java
@Component
public class EnhancedDataSecurityPlanStrategy implements ProcessAwareEvaluateStrategy {
    
    private final Map<String, Map<String, Object>> stepResultCache = new HashMap<>();
    
    @Override
    public EvaluatePlanResponse executeStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        // 验证步骤执行条件
        if (!validateStepCondition(request, stepCode, processInstanceId)) {
            throw new ServiceException("步骤执行条件不满足: " + stepCode);
        }
        
        // 执行具体步骤
        Map<String, Object> stepResult = executeSpecificStep(request, stepCode, processInstanceId);
        
        // 缓存步骤结果
        String cacheKey = processInstanceId + "_" + stepCode;
        stepResultCache.put(cacheKey, stepResult);
        
        // 构建响应
        return EvaluatePlanResponse.success(request.getPlanId(), "步骤执行成功: " + stepCode)
                .setProgress(calculateStepProgress(stepCode))
                .setResultData(stepResult);
    }
    
    // ... 其他实现方法
}
```

## 🎯 实现效果

### 1. 任务内容参数化执行

**之前**：只能执行固定的评估流程
```java
// 只能这样调用
EvaluatePlanRequest request = new EvaluatePlanRequest()
    .setEvaluateType("data_security_plan")
    .setPlanId(1001L);
EvaluatePlanResponse response = dispatcher.dispatch(request);
```

**现在**：可以执行不同的任务内容
```java
// 执行数据收集任务
EvaluateTaskRequest dataTask = new EvaluateTaskRequest()
    .setEvaluateType("data_security_plan")
    .setTaskType("data_collection")
    .setTaskParams(Map.of("dataScope", "all", "includeBackup", true));
EvaluatePlanResponse response1 = dispatcher.executeTask(dataTask);

// 执行风险分析任务
EvaluateTaskRequest riskTask = new EvaluateTaskRequest()
    .setEvaluateType("data_security_plan")
    .setTaskType("risk_analysis")
    .setTaskParams(Map.of("analysisLevel", "detailed", "includeThirdParty", true));
EvaluatePlanResponse response2 = dispatcher.executeTask(riskTask);
```

### 2. 步骤级别精细控制

**之前**：无法单独执行某个步骤
```java
// 无法实现
```

**现在**：可以执行特定步骤
```java
// 执行风险识别步骤
EvaluateStepRequest stepRequest = new EvaluateStepRequest()
    .setEvaluateType("data_security_plan")
    .setStepCode("risk_identify")
    .setProcessInstanceId(2001L)
    .setStepParams(Map.of("analysisDepth", "detailed"));
EvaluatePlanResponse response = dispatcher.executeStep(stepRequest);
```

### 3. 任务状态查询

**之前**：只能查询整体进度
```java
Integer progress = dispatcher.getProgress("data_security_plan", 1001L);
```

**现在**：可以查询详细的任务和步骤状态
```java
// 获取可用任务列表
Map<String, Object> taskList = dispatcher.getAvailableTasks("data_security_plan", 1001L);

// 获取步骤状态
Map<String, Object> stepStatus = dispatcher.getStepStatus("data_security_plan", 1001L, "risk_identify");
```

## 📊 架构对比

### 修改前架构
```
Controller -> Dispatcher -> Strategy -> executeEvaluate()
                                    -> generateReport()
                                    -> getProgress()
```

### 修改后架构
```
Controller -> Dispatcher -> Strategy -> executeEvaluate()
           -> executeTask()         -> generateReport()
           -> executeStep()         -> getProgress()
           -> getTaskList()         -> executeStep() (ProcessAware)
           -> getStepStatus()       -> validateStepCondition()
                                   -> handleStepRollback()
                                   -> getStepDependencies()
```

## 🔧 扩展能力

### 1. 新增任务类型
只需在策略实现中添加新的任务处理逻辑：
```java
private Map<String, Object> executeSpecificTask(String taskType, Map<String, Object> taskParams) {
    switch (taskType) {
        case "new_task_type":
            return handleNewTaskType(taskParams);
        // ... 其他任务类型
    }
}
```

### 2. 新增步骤
只需在ProcessAwareEvaluateStrategy实现中添加：
```java
private Map<String, Object> executeSpecificStep(String stepCode, ...) {
    switch (stepCode) {
        case "new_step_code":
            return handleNewStep(...);
        // ... 其他步骤
    }
}
```

## ✅ 总结

通过本次增强，评估计划控制器现在完全满足了"在某种类型的某个评估计划任务下执行不同的任务内容"的需求：

1. **✅ 任务内容参数化**：支持为同一评估类型传递不同的任务参数
2. **✅ 任务类型区分**：支持在同一评估计划下执行不同类型的任务内容
3. **✅ 步骤级别控制**：支持单独执行评估流程中的特定步骤
4. **✅ 流程管理**：支持步骤依赖、回退、跳过等高级功能
5. **✅ 状态查询**：支持查询任务列表和步骤状态
6. **✅ 扩展性**：架构支持轻松添加新的任务类型和步骤

现有的基础功能保持不变，新增功能完全向后兼容。
