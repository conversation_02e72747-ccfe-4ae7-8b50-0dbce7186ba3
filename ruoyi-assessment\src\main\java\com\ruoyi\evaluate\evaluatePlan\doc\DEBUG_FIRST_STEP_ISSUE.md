# 调试第一个步骤实例为null的问题

## 问题描述
在`markFirstStepAsCompleted`方法中，`firstStepInstance == null`一直成立，导致无法标记第一个步骤为完成。

## 可能的原因分析

### 1. 时序问题
流程实例和步骤实例的创建可能存在时序问题：
- `createProcessControlData`创建了流程实例和步骤实例
- `markFirstStepAsCompleted`立即查询，但数据可能还未提交到数据库

### 2. 事务问题
- 创建和查询可能在不同的事务中
- 数据库事务隔离级别导致查询不到刚创建的数据

### 3. 数据问题
- 步骤实例的ID字段为null
- 步骤实例列表为空
- 流程定义中没有步骤定义

## 调试步骤

### 1. 检查日志输出
运行代码后，查看以下关键日志：

```
[INFO] 开始标记第一个步骤任务为完成，任务ID: xxx, 操作人: xxx
[INFO] 找到流程实例，ID: xxx, 业务ID: xxx
[INFO] 获取步骤实例结果，流程实例ID: xxx, 步骤实例数量: xxx
[INFO] 步骤实例[0]: ID=xxx, 名称=xxx, 状态=xxx
```

### 2. 数据库检查
执行以下SQL查询，检查数据是否正确创建：

```sql
-- 检查流程实例
SELECT * FROM dsa_process_instance WHERE business_id = [你的任务ID];

-- 检查步骤实例
SELECT psi.* FROM dsa_process_step_instance psi
JOIN dsa_process_instance pi ON psi.process_instance_id = pi.id
WHERE pi.business_id = [你的任务ID]
ORDER BY psi.id;

-- 检查流程定义和步骤定义
SELECT pd.*, psd.* FROM dsa_process_definition pd
LEFT JOIN dsa_process_step_definition psd ON pd.id = psd.process_definition_id
WHERE pd.process_code = 'data_security_plan';
```

### 3. 添加临时调试代码
在`markFirstStepAsCompleted`方法中添加更多调试信息：

```java
private void markFirstStepAsCompleted(Long planTaskId, String operator) {
    try {
        log.info("=== 开始调试第一个步骤标记问题 ===");
        log.info("任务ID: {}, 操作人: {}", planTaskId, operator);
        
        // 检查流程实例
        ProcessInstance processInstance = getProcessInstanceWithRetry(planTaskId, 3);
        if (processInstance == null) {
            log.error("❌ 流程实例为null，任务ID: {}", planTaskId);
            return;
        }
        log.info("✅ 流程实例存在，ID: {}", processInstance.getId());
        
        // 检查步骤实例
        List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstance.getId());
        log.info("步骤实例查询结果: {}", stepInstances != null ? "非null" : "null");
        log.info("步骤实例数量: {}", stepInstances != null ? stepInstances.size() : 0);
        
        if (stepInstances != null && !stepInstances.isEmpty()) {
            for (int i = 0; i < stepInstances.size(); i++) {
                ProcessStepInstance step = stepInstances.get(i);
                log.info("步骤[{}]: ID={}, 名称={}, 状态={}, ID是否为null={}", 
                        i, step.getId(), step.getStepName(), step.getStatus(), step.getId() == null);
            }
            
            // 检查stream操作
            long validStepCount = stepInstances.stream()
                    .filter(step -> step.getId() != null)
                    .count();
            log.info("有效步骤数量（ID不为null）: {}", validStepCount);
            
            if (validStepCount > 0) {
                ProcessStepInstance minStep = stepInstances.stream()
                        .filter(step -> step.getId() != null)
                        .min((s1, s2) -> Long.compare(s1.getId(), s2.getId()))
                        .orElse(null);
                log.info("最小ID步骤: {}", minStep != null ? minStep.getId() : "null");
            }
        }
        
        log.info("=== 调试信息结束 ===");
        
        // 原有逻辑...
        
    } catch (Exception e) {
        log.error("调试过程中发生异常: {}", e.getMessage(), e);
    }
}
```

## 解决方案

### 方案1：增加延迟和重试
已经实现了`getProcessInstanceWithRetry`方法，可以进一步增加对步骤实例的重试：

```java
private List<ProcessStepInstance> getStepInstancesWithRetry(Long processInstanceId, int maxRetries) {
    List<ProcessStepInstance> stepInstances = null;
    int retryCount = 0;
    
    while (retryCount <= maxRetries) {
        stepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);
        if (stepInstances != null && !stepInstances.isEmpty()) {
            // 检查是否有有效的步骤实例（ID不为null）
            long validCount = stepInstances.stream()
                    .filter(step -> step.getId() != null)
                    .count();
            if (validCount > 0) {
                break;
            }
        }
        
        if (retryCount < maxRetries) {
            try {
                Thread.sleep(200); // 增加等待时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        retryCount++;
    }
    
    return stepInstances;
}
```

### 方案2：异步处理
将标记第一个步骤完成的操作改为异步执行：

```java
@Async
public void markFirstStepAsCompletedAsync(Long planTaskId, String operator) {
    // 异步执行，避免事务问题
    markFirstStepAsCompleted(planTaskId, operator);
}
```

### 方案3：事务后处理
使用Spring的`@TransactionalEventListener`在事务提交后执行：

```java
@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
public void handleTaskCreated(TaskCreatedEvent event) {
    markFirstStepAsCompleted(event.getTaskId(), event.getOperator());
}
```

## 临时解决方案

如果问题持续存在，可以临时使用以下方案：

### 1. 手动SQL更新
```java
private void markFirstStepAsCompletedBySql(Long planTaskId, String operator) {
    try {
        String sql = """
            UPDATE dsa_process_step_instance psi
            SET psi.status = ?, psi.end_time = NOW(), psi.operator = ?, psi.remark = ?
            WHERE psi.id = (
                SELECT MIN(psi2.id) FROM dsa_process_step_instance psi2
                JOIN dsa_process_instance pi ON psi2.process_instance_id = pi.id
                WHERE pi.business_id = ?
            )
            """;
        
        // 执行SQL更新
        // jdbcTemplate.update(sql, ProcessStepStatusEnum.FINISHED.getCode(), operator, "系统自动完成第一个步骤", planTaskId);
        
    } catch (Exception e) {
        log.error("SQL方式标记第一个步骤完成失败: {}", e.getMessage(), e);
    }
}
```

### 2. 延迟执行
```java
// 在createPlanTask方法中
// 延迟执行标记操作
CompletableFuture.runAsync(() -> {
    try {
        Thread.sleep(1000); // 等待1秒
        markFirstStepAsCompleted(evaluatePlanTask.getId(), evaluatePlanTask.getCreateBy());
    } catch (Exception e) {
        log.error("延迟标记第一个步骤失败: {}", e.getMessage(), e);
    }
});
```

## 建议的调试顺序

1. **先运行增强的调试代码**，查看详细日志
2. **检查数据库**，确认数据是否正确创建
3. **如果数据存在但查询不到**，考虑事务问题
4. **如果数据不存在**，检查流程定义和创建逻辑
5. **根据具体情况选择合适的解决方案**
