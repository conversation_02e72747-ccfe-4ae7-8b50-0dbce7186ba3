package com.ruoyi.dataItem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;


/**
 * 数据项基本信息对象 dsa_data_item_base
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_item_base")
public class DataItemBase extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属单位ID
     */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 数据项ID
     */
    @Excel(name = "数据项ID")
    @TableField(value = "data_item_id")
    private Long dataItemId;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 职务/职称
     */
    @Excel(name = "职务/职称")
    @TableField(value = "contact_position")
    private String contactPosition;

    /**
     * 所属部门
     */
    @Excel(name = "所属部门")
    @TableField(value = "org_dept")
    private String orgDept;

    /**
     * 移动电话
     */
    @Excel(name = "移动电话")
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 电子邮件
     */
    @Excel(name = "电子邮件")
    @TableField(value = "email")
    private String email;

    /**
     * 数据流转图
     */
    @Excel(name = "数据流转图")
    @TableField(value = "data_flow_img")
    private String dataFlowImg;

    /**
     * 评估环境图
     */
    @Excel(name = "评估环境图")
    @TableField(value = "assessment_img")
    private String assessmentImg;

    /**
     * 状态，1-正常 0-禁用
     */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;
}