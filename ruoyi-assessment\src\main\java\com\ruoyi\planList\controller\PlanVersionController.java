package com.ruoyi.planList.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.planList.domain.PlanVersion;
import com.ruoyi.planList.service.IPlanVersionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.planList.service.IPlanListService;
import com.ruoyi.common.exception.ServiceException;

import java.util.Map;

/**
 * 评估计划清单版本Controller
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/version/version")
@Api(value = "评估计划清单版本控制器", tags = {"评估计划清单版本管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class PlanVersionController extends BaseController
{
    private final IPlanVersionService planVersionService;
    private final IPlanListService planListService;

    /**
     * 查询评估计划清单版本列表
     */
    @ApiOperation("查询评估计划清单版本列表")
    @PreAuthorize("@ss.hasPermi('version:version:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanVersion planVersion) {
        if (planVersion.getOrgId() == null) {
            throw new ServiceException("请选择所属单位");
        }
        startPage();
        List<PlanVersion> list = planVersionService.list(new QueryWrapper<PlanVersion>(planVersion));
        // 查询每个versionId下PlanList数量
        List<Map<String, Object>> countList = planListService.countPlanListGroupByVersionId();
        java.util.Map<Long, Integer> countMap = countList.stream()
            .collect(java.util.stream.Collectors.toMap(
                m -> ((Number)m.get("version_id")).longValue(),
                m -> ((Number)m.get("count")).intValue()
            ));
        for (PlanVersion version : list) {
            version.setDataItemNum(countMap.getOrDefault(version.getId(), 0));
        }
        return getDataTable(list);
    }

}