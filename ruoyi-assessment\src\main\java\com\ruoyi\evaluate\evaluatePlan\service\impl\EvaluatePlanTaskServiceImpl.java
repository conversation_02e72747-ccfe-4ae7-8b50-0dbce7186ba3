package com.ruoyi.evaluate.evaluatePlan.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.domain.EvaluateType;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.ruoyi.evaluateModel.service.IEvaluateTypeService;
import com.ruoyi.evaluatePlan.dispatcher.EvaluatePlanDispatcher;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.strategy.EvaluateStrategy;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.enums.ProcessStepStatusEnum;
import com.ruoyi.process.enums.ProcessTypeEnum;
import com.ruoyi.process.service.IProcessFlowService;
import com.ruoyi.process.service.IProcessStepInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import java.lang.reflect.Method;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluatePlan.mapper.EvaluatePlanTaskMapper;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 评估计划任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class EvaluatePlanTaskServiceImpl extends ServiceImpl<EvaluatePlanTaskMapper, EvaluatePlanTask> implements IEvaluatePlanTaskService {

    @Autowired
    private IEvaluateModelService evaluateModelService;

    @Autowired
    private IEvaluateTypeService evaluateTypeService;

    @Autowired
    private EvaluatePlanDispatcher evaluatePlanDispatcher;

    @Autowired
    private IProcessFlowService processFlowService;

    @Autowired
    private IProcessStepInstanceService processStepInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPlanTask(EvaluatePlanTaskDto evaluatePlanTaskDto) {
        try {
            // 1. 参数校验
            validatePlanTaskDto(evaluatePlanTaskDto);

            // 2. 构建评估计划任务实体
            EvaluatePlanTask evaluatePlanTask = buildEvaluatePlanTask(evaluatePlanTaskDto);
            String evaluateType = evaluatePlanTask.getEvaluateType();

            // 3. 验证评估策略（非阻塞）
            validateEvaluateStrategy(evaluateType, evaluatePlanTask);

            // 4. 保存任务
            saveEvaluatePlanTask(evaluatePlanTask, evaluateType);

            // 5. 创建流程控制数据
            createProcessControlData(evaluatePlanTask, evaluateType);

            // 6. 标记第一个步骤任务为完成
            finishFirstStep(evaluatePlanTask.getId(), evaluatePlanTask.getCreateBy(), evaluatePlanTaskDto);

            return evaluatePlanTask.getId();

        } catch (ServiceException e) {
            log.error("创建评估计划任务失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            throw handleUnexpectedException(evaluatePlanTaskDto, e);
        }
    }

    /**
     * 参数校验
     */
    private void validatePlanTaskDto(EvaluatePlanTaskDto evaluatePlanTaskDto) {
        if (evaluatePlanTaskDto == null) {
            throw new ServiceException("评估计划任务数据不能为空");
        }
    }

    /**
     * 构建评估计划任务实体
     */
    private EvaluatePlanTask buildEvaluatePlanTask(EvaluatePlanTaskDto evaluatePlanTaskDto) {
        EvaluatePlanTask evaluatePlanTask = new EvaluatePlanTask();
        BeanUtils.copyProperties(evaluatePlanTaskDto, evaluatePlanTask);
        log.info("开始保存评估计划任务，modelId: {}", evaluatePlanTask.getModelId());

        // 根据modelId设置evaluateType
        if (evaluatePlanTask.getModelId() != null) {
            String evaluateType = getEvaluateTypeByModelId(evaluatePlanTask.getModelId());
            if (StringUtils.isNotEmpty(evaluateType)) {
                evaluatePlanTask.setEvaluateType(evaluateType);
                log.info("根据modelId: {} 设置evaluateType: {}", evaluatePlanTask.getModelId(), evaluateType);
            } else {
                log.warn("根据modelId: {} 未找到对应的evaluateType", evaluatePlanTask.getModelId());
            }
        }

        return evaluatePlanTask;
    }

    /**
     * 验证评估策略（非阻塞）
     */
    private void validateEvaluateStrategy(String evaluateType, EvaluatePlanTask evaluatePlanTask) {
        if (StringUtils.isEmpty(evaluateType)) {
            return;
        }

        try {
            // 构建评估请求用于验证
            EvaluatePlanRequest validateRequest = new EvaluatePlanRequest()
                    .setEvaluateType(evaluateType)
                    .setPlanId(evaluatePlanTask.getId())
                    .setTitle(evaluatePlanTask.getName())
                    .setDescription(evaluatePlanTask.getTaskDescription())
                    .setTargetCompanyId(evaluatePlanTask.getOrgId())
                    .setEvaluateCompanyId(evaluatePlanTask.getEvaluateOrgId())
                    .setModelId(evaluatePlanTask.getModelId());

            // 获取对应策略进行验证
            EvaluateStrategy strategy = getEvaluateStrategy(evaluateType);
            if (strategy != null) {
                // 验证请求参数
                if (!strategy.validateRequest(validateRequest)) {
                    log.warn("评估计划任务参数验证失败，evaluateType: {}", evaluateType);
                    throw new ServiceException("评估计划任务参数验证失败");
                }
                log.info("评估计划任务参数验证通过，策略: {}", strategy.getStrategyName());
            } else {
                log.warn("未找到支持的评估策略，evaluateType: {}", evaluateType);
            }
        } catch (ServiceException e) {
            // 如果是不支持的评估类型，记录警告但不阻止保存
            log.warn("评估类型验证异常，evaluateType: {}, 异常: {}", evaluateType, e.getMessage());
        } catch (Exception e) {
            log.error("流程控制验证异常，evaluateType: {}, 异常: {}", evaluateType, e.getMessage(), e);
        }
    }

    /**
     * 保存评估计划任务
     */
    private void saveEvaluatePlanTask(EvaluatePlanTask evaluatePlanTask, String evaluateType) {
        boolean saveResult = this.save(evaluatePlanTask);

        if (saveResult) {
            log.info("评估计划任务保存成功，任务ID: {}, evaluateType: {}",
                    evaluatePlanTask.getId(), evaluateType);
        } else {
            log.error("评估计划任务保存失败，modelId: {}, evaluateType: {}, 任务名称: {}, 报告编号: {}, 创建人: {}",
                    evaluatePlanTask.getModelId(), evaluateType, evaluatePlanTask.getName(),
                    evaluatePlanTask.getReportNo(), evaluatePlanTask.getCreateBy());

            String errorMsg = buildContextErrorMessage("评估计划任务保存失败", evaluatePlanTask, evaluateType, "数据库保存操作失败");
            throw new ServiceException(errorMsg);
        }
    }

    /**
     * 创建流程控制数据
     */
    private void createProcessControlData(EvaluatePlanTask evaluatePlanTask, String evaluateType) {
        if (StringUtils.isEmpty(evaluateType)) {
            return;
        }

        try {
            // 启动流程实例
            Long processInstanceId = processFlowService.startProcessInstance(
                    ProcessTypeEnum.EVALUATE.getCode(),
                    evaluateType,
                    evaluatePlanTask.getId(),
                    evaluatePlanTask.getCreateBy());

            log.info("创建流程控制数据成功，任务ID: {}, 流程实例ID: {}, evaluateType: {}",
                    evaluatePlanTask.getId(), processInstanceId, evaluateType);

        } catch (Exception e) {
            log.error("创建流程控制数据异常，任务ID: {}, evaluateType: {}, modelId: {}, 任务名称: {}, 报告编号: {}, 创建人: {}, 异常: {}",
                    evaluatePlanTask.getId(), evaluateType, evaluatePlanTask.getModelId(),
                    evaluatePlanTask.getName(), evaluatePlanTask.getReportNo(),
                    evaluatePlanTask.getCreateBy(), e.getMessage(), e);

            String originalMessage = e instanceof ServiceException ? e.getMessage() : e.getMessage();
            String errorMsg = buildContextErrorMessage("创建流程控制数据失败", evaluatePlanTask, evaluateType, originalMessage);
            throw new ServiceException(errorMsg);
        }
    }

    /**
     * 处理未预期的异常
     */
    private ServiceException handleUnexpectedException(EvaluatePlanTaskDto evaluatePlanTaskDto, Exception e) {
        String modelId = evaluatePlanTaskDto != null ? String.valueOf(evaluatePlanTaskDto.getModelId()) : "未知";
        String taskName = evaluatePlanTaskDto != null ? evaluatePlanTaskDto.getName() : "未知";
        String reportNo = evaluatePlanTaskDto != null ? evaluatePlanTaskDto.getReportNo() : "未知";

        log.error("创建评估计划任务异常，modelId: {}, 任务名称: {}, 报告编号: {}, 异常: {}",
                modelId, taskName, reportNo, e.getMessage(), e);

        String errorMsg = String.format("创建评估计划任务失败 [模型ID: %s, 任务名称: %s, 报告编号: %s]: %s",
                modelId, taskName, reportNo, e.getMessage());
        return new ServiceException(errorMsg);
    }

    /**
     * 根据modelId查询evaluateType
     *
     * @param modelId 模型ID
     * @return evaluateType的processCode
     */
    private String getEvaluateTypeByModelId(Long modelId) {
        try {
            // 1. 根据modelId查询EvaluateModel获取typeId
            EvaluateModel evaluateModel = evaluateModelService.getById(modelId);
            if (evaluateModel == null) {
                log.warn("未找到modelId为 {} 的评估模型", modelId);
                return null;
            }

            Long typeId = evaluateModel.getTypeId();
            if (typeId == null) {
                log.warn("评估模型 {} 的typeId为空", modelId);
                return null;
            }

            // 2. 根据typeId查询EvaluateType获取processCode
            EvaluateType evaluateType = evaluateTypeService.getById(typeId);
            if (evaluateType == null) {
                log.warn("未找到typeId为 {} 的评估类型", typeId);
                return null;
            }

            return evaluateType.getProcessCode();
        } catch (Exception e) {
            log.error("根据modelId查询evaluateType异常，modelId: {}", modelId, e);
            return null;
        }
    }

    /**
     * 构建包含上下文信息的错误消息
     *
     * @param baseMessage      基础错误消息
     * @param evaluatePlanTask 评估计划任务对象
     * @param evaluateType     评估类型
     * @param originalMessage  原始异常消息
     * @return 包含上下文信息的错误消息
     */
    private String buildContextErrorMessage(String baseMessage, EvaluatePlanTask evaluatePlanTask,
                                            String evaluateType, String originalMessage) {
        return String.format("%s [任务ID: %s, 评估类型: %s, 模型ID: %s, 任务名称: %s, 报告编号: %s]: %s",
                baseMessage,
                evaluatePlanTask.getId(),
                evaluateType,
                evaluatePlanTask.getModelId(),
                evaluatePlanTask.getName(),
                evaluatePlanTask.getReportNo(),
                originalMessage);
    }

    /**
     * 获取评估策略
     *
     * @param evaluateType 评估类型
     * @return 评估策略实例
     */
    private EvaluateStrategy getEvaluateStrategy(String evaluateType) {
        try {
            if (StringUtils.isEmpty(evaluateType)) {
                return null;
            }

            // 通过分发器获取策略（利用反射访问私有方法）
            Method getStrategyMethod = evaluatePlanDispatcher.getClass()
                    .getDeclaredMethod("getStrategy", String.class);
            getStrategyMethod.setAccessible(true);
            return (EvaluateStrategy) getStrategyMethod.invoke(evaluatePlanDispatcher, evaluateType);

        } catch (ServiceException e) {
            // 不支持的评估类型
            log.warn("不支持的评估类型: {}", evaluateType);
            return null;
        } catch (Exception e) {
            log.error("获取评估策略异常，evaluateType: {}", evaluateType, e);
            return null;
        }
    }


    /**
     * 标记第一个步骤任务为完成
     *
     * @param planTaskId 评估计划任务ID
     * @param operator   操作人
     * @param evaluatePlanTaskDto 评估计划任务DTO（包含下一步配置信息）
     */
    @Transactional(rollbackFor = Exception.class)
    void finishFirstStep(Long planTaskId, String operator, EvaluatePlanTaskDto evaluatePlanTaskDto) {
        try {
            log.info("开始标记第一个步骤任务为完成，任务ID: {}, 操作人: {}", planTaskId, operator);

            // 1. 根据业务ID获取流程实例
            ProcessInstance processInstance = getProcessInstance(planTaskId);
            if (processInstance == null) {
                log.warn("未找到评估计划任务对应的流程实例，任务ID: {}", planTaskId);
                return;
            }

            // 2. 获取流程实例的所有步骤实例
            List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstance.getId());
            if (stepInstances == null || stepInstances.isEmpty()) {
                log.warn("未找到流程实例的步骤实例，流程实例ID: {}", processInstance.getId());
                return;
            }

            log.debug("获取到{}个步骤实例，流程实例ID: {}", stepInstances.size(), processInstance.getId());

            // 3. 找到第一个步骤实例（按ID排序，取第一个）
            ProcessStepInstance firstStepInstance = stepInstances.stream()
                    .filter(step -> step.getId() != null)
                    .min((s1, s2) -> Long.compare(s1.getId(), s2.getId()))
                    .orElse(null);

            if (firstStepInstance == null) {
                log.warn("未找到有效的第一个步骤实例，流程实例ID: {}", processInstance.getId());
                return;
            }

            // 4. 检查第一个步骤是否已经完成
            if (ProcessStepStatusEnum.FINISHED.getCode().equals(firstStepInstance.getStatus())) {
                log.info("第一个步骤已经完成，无需重复标记，步骤实例ID: {}", firstStepInstance.getId());
                return;
            }

            log.info("开始处理第一个步骤，ID: {}, 名称: {}", firstStepInstance.getId(), firstStepInstance.getStepName());

            // 5. 完成第一个步骤（包含开始和结束的完整流程）
            boolean firstStepResult = completeFirstStep(firstStepInstance, operator, planTaskId);

            if (firstStepResult) {
                // 6. 启动下一个步骤（复用已查询的步骤实例数据）
                startNextStep(stepInstances, operator, evaluatePlanTaskDto);
            }


        } catch (Exception e) {
            log.error("标记第一个步骤任务为完成异常，任务ID: {}, 操作人: {}, 异常: {}",
                    planTaskId, operator, e.getMessage(), e);
            throw new RuntimeException("完成第一步骤失败", e);
        }
    }

    /**
     * 完成第一个步骤的完整流程（包含时间设置）
     *
     * @param firstStepInstance 第一个步骤实例
     * @param operator 操作人
     * @param planTaskId 计划任务ID
     * @return 是否成功
     */
    private boolean completeFirstStep(ProcessStepInstance firstStepInstance, String operator, Long planTaskId) {
        try {
            Date now = new Date();

            // 一步到位：设置第一步的所有必要字段
            firstStepInstance.setAssignee(SecurityUtils.getUsername());
            firstStepInstance.setDeadlineDays(0);
            firstStepInstance.setSetTaskDeadline(0);
            firstStepInstance.setStartTime(now);
            firstStepInstance.setEndTime(now);
            firstStepInstance.setStatus(ProcessStepStatusEnum.FINISHED.getCode());
            firstStepInstance.setOperator(operator);
            firstStepInstance.setRemark("计划创建完毕，完成第一个步骤");
            firstStepInstance.setDurationMs(0L);
            firstStepInstance.setUpdateTime(now);

            // 一次性更新所有字段
            boolean result = processStepInstanceService.updateById(firstStepInstance);

            if (result) {
                log.info("成功完成第一个步骤，任务ID: {}, 步骤实例ID: {}, 步骤名称: {}, 负责人: {}, 开始时间: {}, 结束时间: {}",
                        planTaskId, firstStepInstance.getId(), firstStepInstance.getStepName(),
                        firstStepInstance.getAssignee(), firstStepInstance.getStartTime(), firstStepInstance.getEndTime());
            } else {
                log.warn("完成第一个步骤失败，任务ID: {}, 步骤实例ID: {}",
                        planTaskId, firstStepInstance.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("完成第一个步骤异常，步骤实例ID: {}, 异常: {}",
                    firstStepInstance.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 启动下一个步骤（重新查询最新数据以确保事务一致性）
     *
     * @param stepInstances 已查询的步骤实例列表（用于获取流程实例ID）
     * @param operator 操作人
     * @param evaluatePlanTaskDto 评估计划任务DTO（包含下一步配置信息）
     */
    private void startNextStep(List<ProcessStepInstance> stepInstances, String operator, EvaluatePlanTaskDto evaluatePlanTaskDto) {
        try {
            if (stepInstances == null || stepInstances.isEmpty()) {
                log.warn("步骤实例列表为空，无法启动下一步");
                return;
            }

            // 获取流程实例ID
            Long processInstanceId = stepInstances.get(0).getProcessInstanceId();

            // 重新查询最新的步骤实例数据（确保获取事务内的最新状态）
            List<ProcessStepInstance> latestStepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);
            if (latestStepInstances == null || latestStepInstances.isEmpty()) {
                log.warn("重新查询步骤实例为空，流程实例ID: {}", processInstanceId);
                return;
            }

            // 查找下一个待执行的步骤（使用最新数据）
            ProcessStepInstance nextStep = latestStepInstances.stream()
                    .filter(step -> ProcessStepStatusEnum.PENDING.getCode().equals(step.getStatus()))
                    .findFirst()
                    .orElse(null);

            if (nextStep == null) {
                log.info("未找到下一个待执行的步骤，流程实例ID: {}", processInstanceId);
                return;
            }

            log.info("找到下一个待执行的步骤，步骤实例ID: {}, 步骤名称: {}",
                    nextStep.getId(), nextStep.getStepName());

            // 设置下一步的开始时间和执行人
            nextStep.setStartTime(new Date());
            nextStep.setOperator(operator);
            nextStep.setStatus(ProcessStepStatusEnum.RUNNING.getCode());
            nextStep.setUpdateTime(new Date());

            // 设置下一步的assignee、deadlineDays、setTaskDeadline（从前端传递的值）
            if (evaluatePlanTaskDto != null) {
                nextStep.setAssignee(evaluatePlanTaskDto.getAssignee());
                nextStep.setDeadlineDays(evaluatePlanTaskDto.getDeadlineDays());
                nextStep.setSetTaskDeadline(evaluatePlanTaskDto.getSetTaskDeadline());

                log.info("设置下一步配置信息，负责人: {}, 截止天数: {}, 设置截止日期: {}",
                        evaluatePlanTaskDto.getAssignee(),
                        evaluatePlanTaskDto.getDeadlineDays(),
                        evaluatePlanTaskDto.getSetTaskDeadline());
            }

            // 更新下一步步骤实例
            boolean result = processStepInstanceService.updateById(nextStep);

            if (result) {
                log.info("成功启动下一个步骤，步骤实例ID: {}, 步骤名称: {}, 执行人: {}, 负责人: {}, 截止天数: {}, 设置截止日期: {}",
                        nextStep.getId(), nextStep.getStepName(), operator,
                        nextStep.getAssignee(), nextStep.getDeadlineDays(), nextStep.getSetTaskDeadline());
            } else {
                log.warn("启动下一个步骤失败，步骤实例ID: {}", nextStep.getId());
            }

        } catch (Exception e) {
            log.error("启动下一个步骤异常，操作人: {}, 异常: {}", operator, e.getMessage(), e);
        }
    }

    /**
     * 获取流程实例
     *
     * @param planTaskId 评估计划任务ID
     * @return 流程实例
     */
    private ProcessInstance getProcessInstance(Long planTaskId) {
        try {
            ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(planTaskId);
            if (processInstance != null) {
                log.info("成功获取流程实例，任务ID: {}, 流程实例ID: {}", planTaskId, processInstance.getId());
            } else {
                log.warn("未找到流程实例，任务ID: {}", planTaskId);
            }
            return processInstance;
        } catch (Exception e) {
            log.error("获取流程实例异常，任务ID: {}, 异常: {}", planTaskId, e.getMessage(), e);
            return null;
        }
    }



}
