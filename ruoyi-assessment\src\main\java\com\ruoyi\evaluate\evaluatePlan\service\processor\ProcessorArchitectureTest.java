package com.ruoyi.evaluate.evaluatePlan.service.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;

/**
 * 处理器架构测试类
 * <p>
 * 用于验证处理器架构是否正常工作，包括：
 * - 处理器注册状态
 * - 处理器匹配逻辑
 * - 包结构验证
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class ProcessorArchitectureTest {

    @Autowired
    private StepDataProcessorFactory processorFactory;

    @Autowired
    private ProcessorRegistry processorRegistry;

    @Autowired
    private ProcessorPackageManager packageManager;

    @PostConstruct
    public void testProcessorArchitecture() {
        log.info("=== 开始处理器架构测试 ===");
        
        testProcessorRegistration();
        testProcessorMatching();
        testSupportedTypes();
        
        log.info("=== 处理器架构测试完成 ===");
    }

    /**
     * 测试处理器注册状态
     */
    private void testProcessorRegistration() {
        log.info("--- 测试处理器注册状态 ---");

        Map<String, Object> status = processorRegistry.getRegistryStatus();
        log.info("注册表状态: {}", status);

        // 安全地获取数值
        int totalProcessors = safeGetInteger(status, "totalProcessors", 0);
        boolean hasDefaultProcessor = safeGetBoolean(status, "hasDefaultProcessor", false);

        log.info("总处理器数: {}", totalProcessors);
        log.info("是否有默认处理器: {}", hasDefaultProcessor);

        if (totalProcessors > 0) {
            log.info("✅ 处理器注册正常");
        } else {
            log.warn("❌ 未发现任何处理器");
        }
    }

    /**
     * 测试处理器匹配逻辑
     */
    private void testProcessorMatching() {
        log.info("--- 测试处理器匹配逻辑 ---");
        
        // 测试数据安全评估处理器匹配
        testProcessorMatch("data_security_plan", "create_plan", "数据安全-创建计划");
        testProcessorMatch("data_security_plan", "evaluate_scope", "数据安全-评估范围");
        testProcessorMatch("data_security_plan", "system_check", "数据安全-系统检查");
        
        // 测试基础信息评估处理器匹配
        testProcessorMatch("basic_info", "current_analysis", "基础信息-现状分析");
        
        // 测试不存在的处理器（应该返回默认处理器）
        testProcessorMatch("unknown_type", "unknown_step", "未知类型-未知步骤");
    }

    /**
     * 测试单个处理器匹配
     */
    private void testProcessorMatch(String evaluateType, String stepCode, String description) {
        try {
            IStepDataProcessor processor = processorFactory.getProcessor(evaluateType, stepCode);
            if (processor != null) {
                log.info("✅ {} - 找到处理器: {}", description, processor.getClass().getSimpleName());
            } else {
                log.warn("❌ {} - 未找到处理器", description);
            }
        } catch (Exception e) {
            log.error("❌ {} - 处理器匹配异常: {}", description, e.getMessage());
        }
    }

    /**
     * 测试支持的评估类型
     */
    private void testSupportedTypes() {
        log.info("--- 测试支持的评估类型 ---");
        
        Set<String> supportedTypes = processorFactory.getSupportedEvaluateTypes();
        log.info("支持的评估类型: {}", supportedTypes);
        
        for (String evaluateType : supportedTypes) {
            Set<String> supportedSteps = processorFactory.getSupportedStepCodes(evaluateType);
            log.info("评估类型 {} 支持的步骤: {}", evaluateType, supportedSteps);
        }
        
        if (!supportedTypes.isEmpty()) {
            log.info("✅ 评估类型支持正常");
        } else {
            log.warn("❌ 未发现支持的评估类型");
        }
    }

    /**
     * 手动触发架构测试（用于调试）
     */
    public void runManualTest() {
        log.info("=== 手动触发处理器架构测试 ===");
        testProcessorArchitecture();
    }

    /**
     * 获取架构状态报告
     */
    public String getArchitectureReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 处理器架构状态报告 ===\n\n");

        // 注册表状态
        Map<String, Object> status = processorRegistry.getRegistryStatus();
        report.append("📊 注册表状态:\n");
        report.append(String.format("  总处理器数: %s\n", status.get("totalProcessors")));
        report.append(String.format("  特定处理器: %s\n", status.get("specificProcessors")));
        report.append(String.format("  通用处理器: %s\n", status.get("commonProcessors")));

        // 安全地获取 boolean 值
        boolean hasDefault = safeGetBoolean(status, "hasDefaultProcessor", false);
        report.append(String.format("  默认处理器: %s\n", hasDefault ? "已配置" : "未配置"));
        report.append("\n");

        // 支持的评估类型
        Set<String> supportedTypes = processorFactory.getSupportedEvaluateTypes();
        report.append("🎯 支持的评估类型:\n");
        for (String type : supportedTypes) {
            Set<String> steps = processorFactory.getSupportedStepCodes(type);
            report.append(String.format("  %s: %s\n", type, steps));
        }

        return report.toString();
    }

    /**
     * 安全地从Map中获取Integer值
     */
    private int safeGetInteger(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    /**
     * 安全地从Map中获取Boolean值
     */
    private boolean safeGetBoolean(Map<String, Object> map, String key, boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }
}
