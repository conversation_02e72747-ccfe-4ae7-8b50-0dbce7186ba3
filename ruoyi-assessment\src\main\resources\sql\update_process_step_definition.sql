-- 更新dsa_process_step_definition表结构，添加任务配置相关字段
-- 执行时间：2025-07-28
-- 作者：Mario

-- 添加任务类型编码字段
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `task_type` varchar(64) DEFAULT NULL COMMENT '任务类型编码' AFTER `description`;

-- 添加任务配置参数字段（JSON格式）
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `task_config` text DEFAULT NULL COMMENT '任务配置参数（JSON格式）' AFTER `task_type`;

-- 添加任务执行器类名字段
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `task_executor` varchar(255) DEFAULT NULL COMMENT '任务执行器类名' AFTER `task_config`;

-- 添加预估执行时间字段（分钟）
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `estimated_duration` int(11) DEFAULT 30 COMMENT '预估执行时间（分钟）' AFTER `task_executor`;

-- 添加任务优先级字段（1-低，2-中，3-高）
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `task_priority` int(1) DEFAULT 2 COMMENT '任务优先级（1-低，2-中，3-高）' AFTER `estimated_duration`;

-- 添加是否可跳过字段（0-否，1-是）
ALTER TABLE `dsa_process_step_definition` 
ADD COLUMN `skippable` int(1) DEFAULT 0 COMMENT '是否可跳过（0-否，1-是）' AFTER `task_priority`;

-- 为新字段添加索引
ALTER TABLE `dsa_process_step_definition` 
ADD INDEX `idx_task_type` (`task_type`);

ALTER TABLE `dsa_process_step_definition` 
ADD INDEX `idx_task_priority` (`task_priority`);

-- 更新现有数据，为数据安全评估流程添加任务配置
-- 首先查找数据安全评估流程的ID
SET @process_id = (SELECT id FROM dsa_process_definition WHERE code = 'data_security_plan' LIMIT 1);

-- 更新数据收集步骤
UPDATE `dsa_process_step_definition` 
SET 
    `task_type` = 'data_collection',
    `task_config` = '{"dataScope": "all", "includeBackup": false, "timeRange": "current"}',
    `task_executor` = 'com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager',
    `estimated_duration` = 30,
    `task_priority` = 2,
    `skippable` = 0
WHERE `process_id` = @process_id AND `step_code` = 'data_identify';

-- 更新风险分析步骤
UPDATE `dsa_process_step_definition` 
SET 
    `task_type` = 'risk_analysis',
    `task_config` = '{"analysisDepth": "standard", "includeThirdParty": false, "riskCategories": ["访问控制", "数据泄露", "系统漏洞"]}',
    `task_executor` = 'com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager',
    `estimated_duration` = 45,
    `task_priority` = 3,
    `skippable` = 0
WHERE `process_id` = @process_id AND `step_code` = 'risk_identify';

-- 更新控制措施建议步骤
UPDATE `dsa_process_step_definition` 
SET 
    `task_type` = 'control_measures',
    `task_config` = '{"measureTypes": ["技术措施", "管理措施", "物理措施"], "priorityLevel": "medium", "implementationPlan": "分阶段实施"}',
    `task_executor` = 'com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager',
    `estimated_duration` = 40,
    `task_priority` = 2,
    `skippable` = 0
WHERE `process_id` = @process_id AND `step_code` = 'control_design';

-- 更新报告生成步骤
UPDATE `dsa_process_step_definition` 
SET 
    `task_type` = 'report_generation',
    `task_config` = '{"reportFormat": "pdf", "includeCharts": true, "detailLevel": "standard"}',
    `task_executor` = 'com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager',
    `estimated_duration` = 15,
    `task_priority` = 1,
    `skippable` = 0
WHERE `process_id` = @process_id AND `step_code` = 'report_generate';

-- 验证更新结果
SELECT 
    id,
    step_name,
    step_code,
    task_type,
    task_config,
    task_executor,
    estimated_duration,
    task_priority,
    skippable
FROM `dsa_process_step_definition` 
WHERE `process_id` = @process_id 
ORDER BY `step_order`;

-- 创建任务配置视图，方便查询
CREATE OR REPLACE VIEW `v_task_config` AS
SELECT 
    pd.code AS process_code,
    pd.name AS process_name,
    psd.step_code,
    psd.step_name,
    psd.task_type,
    psd.task_config,
    psd.task_executor,
    psd.estimated_duration,
    psd.task_priority,
    psd.skippable,
    psd.handler_type,
    psd.description,
    psd.step_order,
    psd.status
FROM `dsa_process_definition` pd
JOIN `dsa_process_step_definition` psd ON pd.id = psd.process_id
WHERE pd.status = 1 AND psd.status = 1 AND psd.task_type IS NOT NULL
ORDER BY pd.code, psd.step_order;

-- 添加表注释
ALTER TABLE `dsa_process_step_definition` 
COMMENT = '流程步骤定义表，支持任务配置管理';

COMMIT;
