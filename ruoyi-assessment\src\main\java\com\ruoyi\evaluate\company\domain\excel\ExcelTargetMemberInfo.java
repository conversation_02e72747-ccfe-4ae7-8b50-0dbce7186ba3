package com.ruoyi.evaluate.company.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 被评估单位人员信息Excel导入对象 - Sheet3
 * 数据从第4行开始读取（跳过表头和示例数据）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ExcelTargetMemberInfo {

    /** 序号 - A列 */
    @ExcelProperty(index = 0)
    private String serialNumber;

    /** 人员姓名 - B列 */
    @ExcelProperty(index = 1)
    private String name;

    /** 岗位或角色名称 - C列 */
    @ExcelProperty(index = 2)
    private String post;

    /** 岗位职责 - D列 */
    @ExcelProperty(index = 3)
    private String duty;

    /** 所属部门 - E列 */
    @ExcelProperty(index = 4)
    private String department;

    /** 涉及的数据处理活动 - F列 */
    @ExcelProperty(index = 5)
    private String dataProcessing;

    /** 是否专职 - G列 */
    @ExcelProperty(index = 6)
    private String fullTime;

    /** 国籍 - H列 */
    @ExcelProperty(index = 7)
    private String nationality;
}
