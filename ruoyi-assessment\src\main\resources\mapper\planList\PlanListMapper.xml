<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.planList.mapper.PlanListMapper">
    
    <resultMap type="PlanList" id="PlanListResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="dataItemName"    column="data_item_name"    />
        <result property="dataType"    column="data_type"    />
        <result property="dataProcessingActivity"    column="data_processing_activity"    />
        <result property="dataLevel"    column="data_level"    />
        <result property="infoSystemName"    column="info_system_name"    />
        <result property="importantSystemCategory"    column="important_system_category"    />
        <result property="businessLine"    column="business_line"    />
        <result property="plannedEvalTime"    column="planned_eval_time"    />
        <result property="actualEvalTime"    column="actual_eval_time"    />
        <result property="isEvalCompleted"    column="is_eval_completed"    />
        <result property="nonComplianceCount"    column="non_compliance_count"    />
        <result property="possibilityLevel"    column="possibility_level"    />
        <result property="securityImpact"    column="security_impact"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="inMiitReport"    column="in_miit_report"    />
        <result property="evalAgency"    column="eval_agency"    />
        <result property="evalMethod"    column="eval_method"    />
        <result property="evalResponsible"    column="eval_responsible"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPlanListVo">
        select id, org_id, org_name, data_item_name, data_type, data_processing_activity, data_level, info_system_name, important_system_category, business_line, planned_eval_time, actual_eval_time, is_eval_completed, non_compliance_count, possibility_level, security_impact, risk_level, in_miit_report, eval_agency, eval_method, eval_responsible, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_plan_list
    </sql>

    <select id="selectPlanListList" parameterType="PlanList" resultMap="PlanListResult">
        <include refid="selectPlanListVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="dataItemName != null  and dataItemName != ''"> and data_item_name like concat('%', #{dataItemName}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="dataProcessingActivity != null  and dataProcessingActivity != ''"> and data_processing_activity = #{dataProcessingActivity}</if>
            <if test="dataLevel != null  and dataLevel != ''"> and data_level = #{dataLevel}</if>
            <if test="infoSystemName != null  and infoSystemName != ''"> and info_system_name like concat('%', #{infoSystemName}, '%')</if>
            <if test="importantSystemCategory != null  and importantSystemCategory != ''"> and important_system_category = #{importantSystemCategory}</if>
            <if test="businessLine != null  and businessLine != ''"> and business_line = #{businessLine}</if>
            <if test="plannedEvalTime != null "> and planned_eval_time = #{plannedEvalTime}</if>
            <if test="actualEvalTime != null "> and actual_eval_time = #{actualEvalTime}</if>
            <if test="isEvalCompleted != null "> and is_eval_completed = #{isEvalCompleted}</if>
            <if test="nonComplianceCount != null "> and non_compliance_count = #{nonComplianceCount}</if>
            <if test="possibilityLevel != null  and possibilityLevel != ''"> and possibility_level = #{possibilityLevel}</if>
            <if test="securityImpact != null  and securityImpact != ''"> and security_impact = #{securityImpact}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="inMiitReport != null "> and in_miit_report = #{inMiitReport}</if>
            <if test="evalAgency != null  and evalAgency != ''"> and eval_agency = #{evalAgency}</if>
            <if test="evalMethod != null  and evalMethod != ''"> and eval_method = #{evalMethod}</if>
            <if test="evalResponsible != null  and evalResponsible != ''"> and eval_responsible = #{evalResponsible}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPlanListById" parameterType="Long" resultMap="PlanListResult">
        <include refid="selectPlanListVo"/>
        where id = #{id}
    </select>

    <insert id="insertPlanList" parameterType="PlanList" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_plan_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="dataItemName != null">data_item_name,</if>
            <if test="dataType != null">data_type,</if>
            <if test="dataProcessingActivity != null">data_processing_activity,</if>
            <if test="dataLevel != null">data_level,</if>
            <if test="infoSystemName != null">info_system_name,</if>
            <if test="importantSystemCategory != null">important_system_category,</if>
            <if test="businessLine != null">business_line,</if>
            <if test="plannedEvalTime != null">planned_eval_time,</if>
            <if test="actualEvalTime != null">actual_eval_time,</if>
            <if test="isEvalCompleted != null">is_eval_completed,</if>
            <if test="nonComplianceCount != null">non_compliance_count,</if>
            <if test="possibilityLevel != null">possibility_level,</if>
            <if test="securityImpact != null">security_impact,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="inMiitReport != null">in_miit_report,</if>
            <if test="evalAgency != null">eval_agency,</if>
            <if test="evalMethod != null">eval_method,</if>
            <if test="evalResponsible != null">eval_responsible,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="dataItemName != null">#{dataItemName},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="dataProcessingActivity != null">#{dataProcessingActivity},</if>
            <if test="dataLevel != null">#{dataLevel},</if>
            <if test="infoSystemName != null">#{infoSystemName},</if>
            <if test="importantSystemCategory != null">#{importantSystemCategory},</if>
            <if test="businessLine != null">#{businessLine},</if>
            <if test="plannedEvalTime != null">#{plannedEvalTime},</if>
            <if test="actualEvalTime != null">#{actualEvalTime},</if>
            <if test="isEvalCompleted != null">#{isEvalCompleted},</if>
            <if test="nonComplianceCount != null">#{nonComplianceCount},</if>
            <if test="possibilityLevel != null">#{possibilityLevel},</if>
            <if test="securityImpact != null">#{securityImpact},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="inMiitReport != null">#{inMiitReport},</if>
            <if test="evalAgency != null">#{evalAgency},</if>
            <if test="evalMethod != null">#{evalMethod},</if>
            <if test="evalResponsible != null">#{evalResponsible},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePlanList" parameterType="PlanList">
        update dsa_plan_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="dataItemName != null">data_item_name = #{dataItemName},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="dataProcessingActivity != null">data_processing_activity = #{dataProcessingActivity},</if>
            <if test="dataLevel != null">data_level = #{dataLevel},</if>
            <if test="infoSystemName != null">info_system_name = #{infoSystemName},</if>
            <if test="importantSystemCategory != null">important_system_category = #{importantSystemCategory},</if>
            <if test="businessLine != null">business_line = #{businessLine},</if>
            <if test="plannedEvalTime != null">planned_eval_time = #{plannedEvalTime},</if>
            <if test="actualEvalTime != null">actual_eval_time = #{actualEvalTime},</if>
            <if test="isEvalCompleted != null">is_eval_completed = #{isEvalCompleted},</if>
            <if test="nonComplianceCount != null">non_compliance_count = #{nonComplianceCount},</if>
            <if test="possibilityLevel != null">possibility_level = #{possibilityLevel},</if>
            <if test="securityImpact != null">security_impact = #{securityImpact},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="inMiitReport != null">in_miit_report = #{inMiitReport},</if>
            <if test="evalAgency != null">eval_agency = #{evalAgency},</if>
            <if test="evalMethod != null">eval_method = #{evalMethod},</if>
            <if test="evalResponsible != null">eval_responsible = #{evalResponsible},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanListById" parameterType="Long">
        delete from dsa_plan_list where id = #{id}
    </delete>

    <delete id="deletePlanListByIds" parameterType="String">
        delete from dsa_plan_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>