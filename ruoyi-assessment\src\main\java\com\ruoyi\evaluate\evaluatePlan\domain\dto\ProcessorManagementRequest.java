package com.ruoyi.evaluate.evaluatePlan.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 处理器管理请求DTO
 * <p>
 * 用于处理器的手动注册、批量操作等请求
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "处理器管理请求")
public class ProcessorManagementRequest {

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型", required = true, 
        allowableValues = "REGISTER,UNREGISTER,REFRESH,TEST,BATCH_REGISTER")
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

    /**
     * 处理器类名
     */
    @ApiModelProperty(value = "处理器类名")
    private String processorClassName;

    /**
     * 评估类型
     */
    @ApiModelProperty(value = "评估类型")
    private String evaluateType;

    /**
     * 步骤编码
     */
    @ApiModelProperty(value = "步骤编码")
    private String stepCode;

    /**
     * 处理器描述
     */
    @ApiModelProperty(value = "处理器描述")
    private String description;

    /**
     * 是否强制注册（覆盖已存在的处理器）
     */
    @ApiModelProperty(value = "是否强制注册", example = "false")
    private Boolean forceRegister = false;

    /**
     * 批量处理器信息
     */
    @ApiModelProperty(value = "批量处理器信息")
    private List<ProcessorInfo> processors;

    /**
     * 测试参数
     */
    @ApiModelProperty(value = "测试参数")
    private Map<String, Object> testParams;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 处理器信息内部类
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "处理器信息")
    public static class ProcessorInfo {
        
        @ApiModelProperty(value = "处理器类名", required = true)
        @NotBlank(message = "处理器类名不能为空")
        private String className;
        
        @ApiModelProperty(value = "评估类型")
        private String evaluateType;
        
        @ApiModelProperty(value = "步骤编码")
        private String stepCode;
        
        @ApiModelProperty(value = "处理器描述")
        private String description;
        
        @ApiModelProperty(value = "优先级", example = "0")
        private Integer priority = 0;
        
        @ApiModelProperty(value = "是否启用", example = "true")
        private Boolean enabled = true;
    }

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        /**
         * 注册处理器
         */
        REGISTER,
        
        /**
         * 注销处理器
         */
        UNREGISTER,
        
        /**
         * 刷新注册表
         */
        REFRESH,
        
        /**
         * 测试处理器
         */
        TEST,
        
        /**
         * 批量注册
         */
        BATCH_REGISTER
    }
}
