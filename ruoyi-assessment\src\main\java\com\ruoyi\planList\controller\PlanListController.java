package com.ruoyi.planList.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.planList.domain.PlanList;
import com.ruoyi.planList.service.IPlanListService;
import com.ruoyi.common.core.page.TableDataInfo;

import java.io.IOException;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.exception.ServiceException;

/**
 * 评估计划清单Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/plan/planList")
@Api(value = "评估计划清单控制器", tags = {"评估计划清单管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class PlanListController extends BaseController {
    @Autowired
    private final IPlanListService planListService;

    /**
     * 查询评估计划清单列表
     */
    @ApiOperation("查询评估计划清单列表")
    @PreAuthorize("@ss.hasPermi('plan:planList:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanList planList) {
        if (planList.getOrgId() == null) {
            throw new ServiceException("所属单位不能为空");
        }
        startPage();
        List<PlanList> list = planListService.list(new QueryWrapper<PlanList>(planList));
        return getDataTable(list);
    }

    /**
     * 导出评估计划清单列表
     */
    @ApiOperation("导出评估计划清单列表")
    @PreAuthorize("@ss.hasPermi('plan:planList:export')")
    @Log(title = "评估计划清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanList planList) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/plan_list/导出-评估计划清单模板.xlsx";
        planListService.exportPlanListWithTemplate(response, planList, templatePath);
    }

    /**
     * 获取评估计划清单详细信息
     */
    @ApiOperation("获取评估计划清单详细信息")
    @PreAuthorize("@ss.hasPermi('plan:planList:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(planListService.getById(id));
    }

    /**
     * 新增评估计划清单
     */
    @ApiOperation("新增评估计划清单")
    @PreAuthorize("@ss.hasPermi('plan:planList:add')")
    @Log(title = "评估计划清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanList planList) {
        return toAjax(planListService.save(planList));
    }

    /**
     * 修改评估计划清单
     */
    @ApiOperation("修改评估计划清单")
    @PreAuthorize("@ss.hasPermi('plan:planList:edit')")
    @Log(title = "评估计划清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanList planList) {
        return toAjax(planListService.updateById(planList));
    }

    /**
     * 删除评估计划清单
     */
    @ApiOperation("删除评估计划清单")
    @PreAuthorize("@ss.hasPermi('plan:planList:remove')")
    @Log(title = "评估计划清单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(planListService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取评估计划清单Excel模板
     *
     * @param response
     */
    @ApiOperation("获取评估计划清单Excel模板，文件流形式")
    @GetMapping("/getExcelTemplate")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/plan_list/评估计划清单模板.xlsx";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "评估计划清单模板.xlsx");
    }

    @ApiOperation("导入评估计划清单Excel")
    @PreAuthorize("@ss.hasPermi('plan:planList:import')")
    @PostMapping("/import")
    public AjaxResult importPlanList(@RequestParam("file") MultipartFile file, @RequestParam("orgId") Long orgId) throws IOException {
        Map<String, Object> result = planListService.importPlanList(file, orgId);
        return AjaxResult.success(result);
    }
}