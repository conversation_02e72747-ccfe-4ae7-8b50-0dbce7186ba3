# 评估计划步骤数据控制器使用指南

## 概述

`EvaluatePlanStepDataController` 是一个专门用于获取评估计划中不同步骤数据的控制器。每个步骤的处理逻辑和数据结构都不一样，该控制器支持步骤处理逻辑的拆分和单独处理。

## 核心特性

### 1. 步骤数据获取
- 支持获取指定步骤的详细数据
- 每个步骤有独立的数据结构和处理逻辑
- 支持流程实例级别的数据隔离

### 2. 数据结构定义
- 提供步骤数据结构的Schema定义
- 支持不同评估类型的步骤差异化处理
- 便于前端动态构建表单和展示界面

### 3. 状态和统计
- 实时获取步骤数据处理状态
- 提供详细的统计信息和进度跟踪
- 支持数据质量和完整性检查

## API接口说明

### 1. 获取步骤数据
```http
GET /evaluatePlan/stepData/{planId}/{stepCode}?processInstanceId={processInstanceId}
```

**参数说明：**
- `planId`: 评估计划ID（必填）
- `stepCode`: 步骤编码（必填）
- `processInstanceId`: 流程实例ID（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取步骤数据成功",
  "data": {
    "stepCode": "data_asset_identify",
    "stepName": "数据资产识别",
    "planId": 1001,
    "planName": "某公司数据安全评估",
    "evaluateType": "data_security_plan",
    "processInstanceId": 2001,
    "assetList": [
      {
        "assetId": "ASSET_001",
        "assetName": "用户数据库",
        "assetType": "数据库",
        "location": "服务器A",
        "owner": "张三",
        "description": "存储用户基本信息的数据库",
        "identifyTime": "2025-07-30T10:30:00"
      }
    ],
    "statistics": {
      "totalAssets": 150,
      "assetsByType": {
        "数据库": 80,
        "文件系统": 45,
        "应用数据": 25
      }
    }
  }
}
```

### 2. 获取步骤数据概览
```http
GET /evaluatePlan/stepData/overview/{planId}?processInstanceId={processInstanceId}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取步骤数据概览成功",
  "data": {
    "planId": 1001,
    "planName": "某公司数据安全评估",
    "evaluateType": "data_security_plan",
    "processInstanceId": 2001,
    "totalSteps": 6,
    "completedSteps": 3,
    "runningSteps": 1,
    "stepDetails": [
      {
        "stepInstanceId": 3001,
        "stepName": "数据资产识别",
        "status": 2,
        "statusName": "已完成",
        "startTime": "2025-07-30T09:00:00",
        "endTime": "2025-07-30T11:30:00",
        "operator": "张三"
      }
    ]
  }
}
```

### 3. 获取步骤数据结构定义
```http
GET /evaluatePlan/stepData/schema/{stepCode}?evaluateType={evaluateType}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取步骤数据结构成功",
  "data": {
    "stepCode": "data_asset_identify",
    "stepName": "数据资产识别",
    "description": "识别和梳理组织内所有数据资产，建立数据资产清单",
    "dataStructure": {
      "assetList": {
        "assetId": "资产ID",
        "assetName": "资产名称",
        "assetType": "资产类型（数据库、文件系统、应用数据等）",
        "location": "存储位置",
        "owner": "数据负责人",
        "description": "资产描述",
        "identifyTime": "识别时间"
      },
      "statistics": {
        "totalAssets": "资产总数",
        "assetsByType": "按类型分组的资产数量",
        "assetsByOwner": "按负责人分组的资产数量",
        "identifyProgress": "识别进度百分比"
      }
    }
  }
}
```

### 4. 获取步骤数据状态
```http
GET /evaluatePlan/stepData/status/{planId}/{stepCode}?processInstanceId={processInstanceId}
```

### 5. 获取步骤数据统计
```http
GET /evaluatePlan/stepData/statistics/{planId}/{stepCode}?processInstanceId={processInstanceId}
```

### 6. 获取支持的步骤类型
```http
GET /evaluatePlan/stepData/types?evaluateType={evaluateType}
```

## 支持的步骤类型

### 1. 数据资产识别 (data_asset_identify)
- **数据结构**: 资产清单、分类统计、识别进度
- **特色功能**: 资产分类、负责人统计、质量检查
- **适用场景**: 数据安全评估的第一步

### 2. 风险识别分析 (risk_identify)
- **数据结构**: 风险清单、威胁分析、脆弱性评估
- **特色功能**: 风险矩阵、威胁建模、影响分析
- **适用场景**: 安全风险评估的核心步骤

### 3. 数据分类分级 (data_classify)
- **数据结构**: 分类规则、分级结果、敏感度标记
- **特色功能**: 自动分类、人工审核、标签管理
- **适用场景**: 数据治理和保护策略制定

### 4. 风险等级评定 (risk_assessment)
- **数据结构**: 评定结果、等级分布、影响评估
- **特色功能**: 量化评估、等级矩阵、趋势分析
- **适用场景**: 风险优先级排序和资源分配

### 5. 控制措施建议 (control_measures)
- **数据结构**: 措施清单、实施计划、成本评估
- **特色功能**: 措施匹配、优先级排序、效果预测
- **适用场景**: 安全控制措施的制定和实施

### 6. 报告生成 (report_generate)
- **数据结构**: 报告模板、内容组织、格式配置
- **特色功能**: 自动生成、模板定制、多格式输出
- **适用场景**: 评估结果的汇总和展示

## 扩展指南

### 1. 创建新的步骤处理器

```java
@Component
public class CustomStepProcessor extends AbstractStepDataProcessor {
    
    @Override
    public String getStepCode() {
        return "custom_step";
    }
    
    @Override
    public String getEvaluateType() {
        return "custom_evaluate_type"; // 或返回null支持所有类型
    }
    
    @Override
    protected String getStepName() {
        return "自定义步骤";
    }
    
    @Override
    protected Map<String, Object> processSpecificStepData(
            EvaluatePlanTask planTask, 
            ProcessStepInstance stepInstance, 
            Long processInstanceId) {
        // 实现具体的数据处理逻辑
        Map<String, Object> data = new HashMap<>();
        // ... 处理逻辑
        return data;
    }
    
    // 实现其他抽象方法...
}
```

### 2. 数据结构设计原则

1. **一致性**: 相同类型的数据使用统一的字段名和格式
2. **扩展性**: 预留扩展字段，支持未来功能增强
3. **可读性**: 使用清晰的字段名和合理的数据层次
4. **性能**: 避免过深的嵌套和冗余数据

### 3. 错误处理

所有接口都遵循统一的错误处理机制：

```json
{
  "code": 400,
  "msg": "获取步骤数据失败: 评估计划不存在",
  "data": null
}
```

## 最佳实践

1. **缓存策略**: 对于计算密集的步骤数据，建议实现缓存机制
2. **权限控制**: 确保用户只能访问有权限的评估计划数据
3. **数据验证**: 在处理器中实现数据完整性和有效性检查
4. **日志记录**: 记录关键操作和异常情况，便于问题排查
5. **性能监控**: 监控各步骤的处理时间，优化性能瓶颈

## 注意事项

1. **并发安全**: 多用户同时访问同一步骤数据时的并发控制
2. **数据一致性**: 确保步骤数据与流程状态的一致性
3. **版本兼容**: 步骤数据结构变更时的向后兼容性
4. **资源管理**: 大量数据处理时的内存和CPU资源管理
