package com.ruoyi.evaluateModel.service;

import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 评估模型服务测试类
 * 主要测试模型启用状态管理功能
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EvaluateModelServiceTest {

    @Autowired
    private IEvaluateModelService evaluateModelService;

    /**
     * 测试新增模型时的自动启用逻辑
     */
    @Test
    public void testSaveModelWithAutoEnable() {
        // 创建第一个模型
        EvaluateModel model1 = new EvaluateModel();
        model1.setTitle("测试模型1");
        model1.setTypeId(1L);
        model1.setStatus(1);
        
        boolean result1 = evaluateModelService.save(model1);
        assertTrue(result1, "第一个模型应该保存成功");
        assertEquals(Integer.valueOf(1), model1.getIsEnabled(), "第一个模型应该被自动启用");

        // 创建第二个相同类型的模型
        EvaluateModel model2 = new EvaluateModel();
        model2.setTitle("测试模型2");
        model2.setTypeId(1L);
        model2.setStatus(1);
        
        boolean result2 = evaluateModelService.save(model2);
        assertTrue(result2, "第二个模型应该保存成功");
        assertEquals(Integer.valueOf(1), model2.getIsEnabled(), "第二个模型应该被自动启用");

        // 验证第一个模型被自动禁用
        EvaluateModel updatedModel1 = evaluateModelService.getById(model1.getId());
        assertEquals(Integer.valueOf(0), updatedModel1.getIsEnabled(), "第一个模型应该被自动禁用");
    }

    /**
     * 测试手动启用模型的功能
     */
    @Test
    public void testEnableModel() {
        // 创建两个模型
        EvaluateModel model1 = new EvaluateModel();
        model1.setTitle("测试模型1");
        model1.setTypeId(2L);
        model1.setStatus(1);
        evaluateModelService.save(model1);

        EvaluateModel model2 = new EvaluateModel();
        model2.setTitle("测试模型2");
        model2.setTypeId(2L);
        model2.setStatus(1);
        evaluateModelService.save(model2);

        // 此时model2应该是启用的，model1应该是禁用的
        EvaluateModel currentModel1 = evaluateModelService.getById(model1.getId());
        EvaluateModel currentModel2 = evaluateModelService.getById(model2.getId());
        assertEquals(Integer.valueOf(0), currentModel1.getIsEnabled(), "模型1应该被禁用");
        assertEquals(Integer.valueOf(1), currentModel2.getIsEnabled(), "模型2应该被启用");

        // 手动启用model1
        boolean enableResult = evaluateModelService.enableModel(model1.getId());
        assertTrue(enableResult, "启用模型1应该成功");

        // 验证状态变更
        EvaluateModel updatedModel1 = evaluateModelService.getById(model1.getId());
        EvaluateModel updatedModel2 = evaluateModelService.getById(model2.getId());
        assertEquals(Integer.valueOf(1), updatedModel1.getIsEnabled(), "模型1应该被启用");
        assertEquals(Integer.valueOf(0), updatedModel2.getIsEnabled(), "模型2应该被禁用");
    }

    /**
     * 测试不同类型模型之间不互相影响
     */
    @Test
    public void testDifferentTypeModelsIndependent() {
        // 创建不同类型的模型
        EvaluateModel model1 = new EvaluateModel();
        model1.setTitle("类型1模型");
        model1.setTypeId(3L);
        model1.setStatus(1);
        evaluateModelService.save(model1);

        EvaluateModel model2 = new EvaluateModel();
        model2.setTitle("类型2模型");
        model2.setTypeId(4L);
        model2.setStatus(1);
        evaluateModelService.save(model2);

        // 两个不同类型的模型都应该被启用
        EvaluateModel savedModel1 = evaluateModelService.getById(model1.getId());
        EvaluateModel savedModel2 = evaluateModelService.getById(model2.getId());
        assertEquals(Integer.valueOf(1), savedModel1.getIsEnabled(), "类型1模型应该被启用");
        assertEquals(Integer.valueOf(1), savedModel2.getIsEnabled(), "类型2模型应该被启用");
    }

    /**
     * 测试启用不存在的模型
     */
    @Test
    public void testEnableNonExistentModel() {
        assertThrows(Exception.class, () -> {
            evaluateModelService.enableModel(99999L);
        }, "启用不存在的模型应该抛出异常");
    }
}
