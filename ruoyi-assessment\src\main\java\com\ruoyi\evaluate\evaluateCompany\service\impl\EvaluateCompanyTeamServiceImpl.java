package com.ruoyi.evaluate.evaluateCompany.service.impl;

import java.util.*;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeamMember;
import com.ruoyi.evaluate.evaluateCompany.enums.TeamGroupEnum;
import com.ruoyi.evaluate.evaluateCompany.listener.TeamMemberImportListener;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamMemberService;
import com.ruoyi.evaluate.evaluateCompany.vo.ExcelTeamMemberVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateCompany.mapper.EvaluateCompanyTeamMapper;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeam;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 评估团队Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@Service
public class EvaluateCompanyTeamServiceImpl extends ServiceImpl<EvaluateCompanyTeamMapper, EvaluateCompanyTeam> implements IEvaluateCompanyTeamService {

    @Autowired
    private IEvaluateCompanyTeamMemberService teamMemberService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importTeamMembers(String teamName, Long orgId, MultipartFile file) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (teamName == null || teamName.trim().isEmpty()) {
            throw new ServiceException("团队名称不能为空");
        }
        if (orgId == null) {
            throw new ServiceException("所属单位ID不能为空");
        }
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        try {
            // 1. 创建团队
            EvaluateCompanyTeam team = createTeam(teamName.trim(), orgId);

            // 2. 解析Excel文件
            Map<String, Object> parseResult = parseExcelWithDetails(file);
            if (!(Boolean) parseResult.get("success")) {
                // 解析失败，返回解析错误
                result.put("success", false);
                result.put("teamId", team.getId());
                result.put("teamName", team.getTeamName());
                result.put("memberCount", 0);
                result.put("errorMessages", parseResult.get("errorMessages"));
                result.put("message", (String) parseResult.get("message"));
                return result;
            }

            @SuppressWarnings("unchecked")
            List<ExcelTeamMemberVo> excelMembers = (List<ExcelTeamMemberVo>) parseResult.get("members");

            // 3. 转换并保存团队成员（返回详细结果）
            Map<String, Object> convertResult = convertAndSaveMembersWithDetails(excelMembers, team.getId(), orgId);

            // 4. 构建返回结果
            if ((Boolean) convertResult.get("success")) {
                @SuppressWarnings("unchecked")
                List<EvaluateCompanyTeamMember> members = (List<EvaluateCompanyTeamMember>) convertResult.get("members");

                // 收集所有错误信息（解析阶段 + 转换阶段）
                @SuppressWarnings("unchecked")
                List<String> parseErrors = (List<String>) parseResult.get("errorMessages");
                @SuppressWarnings("unchecked")
                List<String> convertErrors = (List<String>) convertResult.get("errorMessages");

                List<String> allErrors = new ArrayList<>();
                if (parseErrors != null) allErrors.addAll(parseErrors);
                if (convertErrors != null) allErrors.addAll(convertErrors);

                result.put("success", true);
                result.put("teamId", team.getId());
                result.put("teamName", team.getTeamName());
                result.put("memberCount", members.size());
                result.put("errorMessages", allErrors);
                result.put("hasWarnings", !allErrors.isEmpty());

                if (!allErrors.isEmpty()) {
                    result.put("message", String.format("导入完成，成功 %d 名成员，跳过 %d 条有问题的数据", members.size(), allErrors.size()));
                    log.info("团队导入成功但有警告: teamId={}, teamName={}, memberCount={}, warnings={}",
                        team.getId(), teamName, members.size(), allErrors.size());
                } else {
                    result.put("message", String.format("成功导入团队 '%s'，包含 %d 名成员", teamName, members.size()));
                    log.info("团队导入成功: teamId={}, teamName={}, memberCount={}", team.getId(), teamName, members.size());
                }
            } else {
                // 有错误，但不抛异常，返回详细错误信息
                @SuppressWarnings("unchecked")
                List<String> parseErrors = (List<String>) parseResult.get("errorMessages");
                @SuppressWarnings("unchecked")
                List<String> convertErrors = (List<String>) convertResult.get("errorMessages");

                List<String> allErrors = new ArrayList<>();
                if (parseErrors != null) allErrors.addAll(parseErrors);
                if (convertErrors != null) allErrors.addAll(convertErrors);

                result.put("success", false);
                result.put("teamId", team.getId());
                result.put("teamName", team.getTeamName());
                result.put("memberCount", 0);
                result.put("errorMessages", allErrors);
                result.put("hasWarnings", false);
                result.put("message", "导入失败，请检查以下错误信息");
                log.warn("团队导入失败: teamId={}, teamName={}, errors={}", team.getId(), teamName, allErrors);
            }

        } catch (Exception e) {
            log.error("团队导入失败: teamName={}, orgId={}, error={}", teamName, orgId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
            result.put("errorMessages", Arrays.asList(e.getMessage()));
        }

        return result;
    }

    /**
     * 创建团队
     */
    private EvaluateCompanyTeam createTeam(String teamName, Long orgId) {
        EvaluateCompanyTeam team = new EvaluateCompanyTeam();
        team.setOrgId(orgId);
        team.setTeamName(teamName);
        team.setCreateTime(new Date());
        team.setCreateBy(SecurityUtils.getUsername());

        if (!this.save(team)) {
            throw new ServiceException("创建团队失败");
        }

        return team;
    }

    /**
     * 解析Excel文件（返回详细结果，不抛异常）
     */
    private Map<String, Object> parseExcelWithDetails(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        TeamMemberImportListener listener = new TeamMemberImportListener();

        try {
            log.info("开始解析Excel文件: {}", file.getOriginalFilename());

            EasyExcel.read(file.getInputStream(), ExcelTeamMemberVo.class, listener)
                    .headRowNumber(3) // 表头占3行，从第4行开始读取数据
                    .sheet()
                    .doRead();

            List<ExcelTeamMemberVo> members = listener.getCacheList();
            List<String> errors = listener.getErrorMessages();

            log.info("Excel解析完成，共解析到 {} 条有效成员数据，{} 个错误", members.size(), errors.size());

            if (members.isEmpty()) {
                // 没有有效数据
                String errorMsg = "Excel文件中没有找到有效的成员数据";
                if (!errors.isEmpty()) {
                    errorMsg += "，详细错误信息如下";
                    errors.add(0, errorMsg); // 在错误列表开头添加总体说明
                } else {
                    errors.add(errorMsg);
                }

                result.put("success", false);
                result.put("members", new ArrayList<>());
                result.put("errorMessages", errors);
                result.put("message", errorMsg);
                return result;
            }

            // 有数据但也有错误（比如某些行格式不正确被跳过）
            if (!errors.isEmpty()) {
                log.warn("Excel解析过程中发现 {} 个错误，但仍有 {} 条有效数据", errors.size(), members.size());
            }

            result.put("success", true);
            result.put("members", members);
            result.put("errorMessages", errors); // 即使成功也返回错误信息
            result.put("hasWarnings", !errors.isEmpty()); // 标记是否有警告
            if (!errors.isEmpty()) {
                result.put("message", String.format("解析完成，成功 %d 条，跳过 %d 条有问题的数据", members.size(), errors.size()));
            } else {
                result.put("message", String.format("成功解析 %d 条成员数据", members.size()));
            }
            return result;

        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", e.getMessage(), e);
            List<String> errors = new ArrayList<>();
            errors.add("解析Excel文件失败: " + e.getMessage());

            result.put("success", false);
            result.put("members", new ArrayList<>());
            result.put("errorMessages", errors);
            result.put("message", "解析Excel文件失败");
            return result;
        }
    }

    /**
     * 解析Excel文件（原方法，保持向后兼容）
     */
    private List<ExcelTeamMemberVo> parseExcel(MultipartFile file) throws Exception {
        Map<String, Object> result = parseExcelWithDetails(file);
        if (!(Boolean) result.get("success")) {
            @SuppressWarnings("unchecked")
            List<String> errors = (List<String>) result.get("errorMessages");
            String errorMsg = String.join("; ", errors);
            throw new ServiceException(errorMsg);
        }

        @SuppressWarnings("unchecked")
        List<ExcelTeamMemberVo> members = (List<ExcelTeamMemberVo>) result.get("members");
        return members;
    }

    /**
     * 转换并保存团队成员（返回详细结果，不抛异常）
     */
    private Map<String, Object> convertAndSaveMembersWithDetails(List<ExcelTeamMemberVo> excelMembers,
                                                                Long teamId, Long orgId) {
        Map<String, Object> result = new HashMap<>();
        List<EvaluateCompanyTeamMember> members = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        // 1. 数据转换和验证
        for (int i = 0; i < excelMembers.size(); i++) {
            ExcelTeamMemberVo excelMember = excelMembers.get(i);
            try {
                // 数据验证
                validateMemberData(excelMember);

                EvaluateCompanyTeamMember member = convertToMember(excelMember, teamId, orgId);
                members.add(member);
            } catch (Exception e) {
                String errorMsg = String.format("第%d行数据有误: %s",
                    excelMember.getRowNumber(), e.getMessage());
                log.error("转换成员数据失败: row={}, name={}, error={}",
                         excelMember.getRowNumber(), excelMember.getName(), e.getMessage());
                errorMessages.add(errorMsg);
            }
        }

        // 2. 如果有验证错误，直接返回错误信息
        if (!errorMessages.isEmpty()) {
            result.put("success", false);
            result.put("members", new ArrayList<>());
            result.put("errorMessages", errorMessages);
            result.put("message", String.format("数据验证失败，共发现 %d 个错误", errorMessages.size()));
            return result;
        }

        // 3. 批量保存
        try {
            if (!teamMemberService.saveBatch(members)) {
                errorMessages.add("保存团队成员到数据库失败");
                result.put("success", false);
                result.put("members", new ArrayList<>());
                result.put("errorMessages", errorMessages);
                result.put("message", "保存失败");
                return result;
            }
        } catch (Exception e) {
            log.error("批量保存团队成员失败", e);
            errorMessages.add("保存团队成员失败: " + e.getMessage());
            result.put("success", false);
            result.put("members", new ArrayList<>());
            result.put("errorMessages", errorMessages);
            result.put("message", "保存失败");
            return result;
        }

        // 4. 成功返回
        result.put("success", true);
        result.put("members", members);
        result.put("errorMessages", new ArrayList<>());
        result.put("message", String.format("成功保存 %d 名团队成员", members.size()));
        return result;
    }

    /**
     * 转换并保存团队成员（原方法，保持向后兼容）
     */
    private List<EvaluateCompanyTeamMember> convertAndSaveMembers(List<ExcelTeamMemberVo> excelMembers,
                                                                  Long teamId, Long orgId) {
        Map<String, Object> result = convertAndSaveMembersWithDetails(excelMembers, teamId, orgId);
        if (!(Boolean) result.get("success")) {
            @SuppressWarnings("unchecked")
            List<String> errorMessages = (List<String>) result.get("errorMessages");
            String allErrors = String.join("; ", errorMessages);
            throw new ServiceException("数据验证失败: " + allErrors);
        }

        @SuppressWarnings("unchecked")
        List<EvaluateCompanyTeamMember> members = (List<EvaluateCompanyTeamMember>) result.get("members");
        return members;
    }

    /**
     * 验证成员数据
     */
    private void validateMemberData(ExcelTeamMemberVo excelMember) {
        List<String> errors = new ArrayList<>();

        // 验证必填字段
        if (excelMember.getName() == null || excelMember.getName().trim().isEmpty()) {
            errors.add("姓名(D列)不能为空");
        }

        if (excelMember.getRole() == null || excelMember.getRole().trim().isEmpty()) {
            errors.add("角色(B列)不能为空");
        }

        if (excelMember.getDuty() == null || excelMember.getDuty().trim().isEmpty()) {
            errors.add("职责(C列)不能为空");
        }

        if (excelMember.getPosition() == null || excelMember.getPosition().trim().isEmpty()) {
            errors.add("岗位(F列)不能为空");
        }

        // 验证分组名称
        if (excelMember.getGroupName() == null || excelMember.getGroupName().trim().isEmpty()) {
            errors.add("分组名称不能为空，请确保Excel中包含正确的分组标识行");
        } else {
            Long groupId = TeamGroupEnum.getCodeByName(excelMember.getGroupName());
            if (groupId == null) {
                // 动态获取支持的分组名称列表
                String supportedGroups = getSupportedGroupNames();
                errors.add(String.format("分组名称'%s'无效，支持的分组：%s",
                    excelMember.getGroupName(), supportedGroups));
            }
        }

        // 验证姓名长度
        if (excelMember.getName() != null && excelMember.getName().trim().length() > 50) {
            errors.add("姓名(D列)长度不能超过50个字符");
        }

        // 验证角色长度
        if (excelMember.getRole() != null && excelMember.getRole().trim().length() > 100) {
            errors.add("角色(B列)长度不能超过100个字符");
        }

        if (!errors.isEmpty()) {
            throw new ServiceException(String.join(", ", errors));
        }
    }

    /**
     * 获取支持的分组名称列表
     */
    private String getSupportedGroupNames() {
        List<String> groupNames = new ArrayList<>();
        for (TeamGroupEnum groupEnum : TeamGroupEnum.values()) {
            groupNames.add(groupEnum.getName());
        }
        return String.join("、", groupNames);
    }

    /**
     * 转换Excel数据为团队成员实体
     */
    private EvaluateCompanyTeamMember convertToMember(ExcelTeamMemberVo excelMember, Long teamId, Long orgId) {
        EvaluateCompanyTeamMember member = new EvaluateCompanyTeamMember();

        member.setOrgId(orgId);
        member.setTeamId(teamId);

        // 根据分组名称获取groupId（此时已经验证过了）
        Long groupId = TeamGroupEnum.getCodeByName(excelMember.getGroupName());
        member.setGroupId(groupId);
        member.setGroupName(excelMember.getGroupName());

        member.setRole(excelMember.getRole().trim());
        member.setDuty(excelMember.getDuty().trim());
        member.setName(excelMember.getName().trim());
        member.setPosition(excelMember.getPosition().trim());

        // 可选字段处理（注意：EvaluateCompanyTeamMember实体中没有unit字段，所以跳过）
        member.setAbility(excelMember.getAbility() != null ? excelMember.getAbility().trim() : "");
        member.setExperience(excelMember.getExperience() != null ? excelMember.getExperience().trim() : "");

        member.setStatus(1); // 默认状态为正常

        member.setCreateTime(new Date());
        member.setCreateBy(SecurityUtils.getUsername());

        return member;
    }
}
