# Map到实体类迁移指南

## 问题分析

当前代码中大量使用`Map<String, Object>`来处理步骤数据，存在以下问题：

1. **类型安全性差**：编译时无法检查数据类型错误
2. **代码可读性低**：无法直观了解数据结构
3. **维护困难**：字段名称容易拼写错误，重构困难
4. **IDE支持不足**：缺乏自动补全和类型提示

## 解决方案

采用**渐进式迁移**策略，创建实体类同时保持向后兼容性：

### 1. 核心实体类

#### 通用响应实体
- `StepDataResponse` - 步骤数据响应
- `StepStatusResponse` - 步骤状态响应  
- `StepStatisticsResponse` - 步骤统计响应

#### 业务特定实体
- `DataAssetInfo` - 数据资产信息
- `AssetStatistics` - 资产统计信息

### 2. 转换工具类

`StepDataConverter` 提供Map和实体类之间的转换功能：

```java
// Map转实体
StepDataResponse response = StepDataConverter.mapToStepDataResponse(map);

// 实体转Map（向后兼容）
Map<String, Object> map = response.toMap();
```

### 3. 迁移策略

#### 阶段1：创建实体类和转换工具
- ✅ 创建核心响应实体类
- ✅ 创建业务特定实体类
- ✅ 创建转换工具类

#### 阶段2：在处理器中使用实体类
```java
@Override
protected Map<String, Object> processSpecificStepData(...) {
    // 1. 构建基础响应对象
    StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(...);
    
    // 2. 添加业务数据（使用实体类）
    List<DataAssetInfo> assetList = generateStructuredAssetList(planTask);
    response.addExtensionData("assetList", assetList);
    
    // 3. 转换为Map（保持兼容性）
    return response.toMap();
}
```

#### 阶段3：逐步扩展到所有处理器
- 为每个步骤创建专门的实体类
- 更新所有处理器使用新的实体类
- 保持接口兼容性

#### 阶段4：接口升级（可选）
- 考虑创建新的类型安全接口
- 逐步迁移调用方
- 最终移除Map接口

## 优势对比

### 使用Map的方式（当前）
```java
Map<String, Object> asset = new HashMap<>();
asset.put("assetId", "ASSET_001");
asset.put("assetName", "数据资产1");
asset.put("assetType", "数据库");
// 容易出现拼写错误，无类型检查
```

### 使用实体类的方式（改进后）
```java
DataAssetInfo asset = DataAssetInfo.builder()
    .assetId("ASSET_001")
    .assetName("数据资产1")
    .assetType("数据库")
    .build();
// 类型安全，IDE支持，易于维护
```

## 实施建议

### 1. 立即可做
- ✅ 使用已创建的实体类和转换工具
- ✅ 在新的处理器中使用实体类
- ✅ 参考`ImprovedCreatePlanProcessor`示例

### 2. 逐步迁移
- 为每个步骤创建专门的实体类
- 更新现有处理器使用实体类
- 添加单元测试验证转换正确性

### 3. 长期规划
- 考虑接口升级，提供类型安全的API
- 建立代码规范，新代码必须使用实体类
- 定期重构，减少Map的使用

## 兼容性保证

1. **接口不变**：所有现有接口仍返回`Map<String, Object>`
2. **数据结构不变**：转换后的Map结构与原来完全一致
3. **调用方无感知**：现有调用代码无需修改

## 示例对比

### 原始方式
```java
Map<String, Object> statistics = new HashMap<>();
statistics.put("totalAssets", 150);
statistics.put("assetsByType", typeMap);
// 字段名容易拼错，类型不安全
```

### 改进方式
```java
AssetStatistics statistics = AssetStatistics.builder()
    .totalAssets(150)
    .assetsByType(typeMap)
    .build();
// 类型安全，IDE自动补全，易于重构
```

## 结论

通过引入实体类和转换工具，我们可以：

1. **提高代码质量**：类型安全、可读性好
2. **保持兼容性**：现有代码无需修改
3. **便于维护**：结构化数据，易于扩展
4. **渐进迁移**：可以逐步替换，风险可控

建议优先在新功能中使用实体类，然后逐步迁移现有代码。
