# 评估计划暂存API接口变更说明

## 概述

根据需求，所有涉及`draftKey`的接口都已修改为使用`planId`（评估计划ID）和`step`（步骤）参数来自动拼接生成暂存键。暂存键格式为：`{planId}_{step}`

## API接口变更详情

### 1. 暂存数据接口
**路径**: `POST /evaluatePlan/task/draft/save`

**变更前**:
```
参数:
- draftKey (String): 暂存键
- taskDto (EvaluatePlanTaskDto): 暂存数据
- expireHours (int): 过期时间（小时）
```

**变更后**:
```
参数:
- planId (Long): 评估计划ID
- currentStep (String): 当前步骤
- stepData (Object): 当前步骤的数据内容

特性:
- 自动生成暂存键: planId + "_" + currentStep
- 永久保存（365天过期时间）
- 支持任意类型的步骤数据
```

**示例**:
```javascript
POST /evaluatePlan/task/draft/save?planId=123&currentStep=step1
{
  "formData": {...},
  "attachments": [...]
}
```

### 2. 获取暂存数据接口
**路径**: `GET /evaluatePlan/task/draft/get`

**变更前**:
```
路径: GET /evaluatePlan/task/draft/get/{draftKey}
参数: draftKey (路径参数)
```

**变更后**:
```
路径: GET /evaluatePlan/task/draft/get
参数:
- planId (Long): 评估计划ID
- step (String): 步骤
```

**示例**:
```javascript
GET /evaluatePlan/task/draft/get?planId=123&step=step1
```

### 3. 删除暂存数据接口
**路径**: `DELETE /evaluatePlan/task/draft/delete`

**变更前**:
```
路径: DELETE /evaluatePlan/task/draft/delete/{draftKey}
参数: draftKey (路径参数)
```

**变更后**:
```
路径: DELETE /evaluatePlan/task/draft/delete
参数:
- planId (Long): 评估计划ID
- step (String): 步骤
```

**示例**:
```javascript
DELETE /evaluatePlan/task/draft/delete?planId=123&step=step1
```

### 4. 检查暂存数据是否存在接口
**路径**: `GET /evaluatePlan/task/draft/exists`

**变更前**:
```
路径: GET /evaluatePlan/task/draft/exists/{draftKey}
参数: draftKey (路径参数)
```

**变更后**:
```
路径: GET /evaluatePlan/task/draft/exists
参数:
- planId (Long): 评估计划ID
- step (String): 步骤
```

**示例**:
```javascript
GET /evaluatePlan/task/draft/exists?planId=123&step=step1
```

### 5. 从暂存创建任务接口
**路径**: `POST /evaluatePlan/task/draft/create`

**变更前**:
```
路径: POST /evaluatePlan/task/draft/create/{draftKey}
参数: 
- draftKey (路径参数)
- deleteDraft (boolean): 是否删除暂存数据
```

**变更后**:
```
路径: POST /evaluatePlan/task/draft/create
参数:
- planId (Long): 评估计划ID
- step (String): 步骤
- deleteDraft (boolean): 是否删除暂存数据
```

**示例**:
```javascript
POST /evaluatePlan/task/draft/create?planId=123&step=step1&deleteDraft=true
```

### 6. 批量获取暂存数据接口
**路径**: `POST /evaluatePlan/task/draft/batch/get`

**变更前**:
```
参数: draftKeys (List<String>): 暂存键列表
```

**变更后**:
```
参数:
- planId (Long): 评估计划ID
- steps (List<String>): 步骤列表
```

**示例**:
```javascript
POST /evaluatePlan/task/draft/batch/get?planId=123
["step1", "step2", "step3"]
```

### 7. 批量删除暂存数据接口
**路径**: `DELETE /evaluatePlan/task/draft/batch/delete`

**变更前**:
```
参数: draftKeys (List<String>): 暂存键列表
```

**变更后**:
```
参数:
- planId (Long): 评估计划ID
- steps (List<String>): 步骤列表
```

**示例**:
```javascript
DELETE /evaluatePlan/task/draft/batch/delete?planId=123
["step1", "step2", "step3"]
```

## 新增接口

### 8. 删除上一步暂存数据接口
**路径**: `DELETE /evaluatePlan/task/draft/deleteStepDraft`

```
参数:
- planId (Long): 评估计划ID
- previousStep (String): 上一步骤
```

**用途**: 在进入下一步时删除上一步的暂存数据

**示例**:
```javascript
DELETE /evaluatePlan/task/draft/deleteStepDraft?planId=123&previousStep=step1
```

## 保持不变的接口

以下接口保持原有功能不变：

- `GET /evaluatePlan/task/draft/list` - 获取用户所有暂存数据列表
- `DELETE /evaluatePlan/task/draft/clear` - 清理用户所有暂存数据
- `POST /evaluatePlan/task/draft/batch/save` - 批量暂存数据

## 暂存键生成规则

所有接口都使用统一的暂存键生成规则：
```
draftKey = planId + "_" + step
```

**示例**:
- planId=123, step="step1" → draftKey="123_step1"
- planId=456, step="basic_info" → draftKey="456_basic_info"
- planId=789, step="risk_assessment" → draftKey="789_risk_assessment"

## 返回数据格式

所有修改后的接口都会在返回结果中包含以下信息：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "draftKey": "123_step1",
    "planId": 123,
    "step": "step1",
    "data": {...},
    "exists": true
  }
}
```

## 使用场景

### 典型工作流程
1. **保存当前步骤数据**:
   ```javascript
   POST /evaluatePlan/task/draft/save?planId=123&currentStep=step1
   ```

2. **获取指定步骤数据**:
   ```javascript
   GET /evaluatePlan/task/draft/get?planId=123&step=step1
   ```

3. **进入下一步时删除上一步数据**:
   ```javascript
   DELETE /evaluatePlan/task/draft/deleteStepDraft?planId=123&previousStep=step1
   ```

4. **检查步骤数据是否存在**:
   ```javascript
   GET /evaluatePlan/task/draft/exists?planId=123&step=step2
   ```

## 兼容性说明

⚠️ **重要提醒**: 这些变更是破坏性的，现有前端代码需要相应修改：

1. **路径变更**: 部分接口从路径参数改为查询参数
2. **参数变更**: `draftKey`参数被`planId`和`step`参数替代
3. **数据格式**: 返回数据格式有所调整，增加了更多元信息

## 迁移建议

1. **前端代码修改**: 更新所有调用这些接口的前端代码
2. **测试验证**: 全面测试修改后的接口功能
3. **文档更新**: 更新API文档和使用说明
4. **向后兼容**: 如需要，可以考虑保留旧接口一段时间作为过渡
