package com.ruoyi.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 暂存数据封装类
 * 用于封装暂存的业务数据及其元数据
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Accessors(chain = true)
public class DraftData<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 暂存的业务数据 */
    private T data;

    /** 暂存元数据 */
    private DraftMetadata metadata;

    /**
     * 创建暂存数据
     *
     * @param data 业务数据
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @param <T> 数据类型
     * @return 暂存数据对象
     */
    public static <T> DraftData<T> create(T data, String businessType, Long userId, String draftKey) {
        DraftData<T> draftData = new DraftData<>();
        draftData.setData(data);
        
        DraftMetadata metadata = new DraftMetadata()
                .setBusinessType(businessType)
                .setUserId(userId)
                .setDraftKey(draftKey)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
        
        draftData.setMetadata(metadata);
        return draftData;
    }

    /**
     * 创建暂存数据（带过期时间）
     *
     * @param data 业务数据
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @param expireTime 过期时间
     * @param <T> 数据类型
     * @return 暂存数据对象
     */
    public static <T> DraftData<T> create(T data, String businessType, Long userId, 
                                         String draftKey, Date expireTime) {
        DraftData<T> draftData = create(data, businessType, userId, draftKey);
        draftData.getMetadata().setExpireTime(expireTime);
        return draftData;
    }

    /**
     * 更新暂存数据
     *
     * @param newData 新的业务数据
     * @return 当前对象
     */
    public DraftData<T> updateData(T newData) {
        this.data = newData;
        if (this.metadata != null) {
            this.metadata.setUpdateTime(new Date());
        }
        return this;
    }

    /**
     * 检查是否已过期
     *
     * @return 已过期返回true
     */
    public boolean isExpired() {
        if (metadata == null || metadata.getExpireTime() == null) {
            return false;
        }
        return new Date().after(metadata.getExpireTime());
    }

    /**
     * 获取数据摘要信息
     *
     * @return 摘要信息
     */
    public String getSummary() {
        if (metadata == null) {
            return "无元数据";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("业务类型: ").append(metadata.getBusinessType());
        summary.append(", 用户ID: ").append(metadata.getUserId());
        summary.append(", 暂存键: ").append(metadata.getDraftKey());
        summary.append(", 创建时间: ").append(metadata.getCreateTime());
        
        if (metadata.getExpireTime() != null) {
            summary.append(", 过期时间: ").append(metadata.getExpireTime());
        }
        
        return summary.toString();
    }
}
