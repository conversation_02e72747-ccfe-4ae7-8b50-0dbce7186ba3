# 数据库迁移说明

## 问题描述

您遇到的错误：
```
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.ruoyi.evaluateModel.mapper.EvaluateModelMapper.updateEnabledStatusByTypeId
```

这个错误表明数据库中缺少 `is_enabled` 字段，导致相关的SQL语句无法执行。

## 解决方案

### 第一步：执行数据库迁移脚本

请执行以下SQL脚本来添加必要的数据库字段：

```sql
-- 为dsa_evaluate_model表添加is_enabled字段
ALTER TABLE `dsa_evaluate_model` 
ADD COLUMN `is_enabled` int(1) DEFAULT 0 COMMENT '是否为当前启用的模型，1-启用 0-未启用（每个type_id下只能有一个模型启用）' 
AFTER `status`;

-- 为每个type_id设置一个默认启用的模型（选择最新的一个）
UPDATE `dsa_evaluate_model` m1 
SET `is_enabled` = 1 
WHERE m1.id = (
    SELECT m2.id 
    FROM (
        SELECT id, type_id, 
               ROW_NUMBER() OVER (PARTITION BY type_id ORDER BY id DESC) as rn
        FROM `dsa_evaluate_model` 
        WHERE status = 1 AND del_flag = 0
    ) m2 
    WHERE m2.type_id = m1.type_id AND m2.rn = 1
);

-- 创建索引以提高查询性能
CREATE INDEX `idx_type_enabled` ON `dsa_evaluate_model` (`type_id`, `is_enabled`);
```

### 第二步：重启应用

执行完数据库脚本后，请重启应用程序以使新的配置生效。

### 第三步：启用完整功能

数据库迁移完成后，需要修改代码以启用完整的功能。请按以下步骤操作：

1. 打开 `EvaluateModelServiceImpl.java` 文件
2. 找到 `save` 方法中的这行代码：
   ```java
   // 暂时跳过启用状态逻辑，等数据库字段添加后再启用
   log.info("暂时跳过启用状态逻辑，请先执行数据库迁移脚本添加is_enabled字段");
   ```
3. 将其替换为完整的启用状态逻辑（参考原始实现）

4. 找到 `enableModel` 方法中的这行代码：
   ```java
   // 暂时跳过启用状态逻辑，等数据库字段添加后再启用
   log.warn("启用模型功能暂时不可用，请先执行数据库迁移脚本添加is_enabled字段");
   throw new ServiceException("启用模型功能暂时不可用，请先执行数据库迁移脚本");
   ```
5. 将其替换为完整的启用模型逻辑（参考原始实现）

## 验证步骤

1. 执行数据库脚本后，检查表结构：
   ```sql
   DESCRIBE dsa_evaluate_model;
   ```
   应该能看到 `is_enabled` 字段

2. 检查数据初始化：
   ```sql
   SELECT type_id, COUNT(*) as total, SUM(is_enabled) as enabled_count 
   FROM dsa_evaluate_model 
   WHERE status = 1 AND del_flag = 0 
   GROUP BY type_id;
   ```
   每个 type_id 应该有且仅有一个启用的模型

3. 重启应用后，尝试添加新的评估模型，应该不再出现错误

## 临时解决方案

如果暂时无法执行数据库迁移，当前代码已经做了兼容处理：
- 保存模型功能可以正常使用（跳过启用状态逻辑）
- 启用模型功能会提示需要先执行数据库迁移

## 联系支持

如果在执行过程中遇到问题，请提供以下信息：
1. 数据库版本
2. 错误日志
3. 当前表结构（`DESCRIBE dsa_evaluate_model` 的结果）

这样我们可以更好地协助您解决问题。
