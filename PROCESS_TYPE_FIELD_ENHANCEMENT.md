# 流程实例类型字段增强总结

## 修改概述

根据您在 `ProcessInstance.java` 中新增的 `type` 字段，我们已经完成了相关代码的适配，确保从 `EvaluatePlanTaskController.java` 创建的流程实例的 `type` 字段都设置为 "evaluate"。

## 主要修改内容

### 1. ProcessStepInstance.java 增强
**文件位置**: `ruoyi-assessment/src/main/java/com/ruoyi/process/domain/ProcessStepInstance.java`

**新增字段**:
```java
/** 评估计划ID */
@Excel(name = "评估计划ID")
@TableField(value = "plan_task_id")
private Long planTaskId;
```

**说明**: 添加了 `planTaskId` 字段，用于关联评估计划任务，完善了数据模型的关联关系。

### 2. ProcessFlowServiceImpl.java 修改
**文件位置**: `ruoyi-assessment/src/main/java/com/ruoyi/process/service/impl/ProcessFlowServiceImpl.java`

#### 2.1 创建流程实例时设置type字段
```java
// 创建流程实例
ProcessInstance processInstance = new ProcessInstance();
processInstance.setProcessId(processDefinition.getId());
processInstance.setBusinessId(businessId);
processInstance.setType("evaluate");  // 设置类型为evaluate
processInstance.setStatus(1);
processInstance.setCreateBy(operator);
processInstance.setCreateTime(new Date());
```

#### 2.2 修复链式调用问题
将原来的链式调用改为分别设置属性，避免编译错误：
- `ProcessInstance` 对象的属性设置
- `ProcessStepInstance` 对象的属性设置

### 3. ProcessStepInstanceMapper.xml 更新
**文件位置**: `ruoyi-assessment/src/main/resources/mapper/process/ProcessStepInstanceMapper.xml`

#### 3.1 更新ResultMap
```xml
<resultMap type="ProcessStepInstance" id="ProcessStepInstanceResult">
    <result property="id"    column="id"    />
    <result property="planTaskId"    column="plan_task_id"    />  <!-- 新增 -->
    <result property="processInstanceId"    column="process_instance_id"    />
    <result property="stepDefinitionId"    column="step_definition_id"    />
    ...
</resultMap>
```

#### 3.2 更新SQL查询
```xml
<sql id="selectProcessStepInstanceVo">
    select id, plan_task_id, process_instance_id, step_definition_id, step_name, status, start_time, end_time, duration_ms, operator, remark, create_by, create_time, update_by, update_time, del_flag from dsa_process_step_instance
</sql>
```

#### 3.3 更新插入语句
```xml
<trim prefix="(" suffix=")" suffixOverrides=",">
    <if test="planTaskId != null">plan_task_id,</if>  <!-- 新增 -->
    <if test="processInstanceId != null">process_instance_id,</if>
    ...
</trim>
<trim prefix="values (" suffix=")" suffixOverrides=",">
    <if test="planTaskId != null">#{planTaskId},</if>  <!-- 新增 -->
    <if test="processInstanceId != null">#{processInstanceId},</if>
    ...
</trim>
```

## 数据流转示例

### 创建评估计划任务时的数据流转：

1. **评估计划任务创建**
   ```sql
   INSERT INTO dsa_evaluate_plan_task (id, evaluate_type, model_id, name, ...) 
   VALUES (1001, 'data_security_plan', 1, '某公司数据安全评估', ...);
   ```

2. **流程实例创建**（type字段设置为"evaluate"）
   ```sql
   INSERT INTO dsa_process_instance (id, process_id, business_id, type, status, ...) 
   VALUES (2001, 1, 1001, 'evaluate', 1, ...);
   ```

3. **步骤实例创建**（包含planTaskId关联）
   ```sql
   INSERT INTO dsa_process_step_instance (id, plan_task_id, process_instance_id, step_definition_id, step_name, status, ...) 
   VALUES 
   (3001, 1001, 2001, 1, '风险识别', 0, ...),
   (3002, 1001, 2001, 2, '风险分析', 0, ...),
   (3003, 1001, 2001, 3, '风险评估', 0, ...);
   ```

## 关键特性

### 1. 类型标识
- 所有从评估计划任务创建的流程实例都标记为 `type = "evaluate"`
- 便于区分不同业务场景的流程实例
- 支持按类型查询和统计

### 2. 完整关联
- `ProcessInstance.businessId` → `EvaluatePlanTask.id`
- `ProcessStepInstance.planTaskId` → `EvaluatePlanTask.id`
- `ProcessStepInstance.processInstanceId` → `ProcessInstance.id`

### 3. 数据一致性
- 事务保证流程实例和步骤实例创建的原子性
- 异常处理确保数据完整性
- 重复检查避免数据冲突

## 使用场景

### 1. 流程类型区分
```java
// 查询评估类型的流程实例
List<ProcessInstance> evaluateProcesses = processInstanceService.list(
    new QueryWrapper<ProcessInstance>()
        .eq("type", "evaluate")
        .eq("status", 1)
);
```

### 2. 业务数据关联
```java
// 通过评估任务ID查询相关流程数据
ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(taskId);
List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstance.getId());
```

### 3. 统计分析
```java
// 统计评估类型流程的执行情况
Map<String, Object> stats = processInstanceService.getStatsByType("evaluate");
```

## 扩展性支持

### 1. 多类型支持
- 可以为不同业务场景设置不同的type值
- 如：`"evaluate"`, `"audit"`, `"review"` 等

### 2. 类型配置化
```java
// 可以通过配置文件或数据库配置类型
@Value("${process.type.evaluate:evaluate}")
private String evaluateProcessType;
```

### 3. 类型枚举
```java
public enum ProcessType {
    EVALUATE("evaluate", "评估流程"),
    AUDIT("audit", "审计流程"),
    REVIEW("review", "审查流程");
    
    private String code;
    private String name;
}
```

## 验证方法

### 1. 数据库验证
```sql
-- 验证流程实例type字段
SELECT id, business_id, type, status FROM dsa_process_instance WHERE type = 'evaluate';

-- 验证步骤实例planTaskId字段
SELECT id, plan_task_id, process_instance_id, step_name FROM dsa_process_step_instance WHERE plan_task_id IS NOT NULL;
```

### 2. 日志验证
查看应用日志中的流程创建记录：
```
创建流程实例成功，流程实例ID: 2001, 业务ID: 1001, 流程类型: data_security_plan
创建步骤实例成功，流程实例ID: 2001, 计划任务ID: 1001, 步骤数量: 3
```

### 3. 接口测试
通过创建评估计划任务的接口，验证流程数据是否正确创建。

## 注意事项

1. **数据库字段** - 确保数据库表中已添加相应字段
2. **索引优化** - 考虑为type和planTaskId字段添加索引
3. **历史数据** - 对于已存在的数据，可能需要数据迁移脚本
4. **权限控制** - 确保不同类型的流程有相应的权限控制
5. **监控告警** - 添加流程创建失败的监控和告警

## 总结

通过这次修改，我们成功实现了：
1. 流程实例类型标识功能
2. 完善的数据关联关系
3. 代码的健壮性和可维护性
4. 为后续的业务扩展奠定基础

所有从评估计划任务创建的流程实例都会自动设置 `type = "evaluate"`，便于后续的查询、统计和管理。
