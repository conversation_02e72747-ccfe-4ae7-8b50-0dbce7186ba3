package com.ruoyi.common.utils;

import org.springframework.beans.BeanUtils;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.function.BiConsumer;
import com.ruoyi.common.annotation.ExcelFieldValidate;
import com.ruoyi.common.utils.DictUtils;

public class BeanConvertUtil {
    /**
     * 通用实体转换方法
     * @param source 源对象
     * @param targetClass 目标类型
     * @param customHandler 自定义字段处理（可为null）
     * @return 目标对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass, BiConsumer<S, T> customHandler) {
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            // 自动处理字典字段
            Field[] fields = targetClass.getDeclaredFields();
            for (Field field : fields) {
                ExcelFieldValidate anno = field.getAnnotation(ExcelFieldValidate.class);
                if (anno != null && !anno.dictType().isEmpty()) {
                    try {
                        // 获取源对象同名字段
                        Field srcField = source.getClass().getDeclaredField(field.getName());
                        srcField.setAccessible(true);
                        Object srcValue = srcField.get(source);
                        String dictLabel = srcValue == null ? "" : srcValue.toString();
                        // 字典转换
                        String dictValue = DictUtils.getDictValue(anno.dictType(), dictLabel, null);
                        field.setAccessible(true);
                        field.set(target, dictValue);
                    } catch (NoSuchFieldException ignore) {
                        // 源对象没有该字段，跳过
                    }
                }
            }
            if (customHandler != null) {
                customHandler.accept(source, target);
            }
            return target;
        } catch (Exception e) {
            throw new RuntimeException("实体转换失败", e);
        }
    }

    // 日期字符串转Date的工具方法
    public static Date parseDate(String dateStr) {
        String[] patterns = {"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy年MM月dd日"};
        for (String pattern : patterns) {
            try {
                return new SimpleDateFormat(pattern).parse(dateStr);
            } catch (ParseException ignored) {}
        }
        return null;
    }
}
