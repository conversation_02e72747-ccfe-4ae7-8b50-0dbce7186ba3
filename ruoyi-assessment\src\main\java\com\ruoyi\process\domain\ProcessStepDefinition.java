package com.ruoyi.process.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 流程步骤定义对象 dsa_process_step_definition
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_process_step_definition")
public class ProcessStepDefinition extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 流程ID */
    @Excel(name = "流程ID")
    @TableField(value = "process_id")
    private Long processId;

    /** 步骤顺序 */
    @Excel(name = "步骤顺序")
    @TableField(value = "step_order")
    private Long stepOrder;

    /** 步骤名称 */
    @Excel(name = "步骤名称")
    @TableField(value = "step_name")
    private String stepName;

    /** 步骤编码 */
    @Excel(name = "步骤编码")
    @TableField(value = "step_code")
    private String stepCode;

    /** 处理类型：manual-人工，auto-系统自动 */
    @Excel(name = "处理类型：manual-人工，auto-系统自动")
    @TableField(value = "handler_type")
    private String handlerType;

    /** 步骤描述 */
    @Excel(name = "步骤描述")
    @TableField(value = "description")
    private String description;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}