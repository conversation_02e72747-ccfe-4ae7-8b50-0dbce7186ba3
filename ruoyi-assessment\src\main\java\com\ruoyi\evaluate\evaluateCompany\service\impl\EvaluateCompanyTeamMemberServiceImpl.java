package com.ruoyi.evaluate.evaluateCompany.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateCompany.mapper.EvaluateCompanyTeamMemberMapper;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeamMember;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 评估单位评估团队成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class EvaluateCompanyTeamMemberServiceImpl extends ServiceImpl<EvaluateCompanyTeamMemberMapper, EvaluateCompanyTeamMember> implements IEvaluateCompanyTeamMemberService {

}
