package com.ruoyi.common.utils;

import com.ruoyi.common.exception.ServiceException;
import org.slf4j.Logger;

/**
 * 异常处理工具类
 * 用于统一处理异常信息，避免重复的错误信息
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class ExceptionHandlerUtil {

    /**
     * 处理异常并抛出ServiceException
     * 如果原异常已经是ServiceException，则直接抛出，避免重复包装
     *
     * @param e 原始异常
     * @param contextMessage 上下文错误信息
     * @param logger 日志记录器
     * @param logMessage 日志消息模板
     * @param logParams 日志参数
     * @throws ServiceException 业务异常
     */
    public static void handleAndThrow(Exception e, String contextMessage, Logger logger, String logMessage, Object... logParams) {
        if (e instanceof ServiceException) {
            // 如果已经是ServiceException，直接抛出，避免重复包装
            if (logger != null) {
                logger.error(contextMessage + ", " + logMessage + ", 异常: {}",
                    combineParams(logParams, e.getMessage()), e);
            }
            throw (ServiceException) e;
        } else {
            // 其他异常，包装为ServiceException
            if (logger != null) {
                logger.error(contextMessage + ", " + logMessage + ", 异常: {}",
                    combineParams(logParams, e.getMessage()), e);
            }
            throw new ServiceException(contextMessage + ": " + e.getMessage());
        }
    }

    /**
     * 处理异常并抛出ServiceException（简化版本）
     *
     * @param e 原始异常
     * @param contextMessage 上下文错误信息
     * @param logger 日志记录器
     * @throws ServiceException 业务异常
     */
    public static void handleAndThrow(Exception e, String contextMessage, Logger logger) {
        if (e instanceof ServiceException) {
            // 如果已经是ServiceException，直接抛出，避免重复包装
            if (logger != null) {
                logger.error(contextMessage + ", 异常: {}", e.getMessage(), e);
            }
            throw (ServiceException) e;
        } else {
            // 其他异常，包装为ServiceException
            if (logger != null) {
                logger.error(contextMessage + ", 异常: {}", e.getMessage(), e);
            }
            throw new ServiceException(contextMessage + ": " + e.getMessage());
        }
    }

    /**
     * 合并日志参数
     */
    private static Object[] combineParams(Object[] originalParams, Object... additionalParams) {
        if (originalParams == null || originalParams.length == 0) {
            return additionalParams;
        }
        if (additionalParams == null || additionalParams.length == 0) {
            return originalParams;
        }

        Object[] combined = new Object[originalParams.length + additionalParams.length];
        System.arraycopy(originalParams, 0, combined, 0, originalParams.length);
        System.arraycopy(additionalParams, 0, combined, originalParams.length, additionalParams.length);
        return combined;
    }

    /**
     * 处理异常并抛出ServiceException（不记录日志）
     * 
     * @param e 原始异常
     * @param contextMessage 上下文错误信息
     * @throws ServiceException 业务异常
     */
    public static void handleAndThrow(Exception e, String contextMessage) {
        handleAndThrow(e, contextMessage, null);
    }

    /**
     * 检查异常是否为ServiceException
     * 
     * @param e 异常对象
     * @return 是否为ServiceException
     */
    public static boolean isServiceException(Exception e) {
        return e instanceof ServiceException;
    }

    /**
     * 获取异常的根本原因消息
     * 避免异常信息的重复包装
     * 
     * @param e 异常对象
     * @return 根本原因消息
     */
    public static String getRootMessage(Exception e) {
        if (e instanceof ServiceException) {
            return e.getMessage();
        }
        
        Throwable cause = e;
        while (cause.getCause() != null) {
            cause = cause.getCause();
            if (cause instanceof ServiceException) {
                return cause.getMessage();
            }
        }
        
        return e.getMessage();
    }

    /**
     * 创建带有上下文信息的ServiceException
     * 如果原异常已经包含上下文信息，则不重复添加
     * 
     * @param e 原始异常
     * @param contextMessage 上下文信息
     * @return ServiceException
     */
    public static ServiceException createServiceException(Exception e, String contextMessage) {
        if (e instanceof ServiceException) {
            String originalMessage = e.getMessage();
            // 检查是否已经包含上下文信息，避免重复
            if (originalMessage.contains(contextMessage)) {
                return (ServiceException) e;
            }
        }
        
        return new ServiceException(contextMessage + ": " + getRootMessage(e));
    }

    /**
     * 安全地获取异常消息
     * 处理null异常和空消息的情况
     * 
     * @param e 异常对象
     * @param defaultMessage 默认消息
     * @return 异常消息
     */
    public static String safeGetMessage(Exception e, String defaultMessage) {
        if (e == null) {
            return defaultMessage;
        }
        
        String message = e.getMessage();
        return StringUtils.isNotEmpty(message) ? message : defaultMessage;
    }
}
