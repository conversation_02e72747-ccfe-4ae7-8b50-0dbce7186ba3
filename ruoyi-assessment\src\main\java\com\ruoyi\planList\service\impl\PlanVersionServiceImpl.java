package com.ruoyi.planList.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.planList.mapper.PlanVersionMapper;
import com.ruoyi.planList.domain.PlanVersion;
import com.ruoyi.planList.service.IPlanVersionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 评估计划清单版本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class PlanVersionServiceImpl extends ServiceImpl<PlanVersionMapper, PlanVersion> implements IPlanVersionService {

    @Override
    public PlanVersion getByVersionCode(String versionCode, Long orgId) {
        return this.lambdaQuery()
            .eq(PlanVersion::getVersionCode, versionCode)
            .eq(PlanVersion::getOrgId, orgId)
            .one();
    }
}
