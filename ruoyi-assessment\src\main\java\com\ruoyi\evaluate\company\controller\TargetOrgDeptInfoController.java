package com.ruoyi.evaluate.company.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.ListGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.company.domain.TargetOrgDeptInfo;
import com.ruoyi.evaluate.company.service.ITargetOrgDeptInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 被评估单位 部门信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/targetDept/info")
@Api(value = "被评估单位 部门信息管理控制器", tags = {"被评估单位 部门信息管理管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TargetOrgDeptInfoController extends BaseController {
    private final ITargetOrgDeptInfoService targetOrgDeptInfoService;

    /**
     * 查询被评估单位 部门信息管理列表
     */
    @ApiOperation("查询被评估单位 部门信息管理列表")
    @PreAuthorize("@ss.hasPermi('targetDept:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) TargetOrgDeptInfo targetOrgDeptInfo) {
        startPage();
        List<TargetOrgDeptInfo> list = targetOrgDeptInfoService.list(new QueryWrapper<TargetOrgDeptInfo>(targetOrgDeptInfo));
        return getDataTable(list);
    }

    /**
     * 获取被评估单位 部门信息管理详细信息
     */
    @ApiOperation("获取被评估单位 部门信息管理详细信息")
    @PreAuthorize("@ss.hasPermi('targetDept:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(targetOrgDeptInfoService.getById(id));
    }

    /**
     * 新增被评估单位 部门信息管理
     */
    @ApiOperation("新增被评估单位 部门信息管理")
    @PreAuthorize("@ss.hasPermi('targetDept:info:add')")
    @Log(title = "被评估单位 部门信息管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody TargetOrgDeptInfo targetOrgDeptInfo) {
        return toAjax(targetOrgDeptInfoService.save(targetOrgDeptInfo));
    }

    /**
     * 修改被评估单位 部门信息管理
     */
    @ApiOperation("修改被评估单位 部门信息管理")
    @PreAuthorize("@ss.hasPermi('targetDept:info:edit')")
    @Log(title = "被评估单位 部门信息管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TargetOrgDeptInfo targetOrgDeptInfo) {
        return toAjax(targetOrgDeptInfoService.updateById(targetOrgDeptInfo));
    }

    /**
     * 删除被评估单位 部门信息管理
     */
    @ApiOperation("删除被评估单位 部门信息管理")
    @PreAuthorize("@ss.hasPermi('targetDept:info:remove')")
    @Log(title = "被评估单位 部门信息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(targetOrgDeptInfoService.removeByIds(Arrays.asList(ids)));
    }
}