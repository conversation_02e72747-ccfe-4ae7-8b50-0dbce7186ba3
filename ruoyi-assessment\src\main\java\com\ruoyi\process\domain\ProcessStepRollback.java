package com.ruoyi.process.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 流程回退记录对象 dsa_process_step_rollback
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_process_step_rollback")
public class ProcessStepRollback extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 评估任务ID */
    @Excel(name = "评估任务ID")
    @TableField(value = "plan_task_id")
    private Long planTaskId;

    /** 流程实例ID */
    @Excel(name = "流程实例ID")
    @TableField(value = "process_instance_id")
    private Long processInstanceId;

    /** 回退前步骤实例ID */
    @Excel(name = "回退前步骤实例ID")
    @TableField(value = "from_step_instance_id")
    private Long fromStepInstanceId;

    /** 回退到的步骤定义ID */
    @Excel(name = "回退到的步骤定义ID")
    @TableField(value = "to_step_definition_id")
    private Long toStepDefinitionId;

    /** 回退时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回退时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "rollback_time")
    private Date rollbackTime;

    /** 回退操作人 */
    @Excel(name = "回退操作人")
    @TableField(value = "operator")
    private String operator;

    /** 回退意见/原因 */
    @Excel(name = "回退意见/原因")
    @TableField(value = "rollback_reason")
    private String rollbackReason;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}