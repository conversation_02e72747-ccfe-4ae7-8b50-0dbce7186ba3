<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.company.mapper.TargetCompanyInfoMapper">
    
    <resultMap type="TargetCompanyInfo" id="TargetCompanyInfoResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="simpleCompanyName"    column="simple_company_name"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="postCode"    column="post_code"    />
        <result property="contactName"    column="contact_name"    />
        <result property="job"    column="job"    />
        <result property="dept"    column="dept"    />
        <result property="tel"    column="tel"    />
        <result property="mobile"    column="mobile"    />
        <result property="email"    column="email"    />
        <result property="registerPlace"    column="register_place"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="orgType"    column="org_type"    />
        <result property="legalRepresentative"    column="legal_representative"    />
        <result property="branchUnit"    column="branch_unit"    />
        <result property="operationControl"    column="operation_control"    />
        <result property="personnelSituation"    column="personnel_situation"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="listingSituation"    column="listing_situation"    />
        <result property="dataClassification"    column="data_classification"    />
        <result property="dataRecord"    column="data_record"    />
        <result property="recordReply"    column="record_reply"    />
        <result property="timesNumber"    column="times_number"    />
        <result property="flow"    column="flow"    />
        <result property="bgLogo"    column="bg_logo"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTargetCompanyInfoVo">
        select id, company_name, simple_company_name, company_address, post_code, contact_name, job, dept, tel, mobile, email, register_place, credit_code, org_type, legal_representative, branch_unit, operation_control, personnel_situation, business_scope, listing_situation, data_classification, data_record, record_reply, times_number, flow, bg_logo, status, create_by, create_time, update_by, update_time, remark, del_flag from dsa_target_company_info
    </sql>

    <select id="selectTargetCompanyInfoList" parameterType="TargetCompanyInfo" resultMap="TargetCompanyInfoResult">
        <include refid="selectTargetCompanyInfoVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="simpleCompanyName != null  and simpleCompanyName != ''"> and simple_company_name like concat('%', #{simpleCompanyName}, '%')</if>
            <if test="companyAddress != null  and companyAddress != ''"> and company_address = #{companyAddress}</if>
            <if test="postCode != null  and postCode != ''"> and post_code = #{postCode}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="dept != null  and dept != ''"> and dept = #{dept}</if>
            <if test="tel != null  and tel != ''"> and tel = #{tel}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="registerPlace != null  and registerPlace != ''"> and register_place = #{registerPlace}</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="orgType != null  and orgType != ''"> and org_type = #{orgType}</if>
            <if test="legalRepresentative != null  and legalRepresentative != ''"> and legal_representative = #{legalRepresentative}</if>
            <if test="branchUnit != null  and branchUnit != ''"> and branch_unit = #{branchUnit}</if>
            <if test="operationControl != null  and operationControl != ''"> and operation_control = #{operationControl}</if>
            <if test="personnelSituation != null  and personnelSituation != ''"> and personnel_situation = #{personnelSituation}</if>
            <if test="businessScope != null  and businessScope != ''"> and business_scope = #{businessScope}</if>
            <if test="listingSituation != null  and listingSituation != ''"> and listing_situation = #{listingSituation}</if>
            <if test="dataClassification != null  and dataClassification != ''"> and data_classification = #{dataClassification}</if>
            <if test="dataRecord != null  and dataRecord != ''"> and data_record = #{dataRecord}</if>
            <if test="recordReply != null  and recordReply != ''"> and record_reply = #{recordReply}</if>
            <if test="timesNumber != null  and timesNumber != ''"> and times_number = #{timesNumber}</if>
            <if test="flow != null  and flow != ''"> and flow = #{flow}</if>
            <if test="bgLogo != null  and bgLogo != ''"> and bg_logo = #{bgLogo}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTargetCompanyInfoById" parameterType="Long" resultMap="TargetCompanyInfoResult">
        <include refid="selectTargetCompanyInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTargetCompanyInfo" parameterType="TargetCompanyInfo" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_target_company_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="simpleCompanyName != null">simple_company_name,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="postCode != null">post_code,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="job != null">job,</if>
            <if test="dept != null">dept,</if>
            <if test="tel != null">tel,</if>
            <if test="mobile != null">mobile,</if>
            <if test="email != null">email,</if>
            <if test="registerPlace != null">register_place,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="orgType != null">org_type,</if>
            <if test="legalRepresentative != null">legal_representative,</if>
            <if test="branchUnit != null">branch_unit,</if>
            <if test="operationControl != null">operation_control,</if>
            <if test="personnelSituation != null">personnel_situation,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="listingSituation != null">listing_situation,</if>
            <if test="dataClassification != null">data_classification,</if>
            <if test="dataRecord != null">data_record,</if>
            <if test="recordReply != null">record_reply,</if>
            <if test="timesNumber != null">times_number,</if>
            <if test="flow != null">flow,</if>
            <if test="bgLogo != null">bg_logo,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="simpleCompanyName != null">#{simpleCompanyName},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="postCode != null">#{postCode},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="job != null">#{job},</if>
            <if test="dept != null">#{dept},</if>
            <if test="tel != null">#{tel},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="email != null">#{email},</if>
            <if test="registerPlace != null">#{registerPlace},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="orgType != null">#{orgType},</if>
            <if test="legalRepresentative != null">#{legalRepresentative},</if>
            <if test="branchUnit != null">#{branchUnit},</if>
            <if test="operationControl != null">#{operationControl},</if>
            <if test="personnelSituation != null">#{personnelSituation},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="listingSituation != null">#{listingSituation},</if>
            <if test="dataClassification != null">#{dataClassification},</if>
            <if test="dataRecord != null">#{dataRecord},</if>
            <if test="recordReply != null">#{recordReply},</if>
            <if test="timesNumber != null">#{timesNumber},</if>
            <if test="flow != null">#{flow},</if>
            <if test="bgLogo != null">#{bgLogo},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTargetCompanyInfo" parameterType="TargetCompanyInfo">
        update dsa_target_company_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="simpleCompanyName != null">simple_company_name = #{simpleCompanyName},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="postCode != null">post_code = #{postCode},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="job != null">job = #{job},</if>
            <if test="dept != null">dept = #{dept},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="email != null">email = #{email},</if>
            <if test="registerPlace != null">register_place = #{registerPlace},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="orgType != null">org_type = #{orgType},</if>
            <if test="legalRepresentative != null">legal_representative = #{legalRepresentative},</if>
            <if test="branchUnit != null">branch_unit = #{branchUnit},</if>
            <if test="operationControl != null">operation_control = #{operationControl},</if>
            <if test="personnelSituation != null">personnel_situation = #{personnelSituation},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="listingSituation != null">listing_situation = #{listingSituation},</if>
            <if test="dataClassification != null">data_classification = #{dataClassification},</if>
            <if test="dataRecord != null">data_record = #{dataRecord},</if>
            <if test="recordReply != null">record_reply = #{recordReply},</if>
            <if test="timesNumber != null">times_number = #{timesNumber},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="bgLogo != null">bg_logo = #{bgLogo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTargetCompanyInfoById" parameterType="Long">
        delete from dsa_target_company_info where id = #{id}
    </delete>

    <delete id="deleteTargetCompanyInfoByIds" parameterType="String">
        delete from dsa_target_company_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>