package com.ruoyi.evaluate.company.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetOrgDeptInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 被评估单位部门信息Sheet2监听器
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
public class TargetOrgDeptInfoListener extends AnalysisEventListener<ExcelTargetOrgDeptInfo> {

    private final List<ExcelTargetOrgDeptInfo> cacheList = new ArrayList<>();
    private final List<String> errorMessages = new ArrayList<>();

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param analysisContext
     */
    @Override
    public void invoke(ExcelTargetOrgDeptInfo data, AnalysisContext analysisContext) {
        log.info("Sheet2 table row: {}", data);
        cacheList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Sheet2 Excel 数据解析完成，共 {} 条", cacheList.size());
        saveData();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("Sheet2 table header {}", headMap);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException ex = (ExcelDataConvertException) exception;
            String msg = String.format("Sheet2第%d行，第%d列，值\"%s\"转换失败",
                    ex.getRowIndex() + 1,
                    ex.getColumnIndex() + 1,
                    ex.getCellData() != null ? ex.getCellData().getStringValue() : "null");
            errorMessages.add(msg);
            log.warn(msg);
        } else {
            errorMessages.add("Sheet2未知错误：" + exception.getMessage());
            log.error("Sheet2读取异常", exception);
        }
    }

    public List<ExcelTargetOrgDeptInfo> getCacheList() {
        return cacheList;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("Sheet2 {} 条数据，开始存储数据库！", cacheList.size());
        log.info("存储数据库成功！");
    }
}
