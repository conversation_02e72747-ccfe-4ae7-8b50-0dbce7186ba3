<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.planList.mapper.PlanVersionMapper">
    
    <resultMap type="PlanVersion" id="PlanVersionResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="versionCode"    column="version_code"    />
        <result property="versionDesc"    column="version_desc"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanVersionVo">
        select id, org_id, version_code, version_desc, remark, status, create_by, create_time, update_by, update_time from dsa_plan_version
    </sql>

    <select id="selectPlanVersionList" parameterType="PlanVersion" resultMap="PlanVersionResult">
        <include refid="selectPlanVersionVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="versionCode != null  and versionCode != ''"> and version_code = #{versionCode}</if>
            <if test="versionDesc != null  and versionDesc != ''"> and version_desc = #{versionDesc}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPlanVersionById" parameterType="Long" resultMap="PlanVersionResult">
        <include refid="selectPlanVersionVo"/>
        where id = #{id}
    </select>

    <insert id="insertPlanVersion" parameterType="PlanVersion" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_plan_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="versionCode != null and versionCode != ''">version_code,</if>
            <if test="versionDesc != null">version_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="versionCode != null and versionCode != ''">#{versionCode},</if>
            <if test="versionDesc != null">#{versionDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanVersion" parameterType="PlanVersion">
        update dsa_plan_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="versionCode != null and versionCode != ''">version_code = #{versionCode},</if>
            <if test="versionDesc != null">version_desc = #{versionDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanVersionById" parameterType="Long">
        delete from dsa_plan_version where id = #{id}
    </delete>

    <delete id="deletePlanVersionByIds" parameterType="String">
        delete from dsa_plan_version where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>