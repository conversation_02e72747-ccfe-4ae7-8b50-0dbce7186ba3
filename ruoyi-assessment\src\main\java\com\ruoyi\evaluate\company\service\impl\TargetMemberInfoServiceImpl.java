package com.ruoyi.evaluate.company.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.company.mapper.TargetMemberInfoMapper;
import com.ruoyi.evaluate.company.domain.TargetMemberInfo;
import com.ruoyi.evaluate.company.service.ITargetMemberInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 被评估单位人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class TargetMemberInfoServiceImpl extends ServiceImpl<TargetMemberInfoMapper, TargetMemberInfo> implements ITargetMemberInfoService {

}
