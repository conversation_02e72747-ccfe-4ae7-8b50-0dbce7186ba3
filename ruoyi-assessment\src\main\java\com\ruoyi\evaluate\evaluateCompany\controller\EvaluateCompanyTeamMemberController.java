package com.ruoyi.evaluate.evaluateCompany.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import com.ruoyi.evaluate.evaluateCompany.enums.TeamGroupEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyTeamMember;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyTeamMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估单位评估团队成员Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/evaluateCompany/member")
@Api(value = "评估单位评估团队成员控制器", tags = {"评估单位评估团队成员管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateCompanyTeamMemberController extends BaseController {
    private final IEvaluateCompanyTeamMemberService evaluateCompanyTeamMemberService;

    /**
     * 查询评估单位评估团队成员列表
     */
    @ApiOperation("查询评估单位评估团队成员列表")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:member:list')")
    @GetMapping("/list")
    public AjaxResult list(@Validated(ListGroup.class) EvaluateCompanyTeamMember evaluateCompanyTeamMember) {
        List<EvaluateCompanyTeamMember> list = evaluateCompanyTeamMemberService.list(new QueryWrapper<EvaluateCompanyTeamMember>(evaluateCompanyTeamMember).orderByDesc("id"));
        list.forEach(item -> {
            // 根据group字段设置groupName
            item.setGroupName(TeamGroupEnum.getNameByCode(item.getGroupId()));
        });
        return AjaxResult.success("请求成功", list);
    }

    /**
     * 获取评估单位评估团队成员详细信息
     */
    @ApiOperation("获取评估单位评估团队成员详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateCompanyTeamMemberService.getById(id));
    }

    /**
     * 新增评估单位评估团队成员
     */
    @ApiOperation("新增评估单位评估团队成员")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:member:add')")
    @Log(title = "评估单位评估团队成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody EvaluateCompanyTeamMember evaluateCompanyTeamMember) {
        return toAjax(evaluateCompanyTeamMemberService.save(evaluateCompanyTeamMember));
    }

    /**
     * 修改评估单位评估团队成员
     */
    @ApiOperation("修改评估单位评估团队成员")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:member:edit')")
    @Log(title = "评估单位评估团队成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated(EditGroup.class) @RequestBody EvaluateCompanyTeamMember evaluateCompanyTeamMember) {
        return toAjax(evaluateCompanyTeamMemberService.updateById(evaluateCompanyTeamMember));
    }

    /**
     * 删除评估单位评估团队成员
     */
    @ApiOperation("删除评估单位评估团队成员")
    @PreAuthorize("@ss.hasPermi('evaluateCompany:member:remove')")
    @Log(title = "评估单位评估团队成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateCompanyTeamMemberService.removeByIds(Arrays.asList(ids)));
    }
}