package com.ruoyi.evaluate.evaluateCompany.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Excel团队成员导入VO
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class ExcelTeamMemberVo {

    /** 序号 - A列 */
    @ExcelProperty(value = "序号", index = 0)
    private String serialNumber;

    /** 角色 - B列 */
    @ExcelProperty(value = "角色", index = 1)
    private String role;

    /** 职责 - C列 */
    @ExcelProperty(value = "职责", index = 2)
    private String duty;

    /** 姓名 - D列 */
    @ExcelProperty(value = "姓名", index = 3)
    private String name;

    /** 单位 - E列 */
    @ExcelProperty(value = "单位", index = 4)
    private String unit;

    /** 岗位 - F列 */
    @ExcelProperty(value = "岗位", index = 5)
    private String position;

    /** 能力资质 - G列 */
    @ExcelProperty(value = "能力资质", index = 6)
    private String ability;

    /** 评估工作经验 - H列 */
    @ExcelProperty(value = "评估工作经验", index = 7)
    private String experience;

    /** 分组名称（从Excel中读取，用于匹配groupId） */
    private String groupName;

    /** 行号（用于错误定位） */
    private Integer rowNumber;
}
