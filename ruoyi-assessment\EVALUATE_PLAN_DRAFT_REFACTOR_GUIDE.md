# 评估计划暂存功能重构指南

## 概述

本次重构将评估计划暂存功能进行了全面优化，引入了实体类参数校验、服务层封装等最佳实践，提高了代码的可维护性和复用性。

## 重构内容

### 1. 新增实体类

#### DraftStepRequest - 暂存步骤请求参数
```java
@Data
@Accessors(chain = true)
@ApiModel(description = "暂存步骤请求参数")
public class DraftStepRequest {
    @NotNull(message = "评估计划ID不能为空")
    @Positive(message = "评估计划ID必须为正数")
    private Long planId;

    @NotBlank(message = "步骤名称不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,50}$", message = "步骤名称只能包含字母、数字、下划线和横线，长度1-50位")
    private String step;
}
```

#### DraftSaveRequest - 暂存保存请求参数
```java
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "暂存保存请求参数")
public class DraftSaveRequest extends DraftStepRequest {
    @NotNull(message = "步骤数据不能为空")
    private Object stepData;

    private String dataDescription;
}
```

### 2. 新增服务层

#### IEvaluatePlanDraftService - 暂存服务接口
提供了完整的暂存操作方法，包括：
- `saveDraft` - 暂存数据
- `getDraft` - 获取暂存数据
- `deleteDraft` - 删除暂存数据
- `existsDraft` - 检查暂存数据是否存在
- `createFromDraft` - 从暂存数据创建任务
- `deleteStepDraft` - 删除上一步暂存数据
- `batchGetDrafts` - 批量获取暂存数据
- `batchDeleteDrafts` - 批量删除暂存数据
- `getUserDraftSummary` - 获取用户暂存数据摘要
- `clearUserDrafts` - 清理用户所有暂存数据
- `batchSaveDrafts` - 批量暂存数据

#### EvaluatePlanDraftServiceImpl - 暂存服务实现
- 完整的参数校验
- 详细的日志记录
- 统一的异常处理
- 业务逻辑封装

### 3. 控制器重构

控制器现在变得非常简洁，主要职责是：
- 参数校验（通过`@Valid`注解）
- 调用服务层方法
- 返回统一的响应格式

## API接口变更

### 1. 暂存数据接口
**变更前**:
```javascript
POST /evaluatePlan/task/draft/save?planId=123&currentStep=step1
{
  "formData": {...}
}
```

**变更后**:
```javascript
POST /evaluatePlan/task/draft/save
{
  "planId": 123,
  "step": "step1",
  "stepData": {
    "formData": {...}
  },
  "dataDescription": "基本信息填写"
}
```

### 2. 获取暂存数据接口
**变更前**:
```javascript
GET /evaluatePlan/task/draft/get?planId=123&step=step1
```

**变更后**:
```javascript
GET /evaluatePlan/task/draft/get?planId=123&step=step1
```
*（参数格式保持不变，但现在有了校验）*

### 3. 其他接口
其他接口的调用方式基本保持不变，但现在都有了完整的参数校验和错误处理。

## 参数校验

### 1. 自动校验
所有接口都通过`@Valid`注解自动进行参数校验：

```java
@PostMapping("/save")
public AjaxResult saveDraft(@Valid @RequestBody DraftSaveRequest request) {
    // 如果参数不符合要求，会自动返回400错误
}
```

### 2. 校验规则
- **planId**: 不能为空，必须为正数
- **step**: 不能为空，只能包含字母、数字、下划线和横线，长度1-50位
- **stepData**: 不能为空（仅保存接口）

### 3. 错误响应示例
```json
{
  "code": 400,
  "msg": "参数校验失败",
  "data": {
    "planId": "评估计划ID不能为空",
    "step": "步骤名称只能包含字母、数字、下划线和横线，长度1-50位"
  }
}
```

## 服务层调用示例

### 1. 在其他服务中调用暂存功能
```java
@Service
public class SomeOtherService {
    
    @Autowired
    private IEvaluatePlanDraftService draftService;
    
    public void someMethod() {
        // 创建请求参数
        DraftSaveRequest request = new DraftSaveRequest()
                .setPlanId(123L)
                .setStep("step1")
                .setStepData(someData)
                .setDataDescription("自动保存");
        
        // 调用暂存服务
        Map<String, Object> result = draftService.saveDraft(userId, request);
        
        // 处理结果
        if ((Boolean) result.get("success")) {
            log.info("暂存成功：{}", result.get("draftKey"));
        }
    }
}
```

### 2. 批量操作示例
```java
// 批量获取多个步骤的暂存数据
List<String> steps = Arrays.asList("step1", "step2", "step3");
Map<String, Object> result = draftService.batchGetDrafts(userId, planId, steps);

// 批量删除多个步骤的暂存数据
Map<String, Object> deleteResult = draftService.batchDeleteDrafts(userId, planId, steps);
```

## 优势

### 1. 代码质量提升
- **参数校验**: 统一的参数校验，减少重复代码
- **异常处理**: 统一的异常处理机制
- **日志记录**: 详细的操作日志，便于问题排查

### 2. 可维护性提升
- **职责分离**: 控制器专注于HTTP处理，服务层专注于业务逻辑
- **代码复用**: 服务层方法可以在其他地方调用
- **类型安全**: 强类型参数，减少运行时错误

### 3. 开发效率提升
- **自动校验**: 减少手动参数校验代码
- **统一响应**: 统一的返回格式，便于前端处理
- **文档完善**: 完整的API文档和使用示例

## 迁移指南

### 1. 前端代码修改
主要是`saveDraft`接口的调用方式需要调整：

**修改前**:
```javascript
const response = await fetch('/evaluatePlan/task/draft/save?planId=123&currentStep=step1', {
    method: 'POST',
    body: JSON.stringify(stepData)
});
```

**修改后**:
```javascript
const response = await fetch('/evaluatePlan/task/draft/save', {
    method: 'POST',
    body: JSON.stringify({
        planId: 123,
        step: 'step1',
        stepData: stepData,
        dataDescription: '基本信息填写'
    })
});
```

### 2. 后端代码修改
如果有其他地方直接调用了控制器方法，建议改为调用服务层方法：

**修改前**:
```java
// 直接调用控制器（不推荐）
controller.saveDraft(planId, step, data);
```

**修改后**:
```java
// 调用服务层（推荐）
DraftSaveRequest request = new DraftSaveRequest()
        .setPlanId(planId)
        .setStep(step)
        .setStepData(data);
draftService.saveDraft(userId, request);
```

## 测试建议

### 1. 参数校验测试
```java
@Test
void testSaveDraft_InvalidParams() {
    DraftSaveRequest request = new DraftSaveRequest()
            .setPlanId(-1L)  // 无效的planId
            .setStep("")     // 空的step
            .setStepData(null); // 空的stepData
    
    // 应该抛出参数校验异常
    assertThrows(IllegalArgumentException.class, () -> {
        draftService.saveDraft(userId, request);
    });
}
```

### 2. 业务逻辑测试
```java
@Test
void testSaveDraft_Success() {
    DraftSaveRequest request = new DraftSaveRequest()
            .setPlanId(123L)
            .setStep("step1")
            .setStepData(testData);
    
    Map<String, Object> result = draftService.saveDraft(userId, request);
    
    assertTrue((Boolean) result.get("success"));
    assertEquals("123_step1", result.get("draftKey"));
}
```

## 总结

本次重构大大提升了评估计划暂存功能的代码质量和可维护性，通过引入实体类、参数校验、服务层封装等最佳实践，使代码更加健壮和易于扩展。同时保持了API接口的基本兼容性，降低了迁移成本。
