-- 暂存功能配置SQL脚本
-- 用于初始化暂存功能相关的系统配置

-- 插入暂存功能配置项
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES
('暂存功能开关', 'dsa.draft.enabled', '1', 'Y', 'admin', NOW(), 'admin', NOW(), '暂存功能总开关，1-启用，0-禁用'),
('默认过期时间', 'dsa.draft.default.expire.hours', '24', 'Y', 'admin', NOW(), 'admin', NOW(), '暂存数据默认过期时间（小时）'),
('最大过期时间', 'dsa.draft.max.expire.days', '30', 'Y', 'admin', NOW(), 'admin', NOW(), '暂存数据最大过期时间（天）'),
('单用户最大暂存条数', 'dsa.draft.max.count.per.user', '100', 'Y', 'admin', NOW(), 'admin', NOW(), '单个用户最大暂存数据条数'),
('单条数据最大大小', 'dsa.draft.max.size.mb', '1', 'Y', 'admin', NOW(), 'admin', NOW(), '单条暂存数据最大大小（MB）'),
('自动清理开关', 'dsa.draft.auto.cleanup.enabled', '1', 'Y', 'admin', NOW(), 'admin', NOW(), '自动清理过期数据开关，1-启用，0-禁用'),
('自动清理间隔', 'dsa.draft.auto.cleanup.interval.hours', '6', 'Y', 'admin', NOW(), 'admin', NOW(), '自动清理过期数据的间隔时间（小时）')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
update_by = VALUES(update_by),
update_time = VALUES(update_time),
remark = VALUES(remark);

-- 插入暂存功能相关权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('暂存管理', 0, 6, 'draft', NULL, 1, 0, 'M', '0', '0', NULL, 'documentation', 'admin', NOW(), 'admin', NOW(), '暂存功能管理菜单')
ON DUPLICATE KEY UPDATE 
menu_name = VALUES(menu_name),
update_by = VALUES(update_by),
update_time = VALUES(update_time);

-- 获取暂存管理菜单ID（用于子菜单）
SET @draft_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '暂存管理' AND parent_id = 0);

-- 插入通用暂存权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('通用暂存保存', @draft_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'common:draft:save', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存保存权限'),
('通用暂存查询', @draft_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'common:draft:query', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存查询权限'),
('通用暂存删除', @draft_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'common:draft:remove', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存删除权限'),
('通用暂存列表', @draft_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'common:draft:list', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存列表权限'),
('通用暂存清理', @draft_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'common:draft:clear', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存清理权限'),
('通用暂存编辑', @draft_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'common:draft:edit', '#', 'admin', NOW(), 'admin', NOW(), '通用暂存编辑权限'),
('评估计划任务暂存', @draft_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'evaluatePlan:task:draft', '#', 'admin', NOW(), 'admin', NOW(), '评估计划任务暂存权限')
ON DUPLICATE KEY UPDATE
perms = VALUES(perms),
update_by = VALUES(update_by),
update_time = VALUES(update_time);

-- 为管理员角色分配暂存权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE '%:draft:%' OR perms = 'evaluatePlan:task:draft'
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 创建暂存数据清理任务（可选）
-- 注意：这需要根据实际的定时任务表结构进行调整
/*
INSERT INTO sys_job (job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark) VALUES
('暂存数据清理', 'SYSTEM', 'draftCleanupTask.cleanExpiredDrafts', '0 0 */6 * * ?', '2', '1', '1', 'admin', NOW(), 'admin', NOW(), '每6小时清理一次过期的暂存数据')
ON DUPLICATE KEY UPDATE 
cron_expression = VALUES(cron_expression),
update_by = VALUES(update_by),
update_time = VALUES(update_time);
*/

-- 查询配置结果
SELECT config_name, config_key, config_value, remark 
FROM sys_config 
WHERE config_key LIKE 'dsa.draft.%' 
ORDER BY config_key;
