-- 为dsa_evaluate_model表添加is_enabled字段
-- 用于标识每个type_id下当前启用的模型（每个type_id只能有一个模型启用）

-- 添加is_enabled字段
ALTER TABLE `dsa_evaluate_model` 
ADD COLUMN `is_enabled` int(1) DEFAULT 0 COMMENT '是否为当前启用的模型，1-启用 0-未启用（每个type_id下只能有一个模型启用）' 
AFTER `status`;

-- 为每个type_id设置一个默认启用的模型（选择最新的一个）
UPDATE `dsa_evaluate_model` m1 
SET `is_enabled` = 1 
WHERE m1.id = (
    SELECT m2.id 
    FROM (
        SELECT id, type_id, 
               ROW_NUMBER() OVER (PARTITION BY type_id ORDER BY id DESC) as rn
        FROM `dsa_evaluate_model` 
        WHERE status = 1 AND del_flag = 0
    ) m2 
    WHERE m2.type_id = m1.type_id AND m2.rn = 1
);

-- 创建索引以提高查询性能
CREATE INDEX `idx_type_enabled` ON `dsa_evaluate_model` (`type_id`, `is_enabled`);

-- 验证数据
SELECT 
    type_id,
    COUNT(*) as total_models,
    SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_models
FROM `dsa_evaluate_model` 
WHERE status = 1 AND del_flag = 0
GROUP BY type_id
ORDER BY type_id;
