# Docker Compose 覆盖配置文件
# 用于开发环境的特殊配置

version: '3.8'

services:
  # 开发环境下的应用配置
  ruoyi-app:
    environment:
      # 开发环境使用dev配置
      SPRING_PROFILES_ACTIVE: dev
      # 开发环境数据库配置
      SPRING_DATASOURCE_URL: ***********************/${MYSQL_DATABASE}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
      SPRING_DATASOURCE_USERNAME: ${MYSQL_USER}
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD}
      # 开发环境Redis配置
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
      SPRING_REDIS_DATABASE: 9
    volumes:
      # 开发环境可以挂载JAR包进行热更新
      - ./ruoyi_admin.jar:/app/app.jar
    ports:
      # 开发环境可能需要调试端口
      - "8080:8080"
      - "5005:5005"  # JVM调试端口
