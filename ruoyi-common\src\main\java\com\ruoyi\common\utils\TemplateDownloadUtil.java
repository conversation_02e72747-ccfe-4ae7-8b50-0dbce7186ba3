package com.ruoyi.common.utils;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

public class TemplateDownloadUtil {

    /**
     * 通用模板下载
     * @param response HttpServletResponse
     * @param templatePath 模板文件绝对路径
     * @param fileName 下载时显示的文件名
     */
    public static void downloadTemplate(HttpServletResponse response, String templatePath, String fileName) {
        try (InputStream is = FileUtil.getInputStream(templatePath)) {
            if (is == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // filename 用 ISO-8859-1 编码（部分浏览器支持）
            String userAgent = response.getHeader("User-Agent");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            String contentDisposition;
            if (userAgent != null && userAgent.contains("MSIE")) {
                // IE浏览器
                String isoFileName = new String(fileName.getBytes("GBK"), "ISO-8859-1");
                contentDisposition = "attachment; filename=\"" + isoFileName + "\"";
            } else if (userAgent != null && userAgent.contains("Firefox")) {
                // 火狐浏览器
                contentDisposition = "attachment; filename*=UTF-8''" + encodedFileName;
            } else {
                // 其他浏览器（Chrome、Edge等）
                contentDisposition = "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName;
            }
            response.setHeader("Content-Disposition", contentDisposition);
            IOUtils.copy(is, response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}