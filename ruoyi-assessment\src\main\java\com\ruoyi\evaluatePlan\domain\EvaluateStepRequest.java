package com.ruoyi.evaluatePlan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

/**
 * 评估步骤任务执行请求对象
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class EvaluateStepRequest {

    /** 计划ID */
    @NotNull(message = "计划ID不能为空")
    private Long planId;

    /** 评估类型编码 */
    @NotBlank(message = "评估类型不能为空")
    private String evaluateType;

    /** 步骤编码 */
    @NotBlank(message = "步骤编码不能为空")
    private String stepCode;

    /** 流程实例ID */
    private Long processInstanceId;

    /** 步骤名称 */
    private String stepName;

    /** 步骤描述 */
    private String stepDescription;

    /** 被评估单位ID */
    private Long targetCompanyId;

    /** 评估单位ID */
    private Long evaluateCompanyId;

    /** 步骤参数 */
    private Map<String, Object> stepParams;

    /** 步骤配置 */
    private Map<String, Object> stepConfig;

    /** 执行模式 manual-手动 auto-自动 */
    private String executeMode = "manual";

    /** 是否强制执行（跳过前置条件检查） */
    private Boolean forceExecute = false;

    /** 是否跳过当前步骤 */
    private Boolean skipStep = false;

    /** 超时时间（秒） */
    private Integer timeout;

    /** 重试次数 */
    private Integer retryCount = 0;

    /** 扩展参数 */
    private Map<String, Object> extParams;

    /** 操作人 */
    private String operator;

    /** 执行备注 */
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
