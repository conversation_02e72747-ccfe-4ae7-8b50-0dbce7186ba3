package com.ruoyi.evaluate.company.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import com.ruoyi.evaluate.company.domain.TargetCompanyInfo;
import com.ruoyi.evaluate.company.service.ITargetCompanyInfoService;

import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 被评估单位Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/targetCompany/companyInfo")
@Api(value = "被评估单位控制器", tags = {"被评估单位管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TargetCompanyInfoController extends BaseController
{
    private final ITargetCompanyInfoService targetCompanyInfoService;

    /**
     * 查询被评估单位列表
     */
    @ApiOperation("查询被评估单位列表")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TargetCompanyInfo targetCompanyInfo) {
        startPage();
        List<TargetCompanyInfo> list = targetCompanyInfoService.list(new QueryWrapper<TargetCompanyInfo>(targetCompanyInfo));
        return getDataTable(list);
    }

    /**
     * 导出被评估单位列表
     */
    // @ApiOperation("导出被评估单位列表")
    // @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:export')")
    // @Log(title = "被评估单位", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response,TargetCompanyInfo targetCompanyInfo) {
    //     List<TargetCompanyInfo> list = targetCompanyInfoService.list(new QueryWrapper<TargetCompanyInfo>(targetCompanyInfo));
    //     ExcelUtil<TargetCompanyInfo> util = new ExcelUtil<TargetCompanyInfo>(TargetCompanyInfo.class);
    //     util.exportExcel(response,list, "被评估单位数据");
    // }

    /**
     * 获取被评估单位详细信息
     */
    @ApiOperation("获取被评估单位详细信息")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(targetCompanyInfoService.getById(id));
    }

    /**
     * 新增被评估单位
     */
    @ApiOperation("新增被评估单位")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:add')")
    @Log(title = "被评估单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TargetCompanyInfo targetCompanyInfo) {
        return toAjax(targetCompanyInfoService.save(targetCompanyInfo));
    }

    /**
     * 修改被评估单位
     */
    @ApiOperation("修改被评估单位")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:edit')")
    @Log(title = "被评估单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TargetCompanyInfo targetCompanyInfo) {
        return toAjax(targetCompanyInfoService.updateById(targetCompanyInfo));
    }

    /**
     * 删除被评估单位
     */
    @ApiOperation("删除被评估单位")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:remove')")
    @Log(title = "被评估单位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(targetCompanyInfoService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取导入被评估单位信息模板
     *
     * @param response
     */
    @ApiOperation("获取导入被评估单位信息模板，文件流形式")
    @GetMapping("/getExcelTemplate")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/target_company/target_company.zip";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "数据安全风险评估-基本信息调研表A.zip");
    }

    /**
     * 导入ZIP压缩包（包含Excel文件和引用资源）
     *
     * ZIP压缩包要求：
     * 1. 必须包含一个主Excel文件（.xlsx/.xls格式）
     * 2. Excel文件包含5个sheet：
     *    - Sheet0: 公司信息的另一部分
     *    - Sheet1: 公司信息的一部分
     *    - Sheet2: 部门信息
     *    - Sheet3: 人员信息
     *    - Sheet4: 文档信息
     * 3. 可包含Excel引用的资源文件（图片、文档等）
     * 4. ZIP文件大小不超过100MB，解压后不超过500MB
     */
    @ApiOperation("导入ZIP压缩包")
    @PreAuthorize("@ss.hasPermi('targetCompany:companyInfo:import')")
    @Log(title = "ZIP压缩包导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importZip")
    public AjaxResult importZip(@RequestParam("file") MultipartFile file) throws Exception {
        // 检查文件是否为空
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }

        // 检查文件类型，只允许ZIP格式
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return AjaxResult.error("文件名不能为空");
        }

        if (!originalFilename.toLowerCase().endsWith(".zip")) {
            return AjaxResult.error("只支持ZIP压缩包格式，请上传.zip文件");
        }

        try {
            Map<String, Object> result = targetCompanyInfoService.importZip(file);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("导入失败：" + e.getMessage());
        }
    }


}