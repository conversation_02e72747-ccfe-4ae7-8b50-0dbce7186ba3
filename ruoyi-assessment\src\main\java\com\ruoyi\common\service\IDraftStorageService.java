package com.ruoyi.common.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用暂存服务接口
 * 提供基于Redis的数据暂存功能，支持任意类型数据的临时存储
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IDraftStorageService {

    /**
     * 暂存数据（使用默认过期时间）
     *
     * @param businessType 业务类型（如：evaluate_plan_task）
     * @param userId 用户ID
     * @param draftKey 暂存键（业务内唯一标识）
     * @param data 要暂存的数据
     * @param <T> 数据类型
     * @return 暂存成功返回true
     */
    <T> boolean saveDraft(String businessType, Long userId, String draftKey, T data);

    /**
     * 暂存数据（指定过期时间）
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @param data 要暂存的数据
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @param <T> 数据类型
     * @return 暂存成功返回true
     */
    <T> boolean saveDraft(String businessType, Long userId, String draftKey, T data, 
                         long timeout, TimeUnit timeUnit);

    /**
     * 获取暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @param clazz 数据类型
     * @param <T> 数据类型
     * @return 暂存的数据，不存在返回null
     */
    <T> T getDraft(String businessType, Long userId, String draftKey, Class<T> clazz);

    /**
     * 删除暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @return 删除成功返回true
     */
    boolean deleteDraft(String businessType, Long userId, String draftKey);

    /**
     * 获取用户在指定业务类型下的所有暂存键
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @return 暂存键列表
     */
    List<String> getUserDraftKeys(String businessType, Long userId);

    /**
     * 获取用户在指定业务类型下的所有暂存数据摘要
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @return 暂存数据摘要Map，key为draftKey，value为元数据
     */
    Map<String, Map<String, Object>> getUserDraftSummary(String businessType, Long userId);

    /**
     * 清理用户在指定业务类型下的所有暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @return 清理的数据条数
     */
    int clearUserDrafts(String businessType, Long userId);

    /**
     * 清理指定业务类型下的所有过期暂存数据
     *
     * @param businessType 业务类型
     * @return 清理的数据条数
     */
    int clearExpiredDrafts(String businessType);

    /**
     * 检查暂存数据是否存在
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @return 存在返回true
     */
    boolean existsDraft(String businessType, Long userId, String draftKey);

    /**
     * 获取暂存数据的剩余过期时间
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @return 剩余时间（秒），-1表示永不过期，-2表示不存在
     */
    long getDraftTtl(String businessType, Long userId, String draftKey);

    /**
     * 更新暂存数据的过期时间
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKey 暂存键
     * @param timeout 新的过期时间
     * @param timeUnit 时间单位
     * @return 更新成功返回true
     */
    boolean updateDraftTtl(String businessType, Long userId, String draftKey, 
                          long timeout, TimeUnit timeUnit);

    /**
     * 批量暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param drafts 暂存数据Map，key为draftKey，value为数据
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @return 成功暂存的数据条数
     */
    int batchSaveDrafts(String businessType, Long userId, Map<String, Object> drafts, 
                       long timeout, TimeUnit timeUnit);

    /**
     * 批量获取暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKeys 暂存键列表
     * @param clazz 数据类型
     * @param <T> 数据类型
     * @return 暂存数据Map，key为draftKey，value为数据
     */
    <T> Map<String, T> batchGetDrafts(String businessType, Long userId, 
                                     List<String> draftKeys, Class<T> clazz);

    /**
     * 批量删除暂存数据
     *
     * @param businessType 业务类型
     * @param userId 用户ID
     * @param draftKeys 暂存键列表
     * @return 成功删除的数据条数
     */
    int batchDeleteDrafts(String businessType, Long userId, List<String> draftKeys);
}
