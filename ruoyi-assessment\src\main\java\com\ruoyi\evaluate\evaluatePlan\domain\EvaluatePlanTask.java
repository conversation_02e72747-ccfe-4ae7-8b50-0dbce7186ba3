package com.ruoyi.evaluate.evaluatePlan.domain;

    import java.math.BigDecimal;
    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 评估计划任务对象 dsa_evaluate_plan_task
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_plan_task")
public class EvaluatePlanTask extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 评估单位ID */
    @Excel(name = "评估单位ID")
    @TableField(value = "evaluate_org_id")
    private Long evaluateOrgId;

    /** 评估类型ID */
    @Excel(name = "评估类型")
    @TableField(value = "evaluate_type")
    private String evaluateType;

    /** 评估模型id */
    @Excel(name = "评估模型id")
    @TableField(value = "model_id")
    private Long modelId;

    /** 报告编号 */
    @Excel(name = "报告编号")
    @TableField(value = "report_no")
    private String reportNo;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @TableField(value = "name")
    private String name;

    /** 计划开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "计划开始日期" , width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "plan_start_date")
    private Date planStartDate;

    /** 计划结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "计划结束日期" , width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "plan_end_date")
    private Date planEndDate;

    /** 实际开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "实际开始日期" , width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "real_start_date")
    private Date realStartDate;

    /** 实际结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "实际结束日期" , width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "real_end_date")
    private Date realEndDate;

    /** 截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "截止日期" , width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "deadline")
    private Date deadline;

    /** 模式，1-手动模式 2-批量模式 */
    @Excel(name = "模式，1-手动模式 2-批量模式")
    @TableField(value = "mode")
    private Integer mode;

    /** 任务状态（NOT_STARTED-未开始，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消） */
    @Excel(name = "任务状态", readConverterExp = "NOT_STARTED-未开始，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消")
    @TableField(value = "task_status")
    private String taskStatus;

    /** 负责人 */
    @Excel(name = "负责人")
    @TableField(value = "master_user")
    private String masterUser;

    /** 负责部门 */
    @Excel(name = "负责部门")
    @TableField(value = "master_dept")
    private String masterDept;

    /** 任务描述 */
    @Excel(name = "任务描述")
    @TableField(value = "task_description")
    private String taskDescription;

    /** 完成率（0.00-100.00） */
    @Excel(name = "完成率")
    @TableField(value = "completion_rate")
    private BigDecimal completionRate;

    /** 预估工时（小时） */
    @Excel(name = "预估工时")
    @TableField(value = "plan_hours")
    private BigDecimal planHours;

    /** 实际工时（小时） */
    @Excel(name = "实际工时")
    @TableField(value = "real_hours")
    private BigDecimal realHours;

    /** 父任务ID（用于任务层级关系） */
    @Excel(name = "父任务ID")
    @TableField(value = "origin_task_id")
    private Long originTaskId;

    /** 版本ID */
    @Excel(name = "版本ID")
    @TableField(value = "version_id")
    private Long versionId;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;







}