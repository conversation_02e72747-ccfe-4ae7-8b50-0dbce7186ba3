# 通用暂存功能使用指南

## 概述

通用暂存功能提供了一个基于Redis的数据临时存储解决方案，支持任意类型数据的暂存、获取、删除等操作。该功能特别适用于表单数据的临时保存、草稿功能等场景。

## 核心组件

### 1. 服务接口
- `IDraftStorageService`: 通用暂存服务接口
- `DraftStorageServiceImpl`: 基于Redis的实现

### 2. 数据传输对象
- `DraftData<T>`: 暂存数据封装类
- `DraftMetadata`: 暂存数据元数据

### 3. 常量定义
- `DraftConstants`: 暂存功能相关常量

## 功能特性

### 1. 基本功能
- ✅ 数据暂存（支持自定义过期时间）
- ✅ 数据获取（自动类型转换）
- ✅ 数据删除
- ✅ 批量操作
- ✅ 用户隔离
- ✅ 业务类型分类

### 2. 高级功能
- ✅ 自动过期清理
- ✅ 数据大小限制
- ✅ 用户暂存条数限制
- ✅ 配置化管理
- ✅ 详细的操作日志

## 使用方法

### 1. 基本使用

```java
@Autowired
private IDraftStorageService draftStorageService;

// 暂存数据
EvaluatePlanTaskDto taskDto = new EvaluatePlanTaskDto();
taskDto.setName("测试任务");

boolean success = draftStorageService.saveDraft(
    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
    userId,
    "my_draft_001",
    taskDto
);

// 获取数据
EvaluatePlanTaskDto retrieved = draftStorageService.getDraft(
    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
    userId,
    "my_draft_001",
    EvaluatePlanTaskDto.class
);

// 删除数据
boolean deleted = draftStorageService.deleteDraft(
    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
    userId,
    "my_draft_001"
);
```

### 2. 指定过期时间

```java
// 暂存数据，1小时后过期
draftStorageService.saveDraft(
    businessType,
    userId,
    draftKey,
    data,
    1,
    TimeUnit.HOURS
);
```

### 3. 批量操作

```java
// 批量暂存
Map<String, Object> drafts = new HashMap<>();
drafts.put("draft1", data1);
drafts.put("draft2", data2);

int savedCount = draftStorageService.batchSaveDrafts(
    businessType,
    userId,
    drafts,
    24,
    TimeUnit.HOURS
);

// 批量获取
List<String> keys = Arrays.asList("draft1", "draft2");
Map<String, MyDataType> results = draftStorageService.batchGetDrafts(
    businessType,
    userId,
    keys,
    MyDataType.class
);
```

### 4. 管理功能

```java
// 获取用户所有暂存键
List<String> userDraftKeys = draftStorageService.getUserDraftKeys(businessType, userId);

// 获取暂存数据摘要
Map<String, Map<String, Object>> summary = draftStorageService.getUserDraftSummary(businessType, userId);

// 清理用户所有暂存数据
int clearedCount = draftStorageService.clearUserDrafts(businessType, userId);

// 检查数据是否存在
boolean exists = draftStorageService.existsDraft(businessType, userId, draftKey);

// 获取剩余过期时间
long ttl = draftStorageService.getDraftTtl(businessType, userId, draftKey);
```

## Controller集成示例

暂存功能已经独立为两个控制器：

### 1. 通用暂存控制器 (`DraftStorageController`)

提供通用的暂存功能，支持任意业务类型：

```java
// 暂存数据
POST /common/draft/save?businessType=xxx&draftKey=xxx&expireHours=24

// 获取数据
GET /common/draft/get?businessType=xxx&draftKey=xxx&dataType=Object

// 删除数据
DELETE /common/draft/delete?businessType=xxx&draftKey=xxx

// 获取暂存列表
GET /common/draft/list?businessType=xxx

// 清理所有暂存
DELETE /common/draft/clear?businessType=xxx

// 检查是否存在
GET /common/draft/exists?businessType=xxx&draftKey=xxx

// 批量暂存
POST /common/draft/batch/save?businessType=xxx&expireHours=24

// 批量删除
DELETE /common/draft/batch/delete?businessType=xxx

// 更新过期时间
POST /common/draft/updateTtl?businessType=xxx&draftKey=xxx&expireHours=48
```

### 2. 评估计划任务暂存控制器 (`EvaluatePlanTaskDraftController`)

专门处理评估计划任务的暂存操作：

```java
// 暂存数据
POST /evaluatePlan/task/draft/save?draftKey=xxx&expireHours=24

// 获取数据
GET /evaluatePlan/task/draft/get/{draftKey}

// 删除数据
DELETE /evaluatePlan/task/draft/delete/{draftKey}

// 获取暂存列表
GET /evaluatePlan/task/draft/list

// 清理所有暂存
DELETE /evaluatePlan/task/draft/clear

// 检查是否存在
GET /evaluatePlan/task/draft/exists/{draftKey}

// 从暂存创建任务
POST /evaluatePlan/task/draft/create/{draftKey}?deleteDraft=true

// 批量暂存
POST /evaluatePlan/task/draft/batch/save?expireHours=24

// 批量获取
POST /evaluatePlan/task/draft/batch/get

// 批量删除
DELETE /evaluatePlan/task/draft/batch/delete
```

## 配置项

在系统配置中可以设置以下参数：

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| `dsa.draft.enabled` | `1` | 暂存功能开关 |
| `dsa.draft.default.expire.hours` | `24` | 默认过期时间（小时） |
| `dsa.draft.max.expire.days` | `30` | 最大过期时间（天） |
| `dsa.draft.max.count.per.user` | `100` | 单用户最大暂存条数 |
| `dsa.draft.max.size.mb` | `1` | 单条数据最大大小（MB） |
| `dsa.draft.auto.cleanup.enabled` | `1` | 自动清理过期数据开关 |
| `dsa.draft.auto.cleanup.interval.hours` | `6` | 自动清理间隔（小时） |

## 业务类型

目前支持的业务类型：

- `evaluate_plan_task`: 评估计划任务
- `data_item`: 数据项管理
- `evaluate_report`: 评估报告

可以通过`DraftConstants`类添加新的业务类型。

## 注意事项

### 1. 数据安全
- 暂存数据按用户隔离，用户只能访问自己的数据
- 支持权限控制，需要相应的权限才能操作

#### 权限配置

**通用暂存控制器权限**：
- `common:draft:save` - 暂存数据权限
- `common:draft:query` - 查询暂存数据权限
- `common:draft:remove` - 删除暂存数据权限
- `common:draft:list` - 获取暂存列表权限
- `common:draft:clear` - 清理暂存数据权限
- `common:draft:edit` - 编辑暂存数据权限

**评估计划任务暂存控制器权限**：
- `evaluatePlan:task:draft` - 评估计划任务暂存权限
- `evaluatePlan:task:add` - 从暂存创建任务权限

### 2. 性能考虑
- 暂存数据存储在Redis中，访问速度快
- 建议合理设置过期时间，避免数据堆积
- 大数据量建议使用批量操作

### 3. 数据一致性
- 暂存数据不保证持久化，仅用于临时存储
- 重要数据应及时转换为正式数据

### 4. 错误处理
- 所有操作都有异常处理，不会影响主业务流程
- 详细的日志记录，便于问题排查

## 扩展指南

### 1. 添加新的业务类型

```java
// 在DraftConstants中添加新常量
public static final String BUSINESS_TYPE_NEW_FEATURE = "new_feature";
```

### 2. 自定义暂存数据处理

```java
// 继承DraftStorageServiceImpl，重写相关方法
@Service
public class CustomDraftStorageService extends DraftStorageServiceImpl {
    
    @Override
    public <T> boolean saveDraft(String businessType, Long userId, String draftKey, T data) {
        // 自定义处理逻辑
        return super.saveDraft(businessType, userId, draftKey, data);
    }
}
```

### 3. 添加新的Controller接口

有两种方式添加暂存功能：

**方式一：使用通用暂存控制器**
直接调用`/common/draft/*`接口，指定对应的`businessType`即可。

**方式二：创建专用暂存控制器**
参考`EvaluatePlanTaskDraftController`的实现，为特定业务创建专用的暂存控制器：

```java
@RestController
@RequestMapping("/yourModule/draft")
@Api(value = "您的模块暂存控制器", tags = {"您的模块暂存管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class YourModuleDraftController extends BaseController {

    private final IDraftStorageService draftStorageService;
    private final IYourModuleService yourModuleService;

    // 实现具体的暂存接口...
}
```

## 测试

项目包含完整的单元测试：

- `DraftStorageServiceTest`: 服务层测试
- `DraftStorageControllerTest`: 通用暂存控制器测试
- `EvaluatePlanTaskDraftControllerTest`: 评估计划任务暂存控制器测试

运行测试：

```bash
mvn test -Dtest=DraftStorageServiceTest
mvn test -Dtest=DraftStorageControllerTest
mvn test -Dtest=EvaluatePlanTaskDraftControllerTest
```

## 总结

通用暂存功能提供了一个完整、可靠、易用的数据临时存储解决方案。通过合理的架构设计和丰富的功能特性，可以满足各种业务场景的需求。
