package com.ruoyi.evaluate.evaluateCompany.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 评估单位信息对象 dsa_evaluate_company_info
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_company_info")
public class EvaluateCompanyInfo extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** ID */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 评估单位 */
    @Excel(name = "评估单位")
    @TableField(value = "evaluate_company")
    private String evaluateCompany;

    /** 机构代码 */
    @Excel(name = "机构代码")
    @TableField(value = "org_code")
    private String orgCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    @TableField(value = "address")
    private String address;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    @TableField(value = "postal_code")
    private String postalCode;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /** 联系人职务 */
    @Excel(name = "联系人职务")
    @TableField(value = "contact_job")
    private String contactJob;

    /** 联系人部门 */
    @Excel(name = "联系人部门")
    @TableField(value = "contact_dept")
    private String contactDept;

    /** 联系人办公电话 */
    @Excel(name = "联系人办公电话")
    @TableField(value = "contact_tel")
    private String contactTel;

    /** 联系人移动电话 */
    @Excel(name = "联系人移动电话")
    @TableField(value = "contact_phone")
    private String contactPhone;

    /** 联系人邮箱 */
    @Excel(name = "联系人邮箱")
    @TableField(value = "contact_email")
    private String contactEmail;

    /** 编制人签名图片 */
    @Excel(name = "编制人签名图片")
    @TableField(value = "editor_sign_img")
    private String editorSignImg;

    /** 审核人签名图片 */
    @Excel(name = "审核人签名图片")
    @TableField(value = "auditor_sign_img")
    private String auditorSignImg;

    /** 批准人签名图片 */
    @Excel(name = "批准人签名图片")
    @TableField(value = "approve_sign_img")
    private String approveSignImg;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}