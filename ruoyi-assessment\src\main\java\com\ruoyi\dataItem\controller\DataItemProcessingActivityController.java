package com.ruoyi.dataItem.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.ruoyi.common.group.ListGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataItem.domain.DataItemProcessingActivity;
import com.ruoyi.dataItem.service.IDataItemProcessingActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据处理活动Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/dataItem/processingActivity")
@Api(value = "数据处理活动控制器", tags = {"数据处理活动管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataItemProcessingActivityController extends BaseController
{
    private final IDataItemProcessingActivityService dataItemProcessingActivityService;

    /**
     * 查询数据处理活动列表
     */
    @ApiOperation("查询数据处理活动列表")
    @PreAuthorize("@ss.hasPermi('dataItem:processingActivity:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(ListGroup.class) DataItemProcessingActivity dataItemProcessingActivity) {
        startPage();
        List<DataItemProcessingActivity> list = dataItemProcessingActivityService.list(new QueryWrapper<DataItemProcessingActivity>(dataItemProcessingActivity));
        return getDataTable(list);
    }

    /**
     * 获取数据处理活动详细信息
     */
    @ApiOperation("获取数据处理活动详细信息")
    @PreAuthorize("@ss.hasPermi('dataItem:processingActivity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dataItemProcessingActivityService.getById(id));
    }

    /**
     * 新增数据处理活动
     */
    @ApiOperation("新增数据处理活动")
    @PreAuthorize("@ss.hasPermi('dataItem:processingActivity:add')")
    @Log(title = "数据处理活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody DataItemProcessingActivity dataItemProcessingActivity) {
        return toAjax(dataItemProcessingActivityService.save(dataItemProcessingActivity));
    }

    /**
     * 修改数据处理活动
     */
    @ApiOperation("修改数据处理活动")
    @PreAuthorize("@ss.hasPermi('dataItem:processingActivity:edit')")
    @Log(title = "数据处理活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataItemProcessingActivity dataItemProcessingActivity) {
        return toAjax(dataItemProcessingActivityService.updateById(dataItemProcessingActivity));
    }

    /**
     * 删除数据处理活动
     */
    @ApiOperation("删除数据处理活动")
    @PreAuthorize("@ss.hasPermi('dataItem:processingActivity:remove')")
    @Log(title = "数据处理活动", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dataItemProcessingActivityService.removeByIds(Arrays.asList(ids)));
    }
}