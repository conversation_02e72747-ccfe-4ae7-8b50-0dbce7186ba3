package com.ruoyi.process.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.process.mapper.ProcessStepInstanceMapper;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.service.IProcessStepInstanceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 流程步骤实例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class ProcessStepInstanceServiceImpl extends ServiceImpl<ProcessStepInstanceMapper, ProcessStepInstance> implements IProcessStepInstanceService {

}
