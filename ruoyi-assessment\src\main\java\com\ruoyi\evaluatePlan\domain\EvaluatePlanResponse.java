package com.ruoyi.evaluatePlan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 评估计划响应对象
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class EvaluatePlanResponse {

    /** 计划ID */
    private Long planId;

    /** 执行结果 true-成功 false-失败 */
    private Boolean success;

    /** 响应消息 */
    private String message;

    /** 响应码 */
    private String code;

    /** 评估进度 0-100 */
    private Integer progress;

    /** 评估状态 0-待开始 1-进行中 2-已完成 3-已暂停 */
    private Integer status;

    /** 评估结果数据 */
    private Map<String, Object> resultData;

    /** 评估步骤列表 */
    private List<EvaluateStep> steps;

    /** 生成的文件列表 */
    private List<String> generatedFiles;

    /** 响应时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date responseTime;

    /** 扩展数据 */
    private Map<String, Object> extData;

    /**
     * 评估步骤内部类
     */
    @Data
    @Accessors(chain = true)
    public static class EvaluateStep {
        /** 步骤名称 */
        private String stepName;
        
        /** 步骤状态 0-待执行 1-执行中 2-已完成 3-失败 */
        private Integer stepStatus;
        
        /** 步骤描述 */
        private String description;
        
        /** 执行时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date executeTime;
        
        /** 步骤结果 */
        private String result;
    }

    /**
     * 创建成功响应
     */
    public static EvaluatePlanResponse success(Long planId, String message) {
        return new EvaluatePlanResponse()
                .setPlanId(planId)
                .setSuccess(true)
                .setMessage(message)
                .setCode("200")
                .setResponseTime(new Date());
    }

    /**
     * 创建失败响应
     */
    public static EvaluatePlanResponse failure(Long planId, String message) {
        return new EvaluatePlanResponse()
                .setPlanId(planId)
                .setSuccess(false)
                .setMessage(message)
                .setCode("500")
                .setResponseTime(new Date());
    }
}
