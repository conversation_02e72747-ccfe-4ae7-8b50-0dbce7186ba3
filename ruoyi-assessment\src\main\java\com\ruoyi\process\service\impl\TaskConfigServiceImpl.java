package com.ruoyi.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.process.domain.ProcessDefinition;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.service.IProcessDefinitionService;
import com.ruoyi.process.service.IProcessStepDefinitionService;
import com.ruoyi.process.service.ITaskConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 任务配置服务实现类
 * 负责从流程定义中获取任务配置信息
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class TaskConfigServiceImpl implements ITaskConfigService {

    private static final String TASK_CONFIG_CACHE_KEY = "task_config:";
    private static final String PROCESS_STEPS_CACHE_KEY = "process_steps:";
    private static final Integer CACHE_EXPIRE_TIME = 30; // 30分钟

    @Autowired
    private IProcessDefinitionService processDefinitionService;

    @Autowired
    private IProcessStepDefinitionService processStepDefinitionService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public ProcessStepDefinition getTaskConfig(String processCode, String stepCode) {
        if (!StringUtils.hasText(processCode) || !StringUtils.hasText(stepCode)) {
            return null;
        }

        String cacheKey = TASK_CONFIG_CACHE_KEY + processCode + ":" + stepCode;
        ProcessStepDefinition cached = redisCache.getCacheObject(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 根据流程编码查找流程定义
        ProcessDefinition processDefinition = processDefinitionService.getOne(
                new QueryWrapper<ProcessDefinition>().eq("code", processCode).eq("status", 1));
        
        if (processDefinition == null) {
            log.warn("未找到流程定义，流程编码: {}", processCode);
            return null;
        }

        // 根据流程ID和步骤编码查找步骤定义
        ProcessStepDefinition stepDefinition = processStepDefinitionService.getOne(
                new QueryWrapper<ProcessStepDefinition>()
                        .eq("process_id", processDefinition.getId())
                        .eq("step_code", stepCode)
                        .eq("status", 1));

        if (stepDefinition != null) {
            redisCache.setCacheObject(cacheKey, stepDefinition, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
        }

        return stepDefinition;
    }

    @Override
    public List<ProcessStepDefinition> getTaskConfigsByProcess(String processCode) {
        if (!StringUtils.hasText(processCode)) {
            return Collections.emptyList();
        }

        String cacheKey = PROCESS_STEPS_CACHE_KEY + processCode;
        List<ProcessStepDefinition> cached = redisCache.getCacheObject(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 根据流程编码查找流程定义
        ProcessDefinition processDefinition = processDefinitionService.getOne(
                new QueryWrapper<ProcessDefinition>().eq("code", processCode).eq("status", 1));
        
        if (processDefinition == null) {
            log.warn("未找到流程定义，流程编码: {}", processCode);
            return Collections.emptyList();
        }

        // 根据流程ID查找所有步骤定义
        List<ProcessStepDefinition> stepDefinitions = processStepDefinitionService.list(
                new QueryWrapper<ProcessStepDefinition>()
                        .eq("process_id", processDefinition.getId())
                        .eq("status", 1)
                        .orderByAsc("step_order"));

        if (!stepDefinitions.isEmpty()) {
            redisCache.setCacheObject(cacheKey, stepDefinitions, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
        }

        return stepDefinitions;
    }

    @Override
    public List<ProcessStepDefinition> getTaskConfigsByStepCode(String stepCode) {
        if (!StringUtils.hasText(stepCode)) {
            return Collections.emptyList();
        }

        return processStepDefinitionService.list(
                new QueryWrapper<ProcessStepDefinition>()
                        .eq("step_code", stepCode)
                        .eq("status", 1)
                        .orderByAsc("step_order"));
    }

    @Override
    public Map<String, Object> getDefaultTaskParams(String stepCode) {
        Map<String, Object> defaultParams = new HashMap<>();

        switch (stepCode) {
            case "data_identify":
            case "data_collection":
                defaultParams.put("dataScope", "all");
                defaultParams.put("includeBackup", false);
                defaultParams.put("timeRange", "current");
                break;
            case "risk_assess":
            case "risk_analysis":
                defaultParams.put("analysisDepth", "standard");
                defaultParams.put("includeThirdParty", false);
                defaultParams.put("riskCategories", Arrays.asList("访问控制", "数据泄露", "系统漏洞"));
                break;
            case "control_design":
            case "control_measures":
                defaultParams.put("measureTypes", Arrays.asList("技术措施", "管理措施", "物理措施"));
                defaultParams.put("priorityLevel", "medium");
                defaultParams.put("implementationPlan", "分阶段实施");
                break;
            case "report_generate":
            case "report_generation":
                defaultParams.put("reportFormat", "pdf");
                defaultParams.put("includeCharts", true);
                defaultParams.put("detailLevel", "standard");
                break;
            default:
                log.warn("未知步骤编码: {}", stepCode);
        }

        return defaultParams;
    }

    @Override
    public boolean validateTaskParams(String stepCode, Map<String, Object> taskParams) {
        if (taskParams == null) {
            return true; // 允许空参数
        }

        switch (stepCode) {
            case "data_identify":
            case "data_collection":
                return validateDataCollectionParams(taskParams);
            case "risk_assess":
            case "risk_analysis":
                return validateRiskAnalysisParams(taskParams);
            case "control_design":
            case "control_measures":
                return validateControlMeasuresParams(taskParams);
            case "report_generate":
            case "report_generation":
                return validateReportGenerationParams(taskParams);
            default:
                return false;
        }
    }

    @Override
    public List<String> getSupportedParams(String stepCode) {
        switch (stepCode) {
            case "data_identify":
            case "data_collection":
                return Arrays.asList("dataScope", "includeBackup", "timeRange");
            case "risk_assess":
            case "risk_analysis":
                return Arrays.asList("analysisDepth", "includeThirdParty", "riskCategories");
            case "control_design":
            case "control_measures":
                return Arrays.asList("measureTypes", "priorityLevel", "implementationPlan");
            case "report_generate":
            case "report_generation":
                return Arrays.asList("reportFormat", "includeCharts", "detailLevel");
            default:
                return Collections.emptyList();
        }
    }



    @Override
    public void refreshTaskConfigCache() {
        // 清除所有任务配置缓存
        Collection<String> keys = redisCache.keys(TASK_CONFIG_CACHE_KEY + "*");
        if (!keys.isEmpty()) {
            redisCache.deleteObject(keys);
        }

        keys = redisCache.keys(PROCESS_STEPS_CACHE_KEY + "*");
        if (!keys.isEmpty()) {
            redisCache.deleteObject(keys);
        }

        log.info("任务配置缓存已刷新");
    }

    @Override
    public List<String> getAvailableStepCodes() {
        List<ProcessStepDefinition> allSteps = processStepDefinitionService.list(
                new QueryWrapper<ProcessStepDefinition>()
                        .isNotNull("step_code")
                        .ne("step_code", "")
                        .eq("status", 1));

        return allSteps.stream()
                .map(ProcessStepDefinition::getStepCode)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 验证数据收集参数
     */
    private boolean validateDataCollectionParams(Map<String, Object> params) {
        if (params.containsKey("dataScope")) {
            String dataScope = (String) params.get("dataScope");
            if (!Arrays.asList("all", "sensitive_only", "public_only").contains(dataScope)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证风险分析参数
     */
    private boolean validateRiskAnalysisParams(Map<String, Object> params) {
        if (params.containsKey("analysisDepth")) {
            String depth = (String) params.get("analysisDepth");
            if (!Arrays.asList("basic", "standard", "detailed", "comprehensive").contains(depth)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证控制措施参数
     */
    private boolean validateControlMeasuresParams(Map<String, Object> params) {
        if (params.containsKey("priorityLevel")) {
            String priority = (String) params.get("priorityLevel");
            if (!Arrays.asList("low", "medium", "high", "critical").contains(priority)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证报告生成参数
     */
    private boolean validateReportGenerationParams(Map<String, Object> params) {
        if (params.containsKey("reportFormat")) {
            String format = (String) params.get("reportFormat");
            if (!Arrays.asList("pdf", "word", "html", "excel").contains(format)) {
                return false;
            }
        }
        return true;
    }
}
