package com.ruoyi.evaluateModel.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 评估模型测试控制器
 * 用于测试新功能
 */
@RestController
@RequestMapping("/evaluateModel/test")
@Api(value = "评估模型测试控制器", tags = {"评估模型测试"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateModelTestController {

    private final IEvaluateModelService evaluateModelService;

    /**
     * 测试保存模型功能
     */
    @ApiOperation("测试保存模型功能")
    @PostMapping("/testSave")
    public AjaxResult testSave() {
        try {
            EvaluateModel model = new EvaluateModel();
            model.setTitle("测试模型-" + System.currentTimeMillis());
            model.setTypeId(1L);
            model.setStatus(1);
            
            boolean result = evaluateModelService.save(model);
            
            if (result) {
                return AjaxResult.success("测试保存成功", model);
            } else {
                return AjaxResult.error("测试保存失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("测试保存异常: " + e.getMessage());
        }
    }

    /**
     * 测试启用模型功能
     */
    @ApiOperation("测试启用模型功能")
    @PutMapping("/testEnable/{modelId}")
    public AjaxResult testEnable(@PathVariable("modelId") Long modelId) {
        try {
            boolean result = evaluateModelService.enableModel(modelId);
            
            if (result) {
                return AjaxResult.success("测试启用成功");
            } else {
                return AjaxResult.error("测试启用失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("测试启用异常: " + e.getMessage());
        }
    }

    /**
     * 查看模型详情
     */
    @ApiOperation("查看模型详情")
    @GetMapping("/detail/{modelId}")
    public AjaxResult getDetail(@PathVariable("modelId") Long modelId) {
        try {
            EvaluateModel model = evaluateModelService.getById(modelId);
            return AjaxResult.success("查询成功", model);
        } catch (Exception e) {
            return AjaxResult.error("查询异常: " + e.getMessage());
        }
    }
}
