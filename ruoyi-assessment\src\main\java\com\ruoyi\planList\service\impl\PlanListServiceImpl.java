package com.ruoyi.planList.service.impl;

import java.io.FileInputStream;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.planList.domain.excel.ExcelPlanList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.planList.mapper.PlanListMapper;
import com.ruoyi.planList.domain.PlanList;
import com.ruoyi.planList.service.IPlanListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.planList.service.PlanListValidatorService;
import com.ruoyi.planList.map.FieldLabelMap;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;

import com.ruoyi.planList.listener.PlanListListener;
import com.ruoyi.common.utils.BeanConvertUtil;

import java.io.IOException;

import com.ruoyi.planList.domain.PlanVersion;
import com.ruoyi.planList.service.IPlanVersionService;

/**
 * 评估计划清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class PlanListServiceImpl extends ServiceImpl<PlanListMapper, PlanList> implements IPlanListService {

    @Autowired
    private IPlanListService planListService;

    @Autowired
    private PlanListValidatorService planListValidatorService;

    @Autowired
    private FieldLabelMap fieldLabelMap;

    @Autowired
    private IPlanVersionService planVersionService;

    @Override
    public int batchImport(List<PlanList> excelList) {
        if (CollectionUtils.isEmpty(excelList)) {
            return 0;
        }

        List<PlanList> entityList = excelList.stream().map(item -> {
            PlanList entity = new PlanList();
            BeanUtils.copyProperties(item, entity);
            entity.setCreateTime(new Date());
            entity.setCreateBy(SecurityUtils.getUsername());
            return entity;
        }).collect(Collectors.toList());

        this.saveBatch(entityList);
        return entityList.size();
    }

    @Override
    public Map<String, Object> importPlanList(MultipartFile file, Long orgId) throws IOException {
        Map<String, Object> result = new HashMap<>();
        if (orgId == null) {
            throw new ServiceException("请选择所属单位");
        }
        // 1. 解析Excel
        log.info("1. 解析Excel");
        List<ExcelPlanList> excelData = parseExcel(file);

        // 2. 获取/创建版本ID
        log.info("2. 获取/创建版本ID");
        Long versionId = getOrCreateCurrentYearVersionId(orgId);

        // 3. 转换为PlanList
        log.info("3. 转换");
        List<PlanList> planList = convertExcelListToPlanList(excelData, orgId, versionId);
        log.info("3. 转换 -> {}", planList);

        // 4. 单条校验
        log.info("4. 单条校验");
        List<PlanList> validList = new ArrayList<>();
        List<Map<String, Object>> errorRows = new ArrayList<>();
        validatePlanList(planList, validList, errorRows);
        log.info("4. 单条校验完成 -> {}", validList);

        // 5. 唯一性校验
        log.info("5. 唯一性校验");
        List<PlanList> dbList = getDbPlanList(orgId, versionId);
        List<PlanList> existInDbList = planListValidatorService.validateUniqueKeys(validList, dbList, errorRows);
        log.info("5. 唯一性校验完成 -> {}", validList);

        // 6. 入库（新增）
        log.info("6. 入库（新增）");
        int successCount = batchImport(validList);

        // 7. 批量更新（已存在）
        int updateCount = 0;
        if (!existInDbList.isEmpty()) {
            // 这里假设有批量更新方法 batchUpdate，如果没有请实现
            updateCount = batchUpdatePlanList(existInDbList, orgId, versionId);
        }

        // 8. 结果组装
        result.put("successCount", successCount);
        result.put("updateCount", updateCount);
        result.put("errorCount", errorRows.size());
        result.put("errorRows", errorRows);
        return result;
    }

    @Override
    public void exportPlanListWithTemplate(HttpServletResponse response, PlanList planList, String templatePath) {
        try (InputStream templateInputStream = new FileInputStream(templatePath)) {
            List<PlanList> dataList = this.list(new QueryWrapper<>(planList).orderByAsc("id"));
            List<ExcelPlanList> excelList = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                ExcelPlanList excel = new ExcelPlanList();
                BeanUtils.copyProperties(dataList.get(i), excel);
                excel.setSerialNo(String.valueOf(i + 1));
                excelList.add(excel);
            }
            log.info("数据转换完成 -> {}", excelList);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("评估计划清单数据", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateInputStream)
                    .build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.fill(excelList, writeSheet);
            }
        } catch (Exception e) {
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新PlanList（数据库已存在的数据）
     * 可根据实际业务完善更新逻辑
     */
    private int batchUpdatePlanList(List<PlanList> updateList, Long orgId, Long versionId) {
        if (updateList == null || updateList.isEmpty()) {
            return 0;
        }
        // 这里可以根据唯一性条件查出数据库原有数据，然后set新值后批量更新
        // 这里只做简单实现，实际可根据主键或唯一性条件更新
        for (PlanList update : updateList) {
            // 你可以根据唯一性条件查出原有数据，然后set新值
            // 这里只是简单调用updateById，实际可根据业务调整
            this.updateById(update);
        }
        return updateList.size();
    }

    // 解析Excel
    private List<ExcelPlanList> parseExcel(MultipartFile file) throws IOException {
        PlanListListener listener = new PlanListListener();
        EasyExcel.read(file.getInputStream(), ExcelPlanList.class, listener)
                .headRowNumber(2)
                .sheet()
                .doRead();
        return listener.getCacheList();
    }

    // 转换
    private List<PlanList> convertExcelListToPlanList(List<ExcelPlanList> excelData, Long orgId, Long versionId) {
        List<PlanList> planList = new ArrayList<>();
        for (ExcelPlanList excel : excelData) {
            PlanList plan = convertToPlanList(excel, orgId);
            plan.setOrgId(orgId);
            plan.setVersionId(versionId);
            planList.add(plan);
        }
        return planList;
    }

    // 单条校验
    private void validatePlanList(List<PlanList> planList, List<PlanList> validList, List<Map<String, Object>> errorRows) {
        int rowNum = 3;
        for (PlanList plan : planList) {
            List<ExcelImportHelper.RowError.ColumnError> rowErrors = new ArrayList<>();
            planListValidatorService.validate(plan, rowErrors);
            if (rowErrors.isEmpty()) {
                validList.add(plan);
            } else {
                Map<String, Object> rowError = new HashMap<>();
                rowError.put("rowNum", rowNum);
                rowError.put("errors", rowErrors);
                errorRows.add(rowError);
            }
            rowNum++;
        }
    }

    // 获取/创建当前年份版本ID
    private Long getOrCreateCurrentYearVersionId(Long orgId) {
        int currentYear = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR);
        String versionCode = String.valueOf(currentYear);
        PlanVersion planVersion = planVersionService.getByVersionCode(versionCode, orgId);
        if (planVersion == null) {
            planVersion = new PlanVersion();
            planVersion.setVersionCode(versionCode);
            planVersion.setOrgId(orgId);
            planVersion.setVersionDesc(versionCode + "年度版本");
            planVersionService.save(planVersion);
        }
        return planVersion.getId();
    }

    // 查询数据库PlanList
    private List<PlanList> getDbPlanList(Long orgId, Long versionId) {
        return this.lambdaQuery()
                .eq(PlanList::getOrgId, orgId)
                .eq(versionId != null, PlanList::getVersionId, versionId)
                .list();
    }

    @Override
    public PlanList convertToPlanList(ExcelPlanList excel, Long orgId) {
        PlanList target = new PlanList();
        // 复制基础属性
        BeanUtils.copyProperties(excel, target);
        // 日期类型转换
        target.setPlannedEvalTime(BeanConvertUtil.parseDate(excel.getPlannedEvalTime()));
        target.setActualEvalTime(BeanConvertUtil.parseDate(excel.getActualEvalTime()));
        // 自动处理所有映射字段
        fieldLabelMap.getFieldLabelToValue().forEach((fieldName, mapping) -> {
            try {
                Field srcField = ExcelPlanList.class.getDeclaredField(fieldName);
                srcField.setAccessible(true);
                Object srcValue = srcField.get(excel);
                String label = srcValue == null ? "" : srcValue.toString();
                String mappedValue = mapping.getOrDefault(label, label);
                Field targetField = PlanList.class.getDeclaredField(fieldName);
                targetField.setAccessible(true);
                targetField.set(target, mappedValue);
            } catch (Exception e) {
                // ignore or log
            }
        });
        target.setOrgId(orgId);
        return target;
    }

    @Override
    public List<Map<String, Object>> countPlanListGroupByVersionId() {
        QueryWrapper<PlanList> wrapper = new QueryWrapper<>();
        wrapper.select("version_id, COUNT(*) AS count");
        wrapper.groupBy("version_id");
        // 返回List<Map<String, Object>>，每个map包含version_id和count
        return this.listMaps(wrapper);
    }
}
