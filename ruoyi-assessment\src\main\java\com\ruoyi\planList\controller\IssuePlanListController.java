package com.ruoyi.planList.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.planList.domain.IssuePlanList;
import com.ruoyi.planList.service.IIssuePlanListService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 问题整改计划清单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/planList/issueList")
@Api(value = "问题整改计划清单控制器", tags = {"问题整改计划清单管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class IssuePlanListController extends BaseController
{
    private final IIssuePlanListService issuePlanListService;

    /**
     * 查询问题整改计划清单列表
     */
    @ApiOperation("查询问题整改计划清单列表")
    @PreAuthorize("@ss.hasPermi('planList:issueList:list')")
    @GetMapping("/list")
    public TableDataInfo list(IssuePlanList issuePlanList) {
        startPage();
        List<IssuePlanList> list = issuePlanListService.list(new QueryWrapper<IssuePlanList>(issuePlanList));
        return getDataTable(list);
    }

    /**
     * 导出问题整改计划清单列表
     */
    @ApiOperation("导出问题整改计划清单列表")
    @PreAuthorize("@ss.hasPermi('planList:issueList:export')")
    @Log(title = "问题整改计划清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,IssuePlanList issuePlanList) {
        List<IssuePlanList> list = issuePlanListService.list(new QueryWrapper<IssuePlanList>(issuePlanList));
        ExcelUtil<IssuePlanList> util = new ExcelUtil<IssuePlanList>(IssuePlanList.class);
        util.exportExcel(response,list, "问题整改计划清单数据");
    }

    /**
     * 获取问题整改计划清单详细信息
     */
    @ApiOperation("获取问题整改计划清单详细信息")
    @PreAuthorize("@ss.hasPermi('planList:issueList:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(issuePlanListService.getById(id));
    }

    /**
     * 新增问题整改计划清单
     */
    @ApiOperation("新增问题整改计划清单")
    @PreAuthorize("@ss.hasPermi('planList:issueList:add')")
    @Log(title = "问题整改计划清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IssuePlanList issuePlanList) {
        return toAjax(issuePlanListService.save(issuePlanList));
    }

    /**
     * 修改问题整改计划清单
     */
    @ApiOperation("修改问题整改计划清单")
    @PreAuthorize("@ss.hasPermi('planList:issueList:edit')")
    @Log(title = "问题整改计划清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IssuePlanList issuePlanList) {
        return toAjax(issuePlanListService.updateById(issuePlanList));
    }

    /**
     * 删除问题整改计划清单
     */
    @ApiOperation("删除问题整改计划清单")
    @PreAuthorize("@ss.hasPermi('planList:issueList:remove')")
    @Log(title = "问题整改计划清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(issuePlanListService.removeByIds(Arrays.asList(ids)));
    }
}