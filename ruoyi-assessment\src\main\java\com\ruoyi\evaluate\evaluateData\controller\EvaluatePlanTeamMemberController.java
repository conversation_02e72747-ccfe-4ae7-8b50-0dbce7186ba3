package com.ruoyi.evaluate.evaluateData.controller;

import java.util.Arrays;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanTeamMember;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanTeamMemberService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估计划评估团队成员Controller
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/evaluateData/teamMember/member")
@Api(value = "评估计划成员管理", tags = {"评估计划成员管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanTeamMemberController extends BaseController
{
    private final IEvaluatePlanTeamMemberService evaluatePlanTeamMemberService;

    /**
     * 查询评估计划评估团队成员列表
     */
    @ApiOperation("查询评估计划评估团队成员列表")
    @PreAuthorize("@ss.hasPermi('teamMember:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluatePlanTeamMember evaluatePlanTeamMember) {
        startPage();
        List<EvaluatePlanTeamMember> list = evaluatePlanTeamMemberService.list(new QueryWrapper<EvaluatePlanTeamMember>(evaluatePlanTeamMember));
        return getDataTable(list);
    }

    /**
     * 获取评估计划评估团队成员详细信息
     */
    @ApiOperation("获取评估计划评估团队成员详细信息")
    @PreAuthorize("@ss.hasPermi('teamMember:member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluatePlanTeamMemberService.getById(id));
    }

    /**
     * 新增评估计划评估团队成员
     */
    @ApiOperation("新增评估计划评估团队成员")
    @PreAuthorize("@ss.hasPermi('teamMember:member:add')")
    @Log(title = "评估计划评估团队成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluatePlanTeamMember evaluatePlanTeamMember) {
        return toAjax(evaluatePlanTeamMemberService.save(evaluatePlanTeamMember));
    }

    /**
     * 修改评估计划评估团队成员
     */
    @ApiOperation("修改评估计划评估团队成员")
    @PreAuthorize("@ss.hasPermi('teamMember:member:edit')")
    @Log(title = "评估计划评估团队成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluatePlanTeamMember evaluatePlanTeamMember) {
        return toAjax(evaluatePlanTeamMemberService.updateById(evaluatePlanTeamMember));
    }

    /**
     * 删除评估计划评估团队成员
     */
    @ApiOperation("删除评估计划评估团队成员")
    @PreAuthorize("@ss.hasPermi('teamMember:member:remove')")
    @Log(title = "评估计划评估团队成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluatePlanTeamMemberService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 从评估团队导入成员到当前数据库
     */
    @ApiOperation("从评估团队导入成员到当前数据库")
    @PreAuthorize("@ss.hasPermi('teamMember:member:import')")
    @Log(title = "导入评估团队成员", businessType = BusinessType.IMPORT)
    @PostMapping("/importFromTeam")
    public AjaxResult importFromTeam(@RequestParam("teamId") Long teamId,
                                   @RequestParam("planId") Long planId) {
        try {
            String result = evaluatePlanTeamMemberService.importFromTeam(teamId, planId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("导入团队成员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}