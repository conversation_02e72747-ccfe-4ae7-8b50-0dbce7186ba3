package com.ruoyi.process.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.service.IProcessStepInstanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程步骤实例Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/process/stepInstance")
@Api(value = "流程步骤实例控制器", tags = {"流程步骤实例管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessStepInstanceController extends BaseController
{
    private final IProcessStepInstanceService processStepInstanceService;

    /**
     * 查询流程步骤实例列表
     */
    @ApiOperation("查询流程步骤实例列表")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessStepInstance processStepInstance) {
        startPage();
        List<ProcessStepInstance> list = processStepInstanceService.list(new QueryWrapper<ProcessStepInstance>(processStepInstance));
        return getDataTable(list);
    }

    /**
     * 导出流程步骤实例列表
     */
    @ApiOperation("导出流程步骤实例列表")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:export')")
    @Log(title = "流程步骤实例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,ProcessStepInstance processStepInstance) {
        List<ProcessStepInstance> list = processStepInstanceService.list(new QueryWrapper<ProcessStepInstance>(processStepInstance));
        ExcelUtil<ProcessStepInstance> util = new ExcelUtil<ProcessStepInstance>(ProcessStepInstance.class);
        util.exportExcel(response,list, "流程步骤实例数据");
    }

    /**
     * 获取流程步骤实例详细信息
     */
    @ApiOperation("获取流程步骤实例详细信息")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processStepInstanceService.getById(id));
    }

    /**
     * 新增流程步骤实例
     */
    @ApiOperation("新增流程步骤实例")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:add')")
    @Log(title = "流程步骤实例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessStepInstance processStepInstance) {
        return toAjax(processStepInstanceService.save(processStepInstance));
    }

    /**
     * 修改流程步骤实例
     */
    @ApiOperation("修改流程步骤实例")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:edit')")
    @Log(title = "流程步骤实例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessStepInstance processStepInstance) {
        return toAjax(processStepInstanceService.updateById(processStepInstance));
    }

    /**
     * 删除流程步骤实例
     */
    @ApiOperation("删除流程步骤实例")
    @PreAuthorize("@ss.hasPermi('process:stepInstance:remove')")
    @Log(title = "流程步骤实例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processStepInstanceService.removeByIds(Arrays.asList(ids)));
    }
}