package com.ruoyi.dataItem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据承载系统对象 dsa_data_system
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_data_system")
public class DataSystem extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */

    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位id不能为空", groups = {AddGroup.class, ListGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 信息系统名称 */
    @Excel(name = "信息系统名称")
    @TableField(value = "system_name")
    private String systemName;

    /** 主要功能介绍 */
    @Excel(name = "主要功能介绍")
    @TableField(value = "main_function")
    private String mainFunction;

    /** 网络架构说明 */
    @Excel(name = "网络架构说明")
    @TableField(value = "network_arch_desc")
    private String networkArchDesc;

    /** 网络架构图片路径 */
    @Excel(name = "网络架构图片路径")

    @TableField(value = "network_arch_img")
    private String networkArchImg;

    /** 技术架构说明 */
    @Excel(name = "技术架构说明")

    @TableField(value = "tech_arch_desc")
    private String techArchDesc;

    /** 技术架构图片路径 */
    @Excel(name = "技术架构图片路径")

    @TableField(value = "tech_arch_img")
    private String techArchImg;

    /** 测试地址 */
    @Excel(name = "测试地址")

    @TableField(value = "test_url")
    private String testUrl;

    /** 测试账号 */
    @Excel(name = "测试账号")

    @TableField(value = "test_account")
    private String testAccount;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")

    @TableField(value = "status")
    private Integer status;






}