package com.ruoyi.dataItem.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 数据项变更日志对象 dsa_data_item_log
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@TableName("dsa_data_item_log")
public class DataItemLog extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = EditGroup.class)
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据项ID
     */
    @NotNull(message = "数据项ID（dataItemId）不能为空", groups = {AddGroup.class, EditGroup.class})
    @Excel(name = "数据项ID")
    @TableField(value = "data_item_id")
    private Long dataItemId;

    /**
     * 所属单位ID
     */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 变更字段名
     */
    @Excel(name = "变更字段名")
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 变更前的值
     */
    @Excel(name = "变更前的值")
    @TableField(value = "before_value")
    private String beforeValue;

    /**
     * 变更后的值
     */
    @Excel(name = "变更后的值")
    @TableField(value = "after_value")
    private String afterValue;

    /**
     * 操作类型（1-insert/2-update/3-delete）
     */
    @Excel(name = "操作类型")
    @TableField(value = "operation_type")
    private Integer operationType;

    /**
     * 操作人
     */
    @Excel(name = "操作人")
    @TableField(value = "operation_user")
    private String operationUser;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "operation_time")
    private Date operationTime;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(exist = false)
    private Date updateTime;

    /**
     * 备注
     */
    @TableField(exist = false)
    private String remark;
}