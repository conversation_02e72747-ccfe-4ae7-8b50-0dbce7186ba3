<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateData.mapper.EvaluatePlanTeamMemberMapper">
    
    <resultMap type="EvaluatePlanTeamMember" id="EvaluatePlanTeamMemberResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="role"    column="role"    />
        <result property="duty"    column="duty"    />
        <result property="name"    column="name"    />
        <result property="unit"    column="unit"    />
        <result property="position"    column="position"    />
        <result property="ability"    column="ability"    />
        <result property="experience"    column="experience"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluatePlanTeamMemberVo">
        select id, org_id, plan_id, group_id, role, duty, name, unit, position, ability, experience, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_plan_team_member
    </sql>

    <select id="selectEvaluatePlanTeamMemberList" parameterType="EvaluatePlanTeamMember" resultMap="EvaluatePlanTeamMemberResult">
        <include refid="selectEvaluatePlanTeamMemberVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="role != null  and role != ''"> and role = #{role}</if>
            <if test="duty != null  and duty != ''"> and duty = #{duty}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="ability != null  and ability != ''"> and ability = #{ability}</if>
            <if test="experience != null  and experience != ''"> and experience = #{experience}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluatePlanTeamMemberById" parameterType="Long" resultMap="EvaluatePlanTeamMemberResult">
        <include refid="selectEvaluatePlanTeamMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluatePlanTeamMember" parameterType="EvaluatePlanTeamMember" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_plan_team_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="role != null">role,</if>
            <if test="duty != null">duty,</if>
            <if test="name != null">name,</if>
            <if test="unit != null">unit,</if>
            <if test="position != null">position,</if>
            <if test="ability != null">ability,</if>
            <if test="experience != null">experience,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="role != null">#{role},</if>
            <if test="duty != null">#{duty},</if>
            <if test="name != null">#{name},</if>
            <if test="unit != null">#{unit},</if>
            <if test="position != null">#{position},</if>
            <if test="ability != null">#{ability},</if>
            <if test="experience != null">#{experience},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluatePlanTeamMember" parameterType="EvaluatePlanTeamMember">
        update dsa_evaluate_plan_team_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="role != null">role = #{role},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="name != null">name = #{name},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="position != null">position = #{position},</if>
            <if test="ability != null">ability = #{ability},</if>
            <if test="experience != null">experience = #{experience},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluatePlanTeamMemberById" parameterType="Long">
        delete from dsa_evaluate_plan_team_member where id = #{id}
    </delete>

    <delete id="deleteEvaluatePlanTeamMemberByIds" parameterType="String">
        delete from dsa_evaluate_plan_team_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>