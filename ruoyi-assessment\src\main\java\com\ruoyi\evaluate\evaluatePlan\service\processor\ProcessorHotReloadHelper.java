package com.ruoyi.evaluate.evaluatePlan.service.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 处理器热重载助手
 * <p>
 * 提供开发时的处理器热重载功能，支持JRebel等热部署工具
 * 只在开发环境下启用
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "processor.hot-reload.enabled", havingValue = "true", matchIfMissing = false)
public class ProcessorHotReloadHelper {

    @Autowired
    private ProcessorRegistry processorRegistry;

    @Autowired
    private StepDataProcessorFactory processorFactory;

    /**
     * 刷新处理器注册表
     * 用于开发时代码修改后重新加载处理器
     */
    public void refreshProcessors() {
        log.info("=== 开始刷新处理器注册表 ===");
        
        try {
            // 记录刷新前状态
            Map<String, Object> beforeStatus = processorRegistry.getRegistryStatus();
            log.info("刷新前状态: 总处理器数={}, 特定处理器={}, 通用处理器={}", 
                beforeStatus.get("totalProcessors"),
                beforeStatus.get("specificProcessors"), 
                beforeStatus.get("commonProcessors"));
            
            // 执行刷新
            processorRegistry.refreshRegistry();
            
            // 记录刷新后状态
            Map<String, Object> afterStatus = processorRegistry.getRegistryStatus();
            log.info("刷新后状态: 总处理器数={}, 特定处理器={}, 通用处理器={}", 
                afterStatus.get("totalProcessors"),
                afterStatus.get("specificProcessors"), 
                afterStatus.get("commonProcessors"));
            
            log.info("✅ 处理器注册表刷新成功");
            
        } catch (Exception e) {
            log.error("❌ 处理器注册表刷新失败", e);
            throw new RuntimeException("处理器刷新失败: " + e.getMessage(), e);
        }
        
        log.info("=== 处理器注册表刷新完成 ===");
    }

    /**
     * 强制重新注册所有处理器
     */
    public void forceReregisterAll() {
        log.info("=== 开始强制重新注册所有处理器 ===");
        
        try {
            processorRegistry.forceReregister();
            log.info("✅ 强制重新注册成功");
        } catch (Exception e) {
            log.error("❌ 强制重新注册失败", e);
            throw new RuntimeException("强制重新注册失败: " + e.getMessage(), e);
        }
        
        log.info("=== 强制重新注册完成 ===");
    }

    /**
     * 测试处理器是否正常工作
     */
    public void testProcessors() {
        log.info("=== 开始测试处理器功能 ===");
        
        try {
            // 测试数据安全评估处理器
            testProcessorMatch("data_security_plan", "create_plan", "数据安全-创建计划");
            testProcessorMatch("data_security_plan", "evaluate_scope", "数据安全-评估范围");
            
            // 测试基础信息评估处理器
            testProcessorMatch("basic_info", "current_analysis", "基础信息-现状分析");
            
            // 测试默认处理器
            testProcessorMatch("unknown_type", "unknown_step", "未知类型-默认处理器");
            
            log.info("✅ 处理器功能测试完成");
            
        } catch (Exception e) {
            log.error("❌ 处理器功能测试失败", e);
        }
        
        log.info("=== 处理器功能测试结束 ===");
    }

    /**
     * 测试单个处理器匹配
     */
    private void testProcessorMatch(String evaluateType, String stepCode, String description) {
        try {
            IStepDataProcessor processor = processorFactory.getProcessor(evaluateType, stepCode);
            if (processor != null) {
                log.info("✅ {} - 找到处理器: {}", description, processor.getClass().getSimpleName());
            } else {
                log.warn("⚠️ {} - 未找到处理器", description);
            }
        } catch (Exception e) {
            log.error("❌ {} - 处理器匹配异常: {}", description, e.getMessage());
        }
    }

    /**
     * 获取处理器状态报告
     */
    public String getStatusReport() {
        Map<String, Object> status = processorRegistry.getRegistryStatus();

        StringBuilder report = new StringBuilder();
        report.append("=== 处理器热重载状态报告 ===\n");
        report.append(String.format("总处理器数: %s\n", status.get("totalProcessors")));
        report.append(String.format("特定处理器: %s\n", status.get("specificProcessors")));
        report.append(String.format("通用处理器: %s\n", status.get("commonProcessors")));

        // 安全地获取 boolean 值
        Object hasDefaultObj = status.get("hasDefaultProcessor");
        boolean hasDefault = hasDefaultObj != null && (Boolean) hasDefaultObj;
        report.append(String.format("默认处理器: %s\n", hasDefault ? "已配置" : "未配置"));

        Object initializedObj = status.get("initialized");
        boolean initialized = initializedObj != null && (Boolean) initializedObj;
        report.append(String.format("注册表状态: %s\n", initialized ? "已初始化" : "未初始化"));

        report.append(String.format("最后注册时间: %s\n", status.get("lastRegistrationTime")));

        return report.toString();
    }

    /**
     * 检查是否启用了热重载功能
     */
    public boolean isHotReloadEnabled() {
        return true; // 如果这个类被实例化，说明热重载已启用
    }
}
