package com.ruoyi.common.utils.file;

import com.ruoyi.common.exception.file.InvalidZipFileException;
import com.ruoyi.common.exception.file.ZipExtractionException;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import java.nio.charset.Charset;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * ZIP文件处理工具类
 * 提供ZIP文件验证、解压缩等功能，支持性能优化
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
public class ZipFileUtils {

    /**
     * 默认最大文件大小 500MB
     */
    public static final long DEFAULT_MAX_ZIP_SIZE = 500 * 1024 * 1024L;

    /**
     * 默认最大解压文件数量 5000
     */
    public static final int DEFAULT_MAX_EXTRACT_FILES = 5000;

    /**
     * 默认最大解压后总大小 2048MB
     */
    public static final long DEFAULT_MAX_EXTRACT_SIZE = 2 * 1024 * 1024 * 1024L;

    /**
     * 支持的Excel文件扩展名
     */
    private static final Set<String> EXCEL_EXTENSIONS = new HashSet<String>() {{
        add(".xlsx");
        add(".xls");
    }};

    /**
     * 线程池大小
     */
    private static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors();

    /**
     * 常用的ZIP文件编码格式
     */
    private static final String[] COMMON_CHARSETS = {"GBK", "UTF-8", "GB2312", "ISO-8859-1"};

    /**
     * 验证ZIP文件是否有效
     *
     * @param file 上传的ZIP文件
     * @throws InvalidZipFileException 如果ZIP文件无效
     */
    public static void validateZipFile(MultipartFile file) throws InvalidZipFileException {
        if (file == null || file.isEmpty()) {
            throw new InvalidZipFileException("ZIP文件不能为空");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
            throw new InvalidZipFileException("文件必须是ZIP格式");
        }

        // 检查文件大小
        if (file.getSize() > DEFAULT_MAX_ZIP_SIZE) {
            throw new InvalidZipFileException("ZIP文件大小不能超过 " + (DEFAULT_MAX_ZIP_SIZE / 1024 / 1024) + "MB");
        }

        // 验证ZIP文件结构
        try (InputStream inputStream = file.getInputStream()) {
            // 创建临时文件进行验证
            Path tempFile = Files.createTempFile("zip_validation_", ".zip");
            try {
                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

                ZipFile zipFile = new ZipFile(tempFile.toFile());
                // 设置字符集以正确处理中文文件名
                setOptimalCharset(zipFile);

                if (!zipFile.isValidZipFile()) {
                    throw new InvalidZipFileException("ZIP文件格式无效或已损坏");
                }

                // 检查ZIP文件内容
                validateZipContent(zipFile);
            } finally {
                // 清理临时文件
                Files.deleteIfExists(tempFile);
            }
        } catch (IOException e) {
            log.error("验证ZIP文件时发生IO异常: {}", e.getMessage(), e);
            throw new InvalidZipFileException("验证ZIP文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 验证ZIP文件内容
     *
     * @param zipFile ZIP文件对象
     * @throws InvalidZipFileException 如果内容验证失败
     */
    private static void validateZipContent(ZipFile zipFile) throws InvalidZipFileException {
        try {
            List<FileHeader> fileHeaders = zipFile.getFileHeaders();
            
            if (fileHeaders.isEmpty()) {
                throw new InvalidZipFileException("ZIP文件不能为空");
            }

            if (fileHeaders.size() > DEFAULT_MAX_EXTRACT_FILES) {
                throw new InvalidZipFileException("ZIP文件包含的文件数量过多，最多允许 " + DEFAULT_MAX_EXTRACT_FILES + " 个文件");
            }

            long totalUncompressedSize = 0;
            boolean hasExcelFile = false;

            for (FileHeader fileHeader : fileHeaders) {
                String fileName = fileHeader.getFileName();
                
                // 检查文件名安全性
                if (fileName.contains("..") || fileName.startsWith("/") || fileName.contains("\\")) {
                    throw new InvalidZipFileException("ZIP文件包含不安全的文件路径: " + fileName);
                }

                // 累计解压后大小
                totalUncompressedSize += fileHeader.getUncompressedSize();
                
                // 检查是否包含Excel文件
                if (isExcelFile(fileName)) {
                    hasExcelFile = true;
                }
            }

            if (totalUncompressedSize > DEFAULT_MAX_EXTRACT_SIZE) {
                throw new InvalidZipFileException("ZIP文件解压后大小过大，最多允许 " + (DEFAULT_MAX_EXTRACT_SIZE / 1024 / 1024) + "MB");
            }

            if (!hasExcelFile) {
                throw new InvalidZipFileException("ZIP文件必须包含至少一个Excel文件(.xlsx或.xls)");
            }

        } catch (ZipException e) {
            throw new InvalidZipFileException("读取ZIP文件内容失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否为Excel文件
     *
     * @param fileName 文件名
     * @return true如果是Excel文件
     */
    private static boolean isExcelFile(String fileName) {
        if (fileName == null) {
            return false;
        }
        String lowerFileName = fileName.toLowerCase();
        return EXCEL_EXTENSIONS.stream().anyMatch(lowerFileName::endsWith);
    }

    /**
     * 为ZIP文件设置最佳字符集
     *
     * @param zipFile ZIP文件对象
     */
    private static void setOptimalCharset(ZipFile zipFile) {
        // 尝试不同的字符集，找到最适合的
        for (String charsetName : COMMON_CHARSETS) {
            try {
                zipFile.setCharset(Charset.forName(charsetName));

                // 尝试读取文件头来验证字符集是否正确
                List<FileHeader> headers = zipFile.getFileHeaders();
                boolean hasValidFilenames = true;

                for (FileHeader header : headers) {
                    String fileName = header.getFileName();
                    // 检查文件名是否包含乱码字符
                    if (fileName != null && (fileName.contains("?") || fileName.matches(".*[\\u0000-\\u001F\\u007F-\\u009F].*"))) {
                        hasValidFilenames = false;
                        break;
                    }
                }

                if (hasValidFilenames) {
                    log.debug("ZIP文件使用字符集: {}", charsetName);
                    return;
                }
            } catch (Exception e) {
                log.debug("尝试字符集 {} 失败: {}", charsetName, e.getMessage());
            }
        }

        // 如果所有字符集都不理想，使用GBK作为默认值（适合中文Windows系统）
        try {
            zipFile.setCharset(Charset.forName("GBK"));
            log.info("使用默认字符集 GBK 处理ZIP文件");
        } catch (Exception e) {
            log.warn("设置默认字符集失败: {}", e.getMessage());
        }
    }

    /**
     * 解压ZIP文件到指定目录
     *
     * @param zipFile ZIP文件
     * @param extractDir 解压目录
     * @return 解压结果信息
     * @throws ZipExtractionException 如果解压失败
     */
    public static ZipExtractionResult extractZipFile(File zipFile, String extractDir) throws ZipExtractionException {
        return extractZipFile(zipFile, extractDir, false);
    }

    /**
     * 解压ZIP文件到指定目录
     *
     * @param zipFile ZIP文件
     * @param extractDir 解压目录
     * @param useParallelExtraction 是否使用并行解压
     * @return 解压结果信息
     * @throws ZipExtractionException 如果解压失败
     */
    public static ZipExtractionResult extractZipFile(File zipFile, String extractDir, boolean useParallelExtraction) 
            throws ZipExtractionException {
        
        long startTime = System.currentTimeMillis();
        Path extractPath = Paths.get(extractDir);
        
        try {
            // 创建解压目录
            Files.createDirectories(extractPath);

            ZipFile zip = new ZipFile(zipFile);
            // 设置字符集以正确处理中文文件名
            setOptimalCharset(zip);

            try {
                List<FileHeader> fileHeaders = zip.getFileHeaders();

                if (useParallelExtraction && fileHeaders.size() > 10) {
                    return extractFilesParallel(zip, fileHeaders, extractPath, startTime);
                } else {
                    return extractFilesSequential(zip, fileHeaders, extractPath, startTime);
                }
            } catch (ZipException e) {
                log.error("读取ZIP文件失败: {}", e.getMessage(), e);
                throw new ZipExtractionException("读取ZIP文件失败: " + e.getMessage());
            }

        } catch (IOException e) {
            log.error("创建解压目录失败: {}", e.getMessage(), e);
            throw new ZipExtractionException("创建解压目录失败: " + e.getMessage());
        }
    }

    /**
     * 顺序解压文件
     */
    private static ZipExtractionResult extractFilesSequential(ZipFile zip, List<FileHeader> fileHeaders, 
            Path extractPath, long startTime) throws ZipExtractionException {
        
        List<String> extractedFiles = new ArrayList<>();
        List<String> excelFiles = new ArrayList<>();
        
        try {
            for (FileHeader fileHeader : fileHeaders) {
                if (!fileHeader.isDirectory()) {
                    String fileName = fileHeader.getFileName();
                    Path targetPath = extractPath.resolve(fileName);
                    
                    // 确保父目录存在
                    Files.createDirectories(targetPath.getParent());
                    
                    // 解压文件
                    zip.extractFile(fileHeader, extractPath.toString());
                    
                    extractedFiles.add(fileName);
                    
                    if (isExcelFile(fileName)) {
                        excelFiles.add(targetPath.toString());
                    }
                    
                    log.debug("已解压文件: {}", fileName);
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("ZIP文件解压完成，共解压 {} 个文件，耗时 {}ms", extractedFiles.size(), duration);
            
            return new ZipExtractionResult(extractedFiles, excelFiles, extractPath.toString(), duration);
            
        } catch (IOException e) {
            throw new ZipExtractionException("解压文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 并行解压文件（适用于大量小文件）
     */
    private static ZipExtractionResult extractFilesParallel(ZipFile zip, List<FileHeader> fileHeaders,
                                                            Path extractPath, long startTime) throws ZipExtractionException {
        
        List<String> extractedFiles = Collections.synchronizedList(new ArrayList<>());
        List<String> excelFiles = Collections.synchronizedList(new ArrayList<>());
        
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        
        try {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (FileHeader fileHeader : fileHeaders) {
                if (!fileHeader.isDirectory()) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            String fileName = fileHeader.getFileName();
                            Path targetPath = extractPath.resolve(fileName);
                            
                            // 确保父目录存在
                            synchronized (ZipFileUtils.class) {
                                Files.createDirectories(targetPath.getParent());
                            }
                            
                            // 解压文件
                            synchronized (zip) {
                                zip.extractFile(fileHeader, extractPath.toString());
                            }
                            
                            extractedFiles.add(fileName);
                            
                            if (isExcelFile(fileName)) {
                                excelFiles.add(targetPath.toString());
                            }
                            
                            log.debug("已解压文件: {}", fileName);
                            
                        } catch (Exception e) {
                            log.error("并行解压文件失败: {}", e.getMessage(), e);
                            throw new RuntimeException("解压文件失败: " + e.getMessage(), e);
                        }
                    }, executor);
                    
                    futures.add(future);
                }
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("ZIP文件并行解压完成，共解压 {} 个文件，耗时 {}ms", extractedFiles.size(), duration);
            
            return new ZipExtractionResult(extractedFiles, excelFiles, extractPath.toString(), duration);
            
        } catch (Exception e) {
            throw new ZipExtractionException("并行解压文件时发生错误: " + e.getMessage());
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 清理解压目录
     *
     * @param extractDir 解压目录路径
     */
    public static void cleanupExtractedFiles(String extractDir) {
        if (extractDir == null || extractDir.trim().isEmpty()) {
            return;
        }

        try {
            Path path = Paths.get(extractDir);
            if (Files.exists(path)) {
                try (Stream<Path> paths = Files.walk(path)) {
                    paths.sorted(Comparator.reverseOrder())
                         .map(Path::toFile)
                         .forEach(file -> {
                             try {
                                 if (file.delete()) {
                                     log.debug("已删除文件: {}", file.getAbsolutePath());
                                 } else {
                                     log.warn("无法删除文件: {}", file.getAbsolutePath());
                                 }
                             } catch (Exception e) {
                                 log.warn("删除文件时发生异常: {}, 错误: {}", file.getAbsolutePath(), e.getMessage());
                             }
                         });
                }
                log.info("已清理解压目录: {}", extractDir);
            }
        } catch (IOException e) {
            log.warn("清理解压目录失败: {}", e.getMessage());
        }
    }

    /**
     * 清理临时Excel文件目录
     *
     * @param tempDir 临时目录路径
     */
    public static void cleanupTempExcelFiles(String tempDir) {
        if (tempDir == null || tempDir.trim().isEmpty()) {
            return;
        }

        try {
            Path path = Paths.get(tempDir);
            if (Files.exists(path)) {
                try (Stream<Path> paths = Files.walk(path)) {
                    paths.filter(Files::isRegularFile)
                         .filter(p -> {
                             String fileName = p.getFileName().toString().toLowerCase();
                             return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
                         })
                         .forEach(file -> {
                             try {
                                 if (Files.deleteIfExists(file)) {
                                     log.debug("已删除临时Excel文件: {}", file.toAbsolutePath());
                                 }
                             } catch (Exception e) {
                                 log.warn("删除临时Excel文件时发生异常: {}, 错误: {}", file.toAbsolutePath(), e.getMessage());
                             }
                         });
                }
                log.info("已清理临时Excel文件目录: {}", tempDir);
            }
        } catch (IOException e) {
            log.warn("清理临时Excel文件目录失败: {}", e.getMessage());
        }
    }

    /**
     * ZIP解压结果类
     */
    public static class ZipExtractionResult {
        private final List<String> extractedFiles;
        private final List<String> excelFiles;
        private final String extractDir;
        private final long extractionTime;

        public ZipExtractionResult(List<String> extractedFiles, List<String> excelFiles, 
                String extractDir, long extractionTime) {
            this.extractedFiles = new ArrayList<>(extractedFiles);
            this.excelFiles = new ArrayList<>(excelFiles);
            this.extractDir = extractDir;
            this.extractionTime = extractionTime;
        }

        public List<String> getExtractedFiles() { return extractedFiles; }
        public List<String> getExcelFiles() { return excelFiles; }
        public String getExtractDir() { return extractDir; }
        public long getExtractionTime() { return extractionTime; }
        public int getExtractedFileCount() { return extractedFiles.size(); }
        public int getExcelFileCount() { return excelFiles.size(); }
    }
}
