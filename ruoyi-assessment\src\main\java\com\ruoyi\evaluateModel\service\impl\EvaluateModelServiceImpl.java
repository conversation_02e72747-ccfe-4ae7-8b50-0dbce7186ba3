package com.ruoyi.evaluateModel.service.impl;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.evaluateModel.domain.EvaluateItem;
import com.ruoyi.evaluateModel.listener.EvaluateItemListener;
import com.ruoyi.evaluateModel.validator.DataItemValidatorService;
import com.ruoyi.planList.domain.excel.ExcelPlanList;
import com.ruoyi.planList.map.FieldLabelMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluateModel.mapper.EvaluateModelMapper;
import com.ruoyi.evaluateModel.domain.EvaluateModel;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.evaluateModel.domain.EvaluateItemCategory;
import com.ruoyi.evaluateModel.service.IEvaluateItemCategoryService;
import com.ruoyi.evaluateModel.service.IEvaluateItemService;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.evaluateModel.domain.excel.ExcelEvaluateItemVo;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Date;

/**
 * 评估模型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Service
public class EvaluateModelServiceImpl extends ServiceImpl<EvaluateModelMapper, EvaluateModel> implements IEvaluateModelService {

    @Autowired
    private IEvaluateItemCategoryService itemCategoryService;
    @Autowired
    private IEvaluateItemService itemService;
    @Autowired
    private DataItemValidatorService validatorService;
    @Autowired
    private FieldLabelMap fieldLabelMap;

    @Override
    @Transactional
    public Map<String, Object> importFromExcel(Long modelId, MultipartFile file) throws Exception {
        Map<String, EvaluateItemCategory> categoryCache = new HashMap<>();
        List<EvaluateItem> itemsToSave = new ArrayList<>();

        Map<String, Object> result = new HashMap<>();
        if (modelId == null) {
            throw new ServiceException("请选择所属评估模型");
        }

        // 1. 解析Excel
        log.info("1. 解析Excel");
        List<ExcelEvaluateItemVo> excelData = parseExcel(file);

        // 3. 转换为PlanList
        log.info("3. 转换");
        List<EvaluateItem> planList = convertExcelListToPlanList(excelData, modelId);
        log.info("3. 转换 -> {}", planList);

        // 4. 单条校验
        log.info("4. 单条校验");
        List<EvaluateItem> validList = new ArrayList<>();
        List<Map<String, Object>> errorRows = new ArrayList<>();
        validatePlanList(planList, validList, errorRows);
        log.info("4. 单条校验完成 -> {}", validList);

        // 5. 插入分类
        importCategory(validList, modelId, categoryCache, itemsToSave);

        // 6. 入库（新增）
        log.info("6. 入库（新增）");
        int successCount = batchImport(itemsToSave);

        // 8. 结果组装
        result.put("successCount", successCount);
        result.put("updateCount", 0);
        result.put("errorCount", errorRows.size());
        result.put("errorRows", errorRows);
        return result;
    }

    /**
     * 拷贝评估模型
     *
     * @param srcModelId    源模型ID
     * @param evaluateModel 拷贝数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copy(Long srcModelId, EvaluateModel evaluateModel) {
        if (srcModelId == null) {
            throw new ServiceException("请选择源评估模型");
        }

        try {
            // 1、复制原来的模型
            log.info("1. 开始复制评估模型，源模型ID: {}", srcModelId);

            // 获取源模型信息
            EvaluateModel srcModel = this.getById(srcModelId);
            if (srcModel == null) {
                throw new ServiceException("源评估模型不存在");
            }

            // 设置新模型的typeId为源模型的typeId
            evaluateModel.setTypeId(srcModel.getTypeId());
            evaluateModel.setCreateTime(new Date());
            evaluateModel.setCreateBy(SecurityUtils.getUsername());
            this.save(evaluateModel);
            Long newModelId = evaluateModel.getId();
            log.info("1. 新模型创建成功，新模型ID: {}, typeId: {}", newModelId, evaluateModel.getTypeId());

            // 2、复制原来的评估分类
            log.info("2. 开始复制评估分类");
            copyCategories(srcModelId, newModelId);

            // 3、复制原来的评估项
            log.info("3. 开始复制评估项");
            copyItems(srcModelId, newModelId);

            // 4、设置新模型为可用，同type_id的其他模型设置为不可用
            log.info("4. 设置新模型为可用状态");
            enableModel(newModelId);

            log.info("评估模型复制完成，源模型ID: {}, 新模型ID: {}", srcModelId, newModelId);
            return 1;
        } catch (Exception e) {
            log.error("复制评估模型失败，源模型ID: {}", srcModelId, e);
            throw new ServiceException("复制评估模型失败: " + e.getMessage());
        }
    }

    /**
     * 复制评估分类
     *
     * @param srcModelId 源模型ID
     * @param newModelId 新模型ID
     */
    private void copyCategories(Long srcModelId, Long newModelId) {
        // 查询源模型的所有分类
        QueryWrapper<EvaluateItemCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_id", srcModelId);
        queryWrapper.orderByAsc("parent_id", "sort", "id");
        List<EvaluateItemCategory> srcCategories = itemCategoryService.list(queryWrapper);

        if (CollectionUtils.isEmpty(srcCategories)) {
            log.info("源模型没有分类数据");
            return;
        }

        // 用于存储旧分类ID与新分类ID的映射关系
        Map<Long, Long> categoryIdMapping = new HashMap<>();

        // 复制分类，需要按层级顺序处理（先处理父分类，再处理子分类）
        for (EvaluateItemCategory srcCategory : srcCategories) {
            EvaluateItemCategory newCategory = new EvaluateItemCategory();
            BeanUtils.copyProperties(srcCategory, newCategory);

            // 重置ID和关联信息
            newCategory.setId(null);
            newCategory.setModelId(newModelId);

            // 处理父分类ID映射
            if (srcCategory.getParentId() != null && srcCategory.getParentId() != 0L) {
                Long newParentId = categoryIdMapping.get(srcCategory.getParentId());
                if (newParentId != null) {
                    newCategory.setParentId(newParentId);
                } else {
                    log.warn("找不到父分类映射，源父分类ID: {}", srcCategory.getParentId());
                    newCategory.setParentId(0L);
                }
            }

            // 设置创建信息
            newCategory.setCreateTime(new Date());
            newCategory.setCreateBy(SecurityUtils.getUsername());
            newCategory.setUpdateTime(null);
            newCategory.setUpdateBy(null);

            // 保存新分类
            itemCategoryService.save(newCategory);

            // 记录ID映射关系
            categoryIdMapping.put(srcCategory.getId(), newCategory.getId());

            log.debug("复制分类成功: {} -> {}, 源ID: {}, 新ID: {}",
                srcCategory.getCategoryName(), newCategory.getCategoryName(),
                srcCategory.getId(), newCategory.getId());
        }

        log.info("分类复制完成，共复制 {} 个分类", srcCategories.size());
    }

    /**
     * 复制评估项
     *
     * @param srcModelId 源模型ID
     * @param newModelId 新模型ID
     */
    private void copyItems(Long srcModelId, Long newModelId) {
        // 查询源模型的所有评估项
        QueryWrapper<EvaluateItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_id", srcModelId);
        queryWrapper.orderByAsc("category_id", "sort", "id");
        List<EvaluateItem> srcItems = itemService.list(queryWrapper);

        if (CollectionUtils.isEmpty(srcItems)) {
            log.info("源模型没有评估项数据");
            return;
        }

        // 获取新模型的分类ID映射关系
        Map<Long, Long> categoryIdMapping = getCategoryIdMapping(srcModelId, newModelId);

        // 复制评估项
        List<EvaluateItem> newItems = new ArrayList<>();
        for (EvaluateItem srcItem : srcItems) {
            EvaluateItem newItem = new EvaluateItem();
            BeanUtils.copyProperties(srcItem, newItem);

            // 重置ID和关联信息
            newItem.setId(null);
            newItem.setModelId(newModelId);

            // 处理分类ID映射
            Long newCategoryId = categoryIdMapping.get(srcItem.getCategoryId());
            if (newCategoryId != null) {
                newItem.setCategoryId(newCategoryId);
            } else {
                log.warn("找不到分类映射，源分类ID: {}, 跳过该评估项", srcItem.getCategoryId());
                continue;
            }

            // 设置创建信息
            newItem.setCreateTime(new Date());
            newItem.setCreateBy(SecurityUtils.getUsername());
            newItem.setUpdateTime(null);
            newItem.setUpdateBy(null);

            newItems.add(newItem);
        }

        // 批量保存评估项
        if (!CollectionUtils.isEmpty(newItems)) {
            itemService.saveBatch(newItems);
            log.info("评估项复制完成，共复制 {} 个评估项", newItems.size());
        }
    }

    /**
     * 获取分类ID映射关系
     *
     * @param srcModelId 源模型ID
     * @param newModelId 新模型ID
     * @return 分类ID映射关系 (源分类ID -> 新分类ID)
     */
    private Map<Long, Long> getCategoryIdMapping(Long srcModelId, Long newModelId) {
        // 查询源模型分类
        QueryWrapper<EvaluateItemCategory> srcQueryWrapper = new QueryWrapper<>();
        srcQueryWrapper.eq("model_id", srcModelId);
        srcQueryWrapper.orderByAsc("parent_id", "sort", "id");
        List<EvaluateItemCategory> srcCategories = itemCategoryService.list(srcQueryWrapper);

        // 查询新模型分类
        QueryWrapper<EvaluateItemCategory> newQueryWrapper = new QueryWrapper<>();
        newQueryWrapper.eq("model_id", newModelId);
        newQueryWrapper.orderByAsc("parent_id", "sort", "id");
        List<EvaluateItemCategory> newCategories = itemCategoryService.list(newQueryWrapper);

        // 建立映射关系（基于分类名称和层级结构）
        Map<Long, Long> mapping = new HashMap<>();

        // 创建源分类的层级路径映射
        Map<String, Long> srcCategoryPathMap = buildCategoryPathMap(srcCategories);
        Map<String, Long> newCategoryPathMap = buildCategoryPathMap(newCategories);

        // 建立ID映射关系
        for (Map.Entry<String, Long> entry : srcCategoryPathMap.entrySet()) {
            String path = entry.getKey();
            Long srcCategoryId = entry.getValue();
            Long newCategoryId = newCategoryPathMap.get(path);

            if (newCategoryId != null) {
                mapping.put(srcCategoryId, newCategoryId);
            } else {
                log.warn("找不到对应的新分类，路径: {}", path);
            }
        }

        return mapping;
    }

    /**
     * 构建分类路径映射
     *
     * @param categories 分类列表
     * @return 路径映射 (路径 -> 分类ID)
     */
    private Map<String, Long> buildCategoryPathMap(List<EvaluateItemCategory> categories) {
        Map<String, Long> pathMap = new HashMap<>();
        Map<Long, EvaluateItemCategory> categoryMap = new HashMap<>();

        // 构建分类映射
        for (EvaluateItemCategory category : categories) {
            categoryMap.put(category.getId(), category);
        }

        // 构建路径映射
        for (EvaluateItemCategory category : categories) {
            String path = buildCategoryPath(category, categoryMap);
            pathMap.put(path, category.getId());
        }

        return pathMap;
    }

    /**
     * 构建分类路径
     *
     * @param category    分类
     * @param categoryMap 分类映射
     * @return 分类路径
     */
    private String buildCategoryPath(EvaluateItemCategory category, Map<Long, EvaluateItemCategory> categoryMap) {
        if (category.getParentId() == null || category.getParentId() == 0L) {
            return category.getCategoryName();
        }

        EvaluateItemCategory parent = categoryMap.get(category.getParentId());
        if (parent != null) {
            return buildCategoryPath(parent, categoryMap) + "/" + category.getCategoryName();
        } else {
            return category.getCategoryName();
        }
    }

    public void importCategory(List<EvaluateItem> validList, Long modelId, Map<String, EvaluateItemCategory> categoryCache, List<EvaluateItem> itemsToSave) {
        for (EvaluateItem object : validList) {
            // 1. 处理一级分类
            String level1CategoryName = object.getCategoryLevel1();
            EvaluateItemCategory level1Category = categoryCache.computeIfAbsent(level1CategoryName, name -> {
                EvaluateItemCategory cat = new EvaluateItemCategory();
                cat.setCategoryName(name);
                cat.setModelId(modelId);
                cat.setParentId(0L); // 顶级分类
                itemCategoryService.save(cat);
                return cat;
            });

            // 2. 处理二级分类
            String level2CategoryName = object.getCategoryLevel2();
            // 使用一级分类名称和二级分类名称作为组合键
            String cacheKey = level1CategoryName + "->" + level2CategoryName;
            EvaluateItemCategory level2Category = categoryCache.computeIfAbsent(cacheKey, name -> {
                EvaluateItemCategory cat = new EvaluateItemCategory();
                cat.setCategoryName(level2CategoryName);
                cat.setModelId(modelId);
                cat.setParentId(level1Category.getId());
                itemCategoryService.save(cat);
                return cat;
            });

            EvaluateItem item = new EvaluateItem();
            BeanUtils.copyProperties(object, item);
            item.setCategoryId(level2Category.getId());

            itemsToSave.add(item);
        }

    }

    public int batchImport(List<EvaluateItem> excelList) {
        if (CollectionUtils.isEmpty(excelList)) {
            return 0;
        }

        List<EvaluateItem> entityList = excelList.stream().map(item -> {
            EvaluateItem entity = new EvaluateItem();
            BeanUtils.copyProperties(item, entity);
            entity.setCreateTime(new Date());
            entity.setCreateBy(SecurityUtils.getUsername());
            return entity;
        }).collect(Collectors.toList());

        this.itemService.saveBatch(entityList);
        return entityList.size();
    }

    // 解析Excel
    private List<ExcelEvaluateItemVo> parseExcel(MultipartFile file) throws IOException {
        EvaluateItemListener listener = new EvaluateItemListener();
        EasyExcel.read(file.getInputStream(), ExcelEvaluateItemVo.class, listener)
                .headRowNumber(2)
                .sheet()
                .doRead();
        return listener.getCacheList();
    }

    // 转换
    private List<EvaluateItem> convertExcelListToPlanList(List<ExcelEvaluateItemVo> excelData, Long modelId) {
        List<EvaluateItem> planList = new ArrayList<>();
        for (ExcelEvaluateItemVo excel : excelData) {
            EvaluateItem plan = convertToPlanList(excel, modelId);
            plan.setModelId(modelId);
            planList.add(plan);
        }
        return planList;
    }

    // 单条校验
    private void validatePlanList(List<EvaluateItem> planList, List<EvaluateItem> validList, List<Map<String, Object>> errorRows) {
        int rowNum = 3;
        for (EvaluateItem plan : planList) {
            List<ExcelImportHelper.RowError.ColumnError> rowErrors = new ArrayList<>();
            validatorService.validate(plan, rowErrors);
            if (rowErrors.isEmpty()) {
                validList.add(plan);
            } else {
                Map<String, Object> rowError = new HashMap<>();
                rowError.put("rowNum", rowNum);
                rowError.put("errors", rowErrors);
                errorRows.add(rowError);
            }
            rowNum++;
        }
    }

    public EvaluateItem convertToPlanList(ExcelEvaluateItemVo excel, Long modelId) {
        EvaluateItem target = new EvaluateItem();
        // 复制基础属性
        BeanUtils.copyProperties(excel, target);
        // 日期类型转换
        // target.setPlannedEvalTime(BeanConvertUtil.parseDate(excel.getPlannedEvalTime()));
        // target.setActualEvalTime(BeanConvertUtil.parseDate(excel.getActualEvalTime()));
        // 自动处理所有映射字段
        fieldLabelMap.getFieldLabelToValue().forEach((fieldName, mapping) -> {
            try {
                Field srcField = ExcelEvaluateItemVo.class.getDeclaredField(fieldName);
                srcField.setAccessible(true);
                Object srcValue = srcField.get(excel);
                String label = srcValue == null ? "" : srcValue.toString();
                String mappedValue = mapping.getOrDefault(label, label);
                Field targetField = EvaluateItem.class.getDeclaredField(fieldName);
                targetField.setAccessible(true);
                targetField.set(target, mappedValue);
            } catch (Exception e) {
                // ignore or log
            }
        });
        target.setModelId(modelId);
        return target;
    }

    /**
     * 启用某个评估模型（同时禁用同type_id下的其他模型）
     * @param modelId 要启用的模型ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableModel(Long modelId) {
        if (modelId == null) {
            throw new ServiceException("模型ID不能为空");
        }

        // 1. 获取要启用的模型信息
        EvaluateModel model = this.getById(modelId);
        if (model == null) {
            throw new ServiceException("评估模型不存在");
        }

        try {
            // 2. 先禁用同type_id下的所有其他模型
            try {
                int disabledCount = this.baseMapper.updateEnabledStatusByTypeId(
                    model.getTypeId(), 0, modelId, SecurityUtils.getUsername(), new Date());
                log.info("禁用同类型其他模型，typeId: {}, 禁用数量: {}", model.getTypeId(), disabledCount);
            } catch (Exception e) {
                log.error("禁用同类型其他模型失败，可能是数据库字段不存在: {}", e.getMessage());
                log.warn("跳过禁用其他模型的逻辑，请确保数据库已添加is_enabled字段");
            }

            // 3. 启用当前模型
            model.setIsEnabled(1);
            model.setUpdateBy(SecurityUtils.getUsername());
            model.setUpdateTime(new Date());
            boolean result = this.updateById(model);

            if (result) {
                log.info("成功启用评估模型，modelId: {}, typeId: {}", modelId, model.getTypeId());
            }

            return result;
        } catch (Exception e) {
            log.error("启用评估模型失败，modelId: {}", modelId, e);
            throw new ServiceException("启用评估模型失败: " + e.getMessage());
        }
    }

    /**
     * 保存评估模型（重写以支持启用状态逻辑）
     * @param evaluateModel 评估模型
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(EvaluateModel evaluateModel) {
        if (evaluateModel == null) {
            return false;
        }

        try {
            // 设置默认值
            if (evaluateModel.getStatus() == null) {
                evaluateModel.setStatus(1); // 默认正常状态
            }

            // 新添加的模型默认启用，其他同type_id的模型设置为不启用
            if (evaluateModel.getIsEnabled() == null) {
                evaluateModel.setIsEnabled(1); // 默认启用
            }

            // 如果新模型要启用，先禁用同type_id下的其他模型
            if (evaluateModel.getIsEnabled() == 1 && evaluateModel.getTypeId() != null) {
                try {
                    int disabledCount = this.baseMapper.updateEnabledStatusByTypeId(
                        evaluateModel.getTypeId(), 0, null, SecurityUtils.getUsername(), new Date());
                    log.info("新增模型时禁用同类型其他模型，typeId: {}, 禁用数量: {}", evaluateModel.getTypeId(), disabledCount);
                } catch (Exception e) {
                    log.error("禁用同类型其他模型失败，可能是数据库字段不存在: {}", e.getMessage());
                    // 如果数据库字段不存在，暂时跳过这个逻辑
                    log.warn("跳过禁用其他模型的逻辑，请确保数据库已添加is_enabled字段");
                }
            }

            // 保存模型
            boolean result = super.save(evaluateModel);

            if (result) {
                log.info("成功保存评估模型，modelId: {}, typeId: {}, isEnabled: {}",
                    evaluateModel.getId(), evaluateModel.getTypeId(), evaluateModel.getIsEnabled());
            }

            return result;
        } catch (Exception e) {
            log.error("保存评估模型失败", e);
            throw new ServiceException("保存评估模型失败: " + e.getMessage());
        }
    }
}
