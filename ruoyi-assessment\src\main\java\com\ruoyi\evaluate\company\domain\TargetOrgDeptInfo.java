package com.ruoyi.evaluate.company.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 被评估单位 部门信息管理对象 dsa_target_org_dept_info
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_target_org_dept_info")
public class TargetOrgDeptInfo extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位ID（orgId）不能为空", groups = {AddGroup.class, ListGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    @TableField(value = "department_name")
    private String departmentName;

    /** 部门职责 */
    @Excel(name = "部门职责")
    @TableField(value = "department_duty")
    private String departmentDuty;

    /** 部门负责人 */
    @Excel(name = "部门负责人")
    @TableField(value = "department_leader")
    private String departmentLeader;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;







}