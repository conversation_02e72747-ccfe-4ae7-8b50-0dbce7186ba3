package com.ruoyi.evaluatePlan.strategy.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.evaluate.company.service.ITargetCompanyInfoService;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyInfoService;
import com.ruoyi.evaluateModel.service.IEvaluateModelService;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;
import com.ruoyi.evaluatePlan.strategy.ProcessAwareEvaluateStrategy;
import com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityEvaluateExecutor;
import com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityStepManager;
import com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 数据安全风险评估策略
 * 采用分层架构设计，支持流程感知和任务内容精细化执行
 * 评估类型编码: data_security_plan
 *
 * 架构说明：
 * - DataSecurityEvaluateExecutor: 负责完整评估流程执行和报告生成
 * - DataSecurityStepManager: 负责步骤级别的执行控制和依赖管理
 * - DataSecurityTaskManager: 负责任务内容的参数化执行
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Component
public class DataSecurityPlanStrategy implements ProcessAwareEvaluateStrategy {

    /** 评估类型编码 */
    private static final String EVALUATE_TYPE = "data_security_plan";
    
    /** 策略名称 */
    private static final String STRATEGY_NAME = "数据安全风险评估方案";

    // ==================== 业务服务依赖 ====================
    
    @Autowired
    private ITargetCompanyInfoService targetCompanyInfoService;
    
    @Autowired
    private IEvaluateCompanyInfoService evaluateCompanyInfoService;
    
    @Autowired
    private IEvaluateModelService evaluateModelService;

    // ==================== 分层组件依赖 ====================
    
    @Autowired
    private DataSecurityEvaluateExecutor evaluateExecutor;
    
    @Autowired
    private DataSecurityStepManager stepManager;
    
    @Autowired
    private DataSecurityTaskManager taskManager;

    // ==================== 基础策略接口实现 ====================

    @Override
    public String getEvaluateType() {
        return EVALUATE_TYPE;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public boolean validateRequest(EvaluatePlanRequest request) {
        if (request == null) {
            log.warn("评估请求为空");
            return false;
        }
        
        if (!StringUtils.hasText(request.getEvaluateType()) || 
            !EVALUATE_TYPE.equals(request.getEvaluateType())) {
            log.warn("评估类型不匹配，期望: {}, 实际: {}", EVALUATE_TYPE, request.getEvaluateType());
            return false;
        }
        
        if (request.getPlanId() == null || request.getPlanId() <= 0) {
            log.warn("计划ID无效: {}", request.getPlanId());
            return false;
        }
        
        if (request.getTargetCompanyId() == null || request.getTargetCompanyId() <= 0) {
            log.warn("被评估单位ID无效: {}", request.getTargetCompanyId());
            return false;
        }
        
        return true;
    }

    @Override
    public EvaluatePlanResponse executeEvaluate(EvaluatePlanRequest request) {
        log.info("开始执行数据安全风险评估，计划ID: {}, 被评估单位ID: {}", 
                request.getPlanId(), request.getTargetCompanyId());
        
        try {
            // 委托给评估执行器处理
            return evaluateExecutor.executeFullEvaluation(request);
        } catch (Exception e) {
            log.error("数据安全风险评估执行异常，计划ID: {}, 异常: {}", request.getPlanId(), e.getMessage(), e);
            return EvaluatePlanResponse.failure(request.getPlanId(), 
                    "数据安全风险评估执行失败: " + e.getMessage());
        }
    }

    @Override
    public String generateReport(EvaluatePlanRequest request) {
        log.info("生成数据安全风险评估报告，计划ID: {}", request.getPlanId());
        
        try {
            // 委托给评估执行器处理
            return evaluateExecutor.generateReport(request);
        } catch (Exception e) {
            log.error("报告生成异常，计划ID: {}, 异常: {}", request.getPlanId(), e.getMessage(), e);
            return "报告生成失败: " + e.getMessage();
        }
    }

    @Override
    public Integer getProgress(Long planId) {
        try {
            // 委托给评估执行器处理
            return evaluateExecutor.getProgress(planId);
        } catch (Exception e) {
            log.error("获取评估进度异常，计划ID: {}, 异常: {}", planId, e.getMessage(), e);
            return 0;
        }
    }

    // ==================== 流程感知接口实现 ====================

    @Override
    public EvaluatePlanResponse executeStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        log.info("执行指定步骤，计划ID: {}, 步骤编码: {}, 流程实例ID: {}", 
                request.getPlanId(), stepCode, processInstanceId);
        
        try {
            // 委托给步骤管理器处理
            return stepManager.executeStep(request, stepCode, processInstanceId);
        } catch (Exception e) {
            log.error("步骤执行异常，计划ID: {}, 步骤编码: {}, 异常: {}", 
                    request.getPlanId(), stepCode, e.getMessage(), e);
            return EvaluatePlanResponse.failure(request.getPlanId(), 
                    "步骤执行失败: " + stepCode + ", 原因: " + e.getMessage());
        }
    }

    @Override
    public boolean validateStepCondition(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        try {
            // 委托给步骤管理器处理
            return stepManager.validateStepCondition(request, stepCode, processInstanceId);
        } catch (Exception e) {
            log.error("步骤条件验证异常，步骤编码: {}, 异常: {}", stepCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Object getStepResult(String stepCode, Long processInstanceId) {
        try {
            // 委托给步骤管理器处理
            return stepManager.getStepResult(stepCode, processInstanceId);
        } catch (Exception e) {
            log.error("获取步骤结果异常，步骤编码: {}, 异常: {}", stepCode, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean handleStepRollback(EvaluatePlanRequest request, String fromStepCode, 
                                     String toStepCode, Long processInstanceId, String reason) {
        log.info("处理步骤回退，从 {} 回退到 {}, 流程实例ID: {}, 原因: {}", 
                fromStepCode, toStepCode, processInstanceId, reason);
        
        try {
            // 委托给步骤管理器处理
            return stepManager.handleStepRollback(request, fromStepCode, toStepCode, processInstanceId, reason);
        } catch (Exception e) {
            log.error("步骤回退处理异常，异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getAvailableRollbackSteps(String currentStepCode, Long processInstanceId) {
        try {
            // 委托给步骤管理器处理
            return stepManager.getAvailableRollbackSteps(currentStepCode, processInstanceId);
        } catch (Exception e) {
            log.error("获取可回退步骤异常，当前步骤: {}, 异常: {}", currentStepCode, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean canSkipStep(String stepCode, Long processInstanceId) {
        try {
            // 委托给步骤管理器处理
            return stepManager.canSkipStep(stepCode, processInstanceId);
        } catch (Exception e) {
            log.error("检查步骤跳过条件异常，步骤编码: {}, 异常: {}", stepCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long getEstimatedStepDuration(String stepCode) {
        try {
            // 委托给步骤管理器处理
            return stepManager.getEstimatedStepDuration(stepCode);
        } catch (Exception e) {
            log.error("获取步骤预估时间异常，步骤编码: {}, 异常: {}", stepCode, e.getMessage(), e);
            return 30 * 60 * 1000L; // 默认30分钟
        }
    }

    @Override
    public List<String> getStepDependencies(String stepCode) {
        try {
            // 委托给步骤管理器处理
            return stepManager.getStepDependencies(stepCode);
        } catch (Exception e) {
            log.error("获取步骤依赖异常，步骤编码: {}, 异常: {}", stepCode, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // ==================== 扩展方法 ====================

    /**
     * 执行指定任务内容
     * 
     * @param request 评估请求
     * @param taskType 任务类型
     * @param taskParams 任务参数
     * @return 任务执行响应
     */
    public EvaluatePlanResponse executeTask(EvaluatePlanRequest request, String taskType, 
                                           Map<String, Object> taskParams) {
        log.info("执行指定任务内容，计划ID: {}, 任务类型: {}", request.getPlanId(), taskType);
        
        try {
            // 委托给任务管理器处理
            return taskManager.executeTask(request, taskType, taskParams);
        } catch (Exception e) {
            log.error("任务内容执行异常，计划ID: {}, 任务类型: {}, 异常: {}", 
                    request.getPlanId(), taskType, e.getMessage(), e);
            return EvaluatePlanResponse.failure(request.getPlanId(), 
                    "任务内容执行失败: " + taskType + ", 原因: " + e.getMessage());
        }
    }

    /**
     * 获取可用任务列表
     * 
     * @return 任务列表
     */
    public List<Map<String, Object>> getAvailableTasks() {
        try {
            // 委托给任务管理器处理
            return taskManager.getAvailableTasks();
        } catch (Exception e) {
            log.error("获取可用任务列表异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 验证任务参数
     * 
     * @param taskType 任务类型
     * @param taskParams 任务参数
     * @return 验证结果
     */
    public boolean validateTaskParams(String taskType, Map<String, Object> taskParams) {
        try {
            // 委托给任务管理器处理
            return taskManager.validateTaskParams(taskType, taskParams);
        } catch (Exception e) {
            log.error("任务参数验证异常，任务类型: {}, 异常: {}", taskType, e.getMessage(), e);
            return false;
        }
    }
}
