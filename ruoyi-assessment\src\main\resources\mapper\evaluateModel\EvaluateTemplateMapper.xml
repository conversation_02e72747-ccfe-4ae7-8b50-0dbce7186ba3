<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluateModel.mapper.EvaluateTemplateMapper">
    
    <resultMap type="EvaluateTemplate" id="EvaluateTemplateResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="modelId"    column="model_id"    />
        <result property="templateFile"    column="template_file"    />
        <result property="remark"    column="remark"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateTemplateVo">
        select id, title, model_id, template_file, remark, status, type, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_template
    </sql>

    <select id="selectEvaluateTemplateList" parameterType="EvaluateTemplate" resultMap="EvaluateTemplateResult">
        <include refid="selectEvaluateTemplateVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="templateFile != null  and templateFile != ''"> and template_file = #{templateFile}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluateTemplateById" parameterType="Long" resultMap="EvaluateTemplateResult">
        <include refid="selectEvaluateTemplateVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateTemplate" parameterType="EvaluateTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="modelId != null">model_id,</if>
            <if test="type != null">type,</if>
            <if test="templateFile != null">template_file,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="type != null">#{type},</if>
            <if test="templateFile != null">#{templateFile},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateTemplate" parameterType="EvaluateTemplate">
        update dsa_evaluate_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="templateFile != null">template_file = #{templateFile},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateTemplateById" parameterType="Long">
        delete from dsa_evaluate_template where id = #{id}
    </delete>

    <delete id="deleteEvaluateTemplateByIds" parameterType="String">
        delete from dsa_evaluate_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>