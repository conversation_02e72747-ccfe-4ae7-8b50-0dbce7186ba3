package com.ruoyi.process.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.process.mapper.ProcessInstanceMapper;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.service.IProcessInstanceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 流程实例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class ProcessInstanceServiceImpl extends ServiceImpl<ProcessInstanceMapper, ProcessInstance> implements IProcessInstanceService {

}
