package com.ruoyi.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ExceptionHandlerUtil;
import com.ruoyi.process.domain.ProcessDefinition;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.enums.ProcessStepStatusEnum;
import com.ruoyi.process.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 流程流转服务实现类
 * 负责流程实例和步骤实例的创建、管理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class ProcessFlowServiceImpl implements IProcessFlowService {

    @Autowired
    private IProcessDefinitionService processDefinitionService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IProcessStepDefinitionService processStepDefinitionService;

    @Autowired
    private IProcessStepInstanceService processStepInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProcessInstance(String type, String processCode, Long businessId, String operator) {
        try {
            if (StringUtils.isEmpty(processCode) || businessId == null) {
                throw new ServiceException("评估类型和业务ID不能为空");
            }

            // 根据评估类型查找流程定义
            ProcessDefinition processDefinition = processDefinitionService.getOne(
                    new LambdaQueryWrapper<ProcessDefinition>()
                            .eq(ProcessDefinition::getCode, processCode)
                            .eq(ProcessDefinition::getStatus, 1));

            if (processDefinition == null) {
                throw new ServiceException("未找到对应的流程定义: " + processCode);
            }

            // 检查是否已存在流程实例
            ProcessInstance existingInstance = processInstanceService.getOne(
                    new LambdaQueryWrapper<ProcessInstance>()
                            .eq(ProcessInstance::getBusinessId, businessId)
                            .eq(ProcessInstance::getProcessId, processDefinition.getId()));

            if (existingInstance != null) {
                log.warn("业务ID {} 已存在流程实例: {}", businessId, existingInstance.getId());
                return existingInstance.getId();
            }

            // 创建流程实例
            ProcessInstance processInstance = new ProcessInstance();
            processInstance.setProcessId(processDefinition.getId());
            processInstance.setBusinessId(businessId);
            processInstance.setType(type);
            processInstance.setCreateBy(operator);
            processInstance.setCreateTime(new Date());

            boolean saveResult = processInstanceService.save(processInstance);
            if (!saveResult) {
                throw new ServiceException("创建流程实例失败");
            }

            log.info("创建流程实例成功，流程实例ID: {}, 业务ID: {}, 流程类型: {}",
                    processInstance.getId(), businessId, processCode);

            return processInstance.getId();

        } catch (Exception e) {
            ExceptionHandlerUtil.handleAndThrow(e, "创建流程实例失败", log,
                    "评估类型: {}, 业务ID: {}", processCode, businessId);
            return null; // 这行代码不会执行，但编译器需要
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProcessStepInstance> createStepInstances(Long processInstanceId, Long planTaskId, String operator) {
        try {
            if (processInstanceId == null || planTaskId == null) {
                throw new ServiceException("流程实例ID和计划任务ID不能为空");
            }

            // 获取流程实例
            ProcessInstance processInstance = processInstanceService.getById(processInstanceId);
            if (processInstance == null) {
                throw new ServiceException("流程实例不存在: " + processInstanceId);
            }

            // 获取流程定义的所有步骤
            List<ProcessStepDefinition> stepDefinitions = processStepDefinitionService.list(
                    new LambdaQueryWrapper<ProcessStepDefinition>()
                            .eq(ProcessStepDefinition::getProcessId, processInstance.getProcessId())
                            .eq(ProcessStepDefinition::getStatus, 1)
                            .orderByAsc(ProcessStepDefinition::getStepOrder));

            if (stepDefinitions.isEmpty()) {
                throw new ServiceException("流程定义中没有配置步骤");
            }

            // 检查是否已存在步骤实例
            List<ProcessStepInstance> existingSteps = processStepInstanceService.list(
                    new LambdaQueryWrapper<ProcessStepInstance>()
                            .eq(ProcessStepInstance::getProcessInstanceId, processInstanceId));

            if (!existingSteps.isEmpty()) {
                log.warn("流程实例 {} 已存在步骤实例，数量: {}", processInstanceId, existingSteps.size());
                return existingSteps;
            }

            // 创建步骤实例
            List<ProcessStepInstance> stepInstances = new ArrayList<>();
            for (ProcessStepDefinition stepDefinition : stepDefinitions) {
                ProcessStepInstance stepInstance = new ProcessStepInstance();
                stepInstance.setProcessInstanceId(processInstanceId);
                stepInstance.setStepDefinitionId(stepDefinition.getId());
                stepInstance.setStepName(stepDefinition.getStepName());
                stepInstance.setStatus(ProcessStepStatusEnum.PENDING.getCode());
                stepInstance.setCreateBy(operator);
                stepInstance.setCreateTime(new Date());

                stepInstances.add(stepInstance);
            }

            // 批量保存步骤实例
            boolean saveResult = processStepInstanceService.saveBatch(stepInstances);
            if (!saveResult) {
                throw new ServiceException("创建步骤实例失败");
            }

            log.info("创建步骤实例成功，流程实例ID: {}, 计划任务ID: {}, 步骤数量: {}",
                    processInstanceId, planTaskId, stepInstances.size());

            return stepInstances;

        } catch (Exception e) {
            ExceptionHandlerUtil.handleAndThrow(e, "创建步骤实例失败", log,
                    "流程实例ID: {}, 计划任务ID: {}", processInstanceId, planTaskId);
            return null; // 这行代码不会执行，但编译器需要
        }
    }

    @Override
    public List<ProcessStepInstance> getStepInstancesByProcessId(Long processInstanceId) {
        if (processInstanceId == null) {
            return new ArrayList<>();
        }

        return processStepInstanceService.list(
                new LambdaQueryWrapper<ProcessStepInstance>()
                        .eq(ProcessStepInstance::getProcessInstanceId, processInstanceId)
                        .orderByAsc(ProcessStepInstance::getId));
    }

    @Override
    public ProcessInstance getProcessInstanceByBusinessId(Long businessId) {
        if (businessId == null) {
            return null;
        }

        return processInstanceService.getOne(
                new LambdaQueryWrapper<ProcessInstance>()
                        .eq(ProcessInstance::getBusinessId, businessId)
                        .orderByDesc(ProcessInstance::getCreateTime)
                        .last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStepInstanceStatus(Long stepInstanceId, Integer status, String operator, String remark) {
        try {
            if (stepInstanceId == null || status == null) {
                return false;
            }

            ProcessStepInstance stepInstance = processStepInstanceService.getById(stepInstanceId);
            if (stepInstance == null) {
                log.warn("步骤实例不存在: {}", stepInstanceId);
                return false;
            }

            stepInstance.setStatus(status);
            stepInstance.setOperator(operator);
            stepInstance.setRemark(remark);
            stepInstance.setUpdateBy(operator);
            stepInstance.setUpdateTime(new Date());

            // 设置开始时间和结束时间
            if (status.equals(ProcessStepStatusEnum.RUNNING.getCode()) && stepInstance.getStartTime() == null) { // 开始执行
                stepInstance.setStartTime(new Date());
            } else if (status.equals(ProcessStepStatusEnum.FINISHED.getCode()) && stepInstance.getEndTime() == null) { // 执行完成
                stepInstance.setEndTime(new Date());
                if (stepInstance.getStartTime() != null) {
                    long duration = stepInstance.getEndTime().getTime() - stepInstance.getStartTime().getTime();
                    stepInstance.setDurationMs(duration);
                }
            }

            boolean updateResult = processStepInstanceService.updateById(stepInstance);
            if (updateResult) {
                log.info("更新步骤实例状态成功，步骤实例ID: {}, 状态: {}", stepInstanceId, status);
            }

            return updateResult;

        } catch (Exception e) {
            log.error("更新步骤实例状态异常，步骤实例ID: {}, 状态: {}, 异常: {}",
                    stepInstanceId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Integer getProcessProgress(Long processInstanceId) {
        if (processInstanceId == null) {
            return 0;
        }

        try {
            List<ProcessStepInstance> stepInstances = getStepInstancesByProcessId(processInstanceId);
            if (stepInstances.isEmpty()) {
                return 0;
            }

            long completedCount = stepInstances.stream()
                    .mapToInt(ProcessStepInstance::getStatus)
                    .filter(status -> status == 2) // 2-已完成
                    .count();

            return (int) ((completedCount * 100) / stepInstances.size());

        } catch (Exception e) {
            log.error("获取流程进度异常，流程实例ID: {}, 异常: {}", processInstanceId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long startProcessInstance(String type, String processCode, Long businessId, String operator) {
        try {
            // 创建流程实例
            Long processInstanceId = createProcessInstance(type, processCode, businessId, operator);

            // 创建步骤实例
            createStepInstances(processInstanceId, businessId, operator);

            log.info("启动流程实例成功，流程实例ID: {}, 流程代码: {}, 业务ID: {}",
                    processInstanceId, processCode, businessId);

            return processInstanceId;

        } catch (Exception e) {
            ExceptionHandlerUtil.handleAndThrow(e, "启动流程实例失败", log,
                    "流程代码: {}, 业务ID: {}", processCode, businessId);
            return null; // 这行代码不会执行，但编译器需要
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeNextStep(Long processInstanceId, String operator, String remark) {
        try {
            if (processInstanceId == null) {
                return false;
            }

            // 获取下一个待执行的步骤
            ProcessStepInstance nextStep = processStepInstanceService.getOne(
                    new LambdaQueryWrapper<ProcessStepInstance>()
                            .eq(ProcessStepInstance::getProcessInstanceId, processInstanceId)
                            .eq(ProcessStepInstance::getStatus, ProcessStepStatusEnum.PENDING.getCode())
                            .orderByAsc(ProcessStepInstance::getId)
                            .last("LIMIT 1"));

            if (nextStep == null) {
                log.warn("没有找到待执行的步骤，流程实例ID: {}", processInstanceId);
                return false;
            }

            // 更新步骤状态为执行中
            return updateStepInstanceStatus(nextStep.getId(), 1, operator, remark);

        } catch (Exception e) {
            log.error("执行下一步骤异常，流程实例ID: {}, 异常: {}", processInstanceId, e.getMessage(), e);
            return false;
        }
    }
}
