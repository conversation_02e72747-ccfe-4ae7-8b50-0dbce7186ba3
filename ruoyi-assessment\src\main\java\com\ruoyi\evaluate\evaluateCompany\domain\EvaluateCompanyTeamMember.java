package com.ruoyi.evaluate.evaluateCompany.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.group.AddGroup;
import com.ruoyi.common.group.EditGroup;
import com.ruoyi.common.group.ListGroup;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

import javax.validation.constraints.NotNull;

/**
 * 评估单位评估团队成员对象 dsa_evaluate_company_team_member
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_company_team_member")
public class EvaluateCompanyTeamMember extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @NotNull(message = "成员id不能为空", groups = {EditGroup.class})
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 所属单位ID */
    @NotNull(message = "所属单位ID不能为空", groups = {AddGroup.class})
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 团队ID */
    @NotNull(message = "团队ID不能为空", groups = {ListGroup.class, AddGroup.class})
    @Excel(name = "团队ID")
    @TableField(value = "team_id")
    private Long teamId;

    /** 分组 */
    @NotNull(message = "团队ID不能为空", groups = {AddGroup.class})
    @Excel(name = "分组")
    @TableField(value = "group_id")
    private Long groupId;

    /** 分组 */
    @NotNull(message = "团队ID不能为空", groups = {AddGroup.class})
    @Excel(name = "分组名称")
    @TableField(exist = false)
    private String groupName;

    /** 角色 */
    @Excel(name = "角色")
    @TableField(value = "role")
    private String role;

    /** 职责 */
    @Excel(name = "职责")
    @TableField(value = "duty")
    private String duty;

    /** 姓名 */
    @Excel(name = "姓名")
    @TableField(value = "name")
    private String name;

    /** 单位 */
    @Excel(name = "单位")
    @TableField(value = "unit")
    private String unit;

    /** 岗位 */
    @Excel(name = "岗位")
    @TableField(value = "position")
    private String position;

    /** 能力资质 */
    @Excel(name = "能力资质")
    @TableField(value = "ability")
    private String ability;

    /** 评估工作经验 */
    @Excel(name = "评估工作经验")
    @TableField(value = "experience")
    private String experience;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}