# 多流程架构设计 - 相同步骤不同数据处理方案

## 概述

本文档描述了如何在相同步骤名称但处理不同数据结构的多流程场景下设计处理器架构。通过具体的`data_security_plan`和`basic_info`两种评估类型的对比，展示架构的灵活性和扩展性。

## 架构设计原则

### 1. 步骤编码 + 评估类型双重标识
```java
// 处理器通过 stepCode + evaluateType 进行精确匹配
public interface IStepDataProcessor {
    String getStepCode();        // 步骤编码，如 "create_plan"
    String getEvaluateType();    // 评估类型，如 "data_security_plan" 或 "basic_info"
}
```

### 2. 工厂类智能分发
```java
// StepDataProcessorFactory 根据双重标识精确匹配处理器
public IStepDataProcessor getProcessor(String evaluateType, String stepCode) {
    // 1. 精确匹配：evaluateType + stepCode
    // 2. 通用匹配：null evaluateType + stepCode
    // 3. 默认处理器：兜底处理
}
```

## 实际案例对比

### 相同步骤：create_plan

#### 数据安全评估 vs 基础信息评估

| 维度 | 数据安全评估 (data_security_plan) | 基础信息评估 (basic_info) |
|------|-----------------------------------|---------------------------|
| **关注点** | 数据资产、安全等级、风险评估 | 组织架构、人员配置、制度建设 |
| **数据结构** | assets, securityLevels, riskMatrix | organizationInfo, evaluationScope, basicRequirements |
| **业务逻辑** | 资产分类、安全等级评定 | 组织信息收集、评估范围确定 |

#### 代码实现对比

**数据安全评估处理器**：
```java
@Component
public class CreatePlanProcessor extends AbstractStepDataProcessor {
    @Override
    public String getStepCode() { return "create_plan"; }
    
    @Override
    public String getEvaluateType() { return "data_security_plan"; }
    
    @Override
    protected void processSpecificStepData(..., StepDataResponse response) {
        // 数据安全特有逻辑
        response.addStepData("assets", generateAssetCategories());
        response.addStepData("securityLevels", generateSecurityLevels());
        response.addStepData("riskFactors", generateRiskFactors());
    }
}
```

**基础信息评估处理器**：
```java
@Component
public class BasicInfoCreatePlanProcessor extends AbstractStepDataProcessor {
    @Override
    public String getStepCode() { return "create_plan"; }
    
    @Override
    public String getEvaluateType() { return "basic_info"; }
    
    @Override
    protected void processSpecificStepData(..., StepDataResponse response) {
        // 基础信息特有逻辑
        response.addStepData("organizationInfo", generateOrganizationInfo());
        response.addStepData("evaluationScope", generateEvaluationScope());
        response.addStepData("basicRequirements", generateBasicRequirements());
    }
}
```

## 完整流程对比

### 数据安全评估流程 (data_security_plan)
1. **create_plan** - 创建评估计划：关注数据资产和安全等级
2. **evaluate_scope** - 确定评估范围：数据流、系统边界
3. **create_team** - 组建评估团队：安全专家、技术人员
4. **system_check** - 系统检查：安全配置、漏洞扫描
5. **sign** - 签字确认：安全责任确认

### 基础信息评估流程 (basic_info)
1. **create_plan** - 创建评估计划：关注组织架构和基础要求
2. **info_collection** - 信息收集：组织信息、人员信息、制度信息
3. **current_analysis** - 现状分析：优势劣势、成熟度评估
4. **gap_analysis** - 差距识别：管理差距、人员差距、制度差距
5. **improvement_recommendations** - 改进建议：实施计划、资源需求

## 数据结构差异示例

### create_plan 步骤数据结构对比

#### 数据安全评估返回结构
```json
{
  "stepCode": "create_plan",
  "stepName": "创建数据安全评估计划",
  "evaluateType": "data_security_plan",
  "stepData": {
    "assets": {
      "byImportance": {"核心资产": 50, "重要资产": 70, "一般资产": 30},
      "bySensitivity": {"高敏感": 40, "中敏感": 80, "低敏感": 30}
    },
    "securityLevels": ["L1-公开", "L2-内部", "L3-机密", "L4-绝密"],
    "riskFactors": ["数据泄露", "未授权访问", "数据篡改"]
  }
}
```

#### 基础信息评估返回结构
```json
{
  "stepCode": "create_plan",
  "stepName": "创建基础信息评估计划",
  "evaluateType": "basic_info",
  "stepData": {
    "organizationInfo": {
      "basicInfo": {"organizationName": "待填写", "organizationType": "企业"},
      "structure": {"departmentCount": 0, "employeeCount": 0}
    },
    "evaluationScope": {
      "dimensions": [
        {"name": "组织管理", "code": "organization_management", "required": true},
        {"name": "人员配置", "code": "personnel_configuration", "required": true}
      ]
    },
    "basicRequirements": {
      "requiredMaterials": ["组织架构图", "人员花名册", "基本制度文件"],
      "timeRequirements": {"estimatedDuration": "2-3周"}
    }
  }
}
```

## 架构优势

### 1. 清晰的职责分离
- 每个处理器只负责特定评估类型的特定步骤
- 避免了单个处理器中的复杂条件判断
- 代码可读性和维护性更好

### 2. 高度的可扩展性
- 新增评估类型只需添加对应的处理器
- 新增步骤只需实现对应的处理器接口
- 不影响现有处理器的逻辑

### 3. 灵活的数据结构
- 每个处理器可以返回完全不同的数据结构
- 通过 `stepData` 字段保持扩展性
- 基础字段统一，扩展字段灵活

### 4. 智能的处理器匹配
- 工厂类支持精确匹配和模糊匹配
- 支持通用处理器和特定处理器
- 提供默认处理器作为兜底方案

## 扩展指南

### 添加新的评估类型
1. 创建对应的处理器类
2. 实现 `IStepDataProcessor` 接口
3. 设置正确的 `stepCode` 和 `evaluateType`
4. 实现特定的业务逻辑

### 添加新的步骤
1. 为每个支持的评估类型创建处理器
2. 或创建通用处理器支持多种评估类型
3. 在数据库中配置对应的步骤定义

### 处理器优先级
使用 `@Order` 注解控制处理器优先级：
- 特定处理器：`@Order(50)`
- 通用处理器：`@Order(100)`
- 默认处理器：`@Order(Integer.MAX_VALUE)`

## 最佳实践

1. **命名规范**：处理器类名包含评估类型前缀，如 `BasicInfoCreatePlanProcessor`
2. **数据结构设计**：保持基础字段统一，扩展字段体现业务特色
3. **错误处理**：在抽象基类中统一处理异常
4. **日志记录**：记录关键的处理过程和结果
5. **单元测试**：为每个处理器编写独立的单元测试

## 工厂类匹配逻辑示例

```java
@Component
public class StepDataProcessorFactory {

    public IStepDataProcessor getProcessor(String evaluateType, String stepCode) {
        // 示例匹配过程
        // 输入：evaluateType="basic_info", stepCode="create_plan"

        // 1. 精确匹配查找
        for (IStepDataProcessor processor : processors) {
            if ("basic_info".equals(processor.getEvaluateType()) &&
                "create_plan".equals(processor.getStepCode())) {
                // 找到：BasicInfoCreatePlanProcessor
                return processor;
            }
        }

        // 2. 通用匹配查找
        for (IStepDataProcessor processor : processors) {
            if (processor.getEvaluateType() == null &&
                "create_plan".equals(processor.getStepCode())) {
                // 找到通用的CreatePlanProcessor（如果存在）
                return processor;
            }
        }

        // 3. 默认处理器
        return defaultProcessor;
    }
}
```

## 处理器注册示例

Spring启动时自动注册的处理器：
```
注册步骤数据处理器成功: CreatePlanProcessor, 键: data_security_plan:create_plan
注册步骤数据处理器成功: BasicInfoCreatePlanProcessor, 键: basic_info:create_plan
注册步骤数据处理器成功: BasicInfoCollectionProcessor, 键: basic_info:info_collection
注册步骤数据处理器成功: BasicInfoAnalysisProcessor, 键: basic_info:current_analysis
注册步骤数据处理器成功: BasicInfoGapAnalysisProcessor, 键: basic_info:gap_analysis
注册步骤数据处理器成功: BasicInfoRecommendationProcessor, 键: basic_info:improvement_recommendations
注册步骤数据处理器成功: DefaultStepDataProcessor, 键: null:null
```

## 总结

通过这种架构设计，我们成功解决了相同步骤名称但处理不同数据结构的多流程场景。架构具有良好的扩展性、可维护性和灵活性，能够支持未来更多评估类型和步骤的添加。

### 关键特性
1. **精确匹配**：通过评估类型+步骤编码实现精确的处理器匹配
2. **数据隔离**：不同评估类型的相同步骤返回完全不同的数据结构
3. **代码复用**：通过抽象基类实现通用逻辑的复用
4. **灵活扩展**：新增评估类型或步骤无需修改现有代码
