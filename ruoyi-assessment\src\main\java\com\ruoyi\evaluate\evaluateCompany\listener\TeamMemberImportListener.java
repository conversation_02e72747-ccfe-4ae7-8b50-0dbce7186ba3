package com.ruoyi.evaluate.evaluateCompany.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ruoyi.evaluate.evaluateCompany.vo.ExcelTeamMemberVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.ruoyi.evaluate.evaluateCompany.enums.TeamGroupEnum;

/**
 * 团队成员导入监听器
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class TeamMemberImportListener extends AnalysisEventListener<ExcelTeamMemberVo> {

    private final List<ExcelTeamMemberVo> cacheList = new ArrayList<>();
    private final List<String> errorMessages = new ArrayList<>();
    private String currentGroupName = "";

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param analysisContext
     */
    @Override
    public void invoke(ExcelTeamMemberVo data, AnalysisContext analysisContext) {
        Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
        data.setRowNumber(rowIndex + 1); // Excel行号从1开始

        log.info("解析Excel行 {}: {}", rowIndex + 1, data);

        // 检查是否所有字段都为空（空行，跳过处理）
        if (isAllFieldsEmpty(data)) {
            log.debug("第{}行为空行，跳过处理", rowIndex + 1);
            return;
        }

        // 检查是否是分组标题行
        if (isGroupTitleRow(data)) {
            currentGroupName = extractGroupName(data);
            log.info("检测到分组: {}", currentGroupName);
            return; // 不添加到缓存列表
        }

        // 检查是否是有效的成员数据行（此方法内部会记录详细的错误信息）
        if (isValidMemberRow(data)) {
            // 验证是否有当前分组
            if (currentGroupName == null || currentGroupName.trim().isEmpty()) {
                String errorMsg = String.format("第%d行数据缺少分组信息，请确保在成员数据前有正确的分组标题行",
                    data.getRowNumber());
                errorMessages.add(errorMsg);
                log.warn(errorMsg);
                return;
            }

            data.setGroupName(currentGroupName);
            cacheList.add(data);
            log.info("添加有效成员数据: 姓名={}, 分组={}", data.getName(), currentGroupName);
        }
        // 如果不是有效的成员数据行，错误信息已经在isValidMemberRow方法中记录了
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel 数据解析完成，共 {} 条有效成员数据", cacheList.size());
        if (cacheList.isEmpty()) {
            errorMessages.add("没有解析到任何有效的成员数据，请检查Excel文件格式是否正确");
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("Excel表头信息: {}", headMap);
        super.invokeHeadMap(headMap, context);
    }

    /**
     * 判断是否是分组标题行
     * 逻辑：只有第一个字段(serialNumber)有值，其他字段均为空，且第一个字段包含分组名称
     */
    private boolean isGroupTitleRow(ExcelTeamMemberVo data) {
        String serialNumber = data.getSerialNumber();

        // 第一个字段必须有值且包含分组名称
        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            return false;
        }

        // 使用枚举动态检查是否包含分组名称
        boolean containsGroupName = containsAnyGroupName(serialNumber);

        if (!containsGroupName) {
            return false;
        }

        // 其他字段必须都为空
        return isFieldEmpty(data.getRole()) &&
               isFieldEmpty(data.getDuty()) &&
               isFieldEmpty(data.getName()) &&
               isFieldEmpty(data.getUnit()) &&
               isFieldEmpty(data.getPosition()) &&
               isFieldEmpty(data.getAbility()) &&
               isFieldEmpty(data.getExperience());
    }

    /**
     * 从数据行中提取分组名称
     * 分组名称主要在第一列(serialNumber)中
     */
    private String extractGroupName(ExcelTeamMemberVo data) {
        // 主要从序号列(A列)提取分组名称
        String serialNumber = data.getSerialNumber();
        if (serialNumber != null) {
            return extractGroupNameFromText(serialNumber);
        }
        return "";
    }

    /**
     * 检查文本是否包含任何分组名称
     */
    private boolean containsAnyGroupName(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 遍历所有枚举值，检查是否包含分组名称
        for (TeamGroupEnum groupEnum : TeamGroupEnum.values()) {
            if (text.contains(groupEnum.getName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从文本中提取分组名称
     */
    private String extractGroupNameFromText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        // 遍历所有枚举值，找到匹配的分组名称
        for (TeamGroupEnum groupEnum : TeamGroupEnum.values()) {
            if (text.contains(groupEnum.getName())) {
                return groupEnum.getName();
            }
        }
        return "";
    }

    /**
     * 判断是否是有效的成员数据行
     * 逻辑：如果不是分组行，则所有字段均不能为空，若其中一个为空则判定为不合法数据
     */
    private boolean isValidMemberRow(ExcelTeamMemberVo data) {
        // 如果是分组标题行，则不是成员数据行
        if (isGroupTitleRow(data)) {
            return false;
        }

        // 检查是否所有字段都为空（空行）
        if (isAllFieldsEmpty(data)) {
            return false; // 空行不是有效的成员数据行
        }

        // 验证所有必要字段都不为空，如果有字段为空则记录错误
        List<String> emptyFields = new ArrayList<>();

        if (isFieldEmpty(data.getSerialNumber())) emptyFields.add("序号(A列)");
        if (isFieldEmpty(data.getRole())) emptyFields.add("角色(B列)");
        if (isFieldEmpty(data.getDuty())) emptyFields.add("职责(C列)");
        if (isFieldEmpty(data.getName())) emptyFields.add("姓名(D列)");
        if (isFieldEmpty(data.getUnit())) emptyFields.add("单位(E列)");
        if (isFieldEmpty(data.getPosition())) emptyFields.add("岗位(F列)");
        if (isFieldEmpty(data.getAbility())) emptyFields.add("能力资质(G列)");
        if (isFieldEmpty(data.getExperience())) emptyFields.add("评估工作经验(H列)");

        if (!emptyFields.isEmpty()) {
            String errorMsg = String.format("第%d行数据不完整，以下字段为空: %s",
                data.getRowNumber(), String.join(", ", emptyFields));
            errorMessages.add(errorMsg);
            log.warn(errorMsg);
            return false;
        }

        return true;
    }

    /**
     * 判断字段是否为空
     */
    private boolean isFieldEmpty(String field) {
        return field == null || field.trim().isEmpty();
    }

    /**
     * 检查是否所有字段都为空
     */
    private boolean isAllFieldsEmpty(ExcelTeamMemberVo data) {
        return (data.getSerialNumber() == null || data.getSerialNumber().trim().isEmpty()) &&
                (data.getRole() == null || data.getRole().trim().isEmpty()) &&
                (data.getDuty() == null || data.getDuty().trim().isEmpty()) &&
                (data.getName() == null || data.getName().trim().isEmpty()) &&
                (data.getUnit() == null || data.getUnit().trim().isEmpty()) &&
                (data.getPosition() == null || data.getPosition().trim().isEmpty()) &&
                (data.getAbility() == null || data.getAbility().trim().isEmpty()) &&
                (data.getExperience() == null || data.getExperience().trim().isEmpty());
    }

    public List<ExcelTeamMemberVo> getCacheList() {
        log.info("有效数据列表 -> {}", cacheList);
        return cacheList;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }
}
