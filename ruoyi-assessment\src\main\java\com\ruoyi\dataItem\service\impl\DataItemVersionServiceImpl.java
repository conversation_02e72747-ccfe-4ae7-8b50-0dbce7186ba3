package com.ruoyi.dataItem.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.dataItem.mapper.DataItemVersionMapper;
import com.ruoyi.dataItem.domain.DataItemVersion;
import com.ruoyi.dataItem.service.IDataItemVersionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.dataItem.domain.DataItem;
import com.ruoyi.dataItem.domain.DataItemSnapshot;
import com.ruoyi.dataItem.service.IDataItemService;
import com.ruoyi.dataItem.service.IDataItemSnapshotService;
import cn.hutool.core.date.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.dataItem.mapper.DataItemMapper;

/**
 * 数据项版本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class DataItemVersionServiceImpl extends ServiceImpl<DataItemVersionMapper, DataItemVersion> implements IDataItemVersionService {

    @Autowired
    private IDataItemService dataItemService;
    @Autowired
    private IDataItemSnapshotService dataItemSnapshotService;
    @Autowired
    private DataItemMapper dataItemMapper;

    @Override
    public boolean add(DataItemVersion dataItemVersion) {
        // 设置版本号
        dataItemVersion.setVersionCode(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
        boolean versionSaved = this.save(dataItemVersion);
        if (!versionSaved) {
            return false;
        }
        // 查询所有数据项（包含逻辑删除的数据）
        List<DataItem> dataItemList = dataItemMapper.selectAllWithDeletedByOrgId(dataItemVersion.getOrgId());
        if (dataItemList == null || dataItemList.isEmpty()) {
            return true;
        }
        // 优化：BeanUtils自动复制同名字段，特殊字段单独处理
        List<DataItemSnapshot> snapshotList = dataItemList.stream().map(item -> {
            DataItemSnapshot snap = new DataItemSnapshot();
            BeanUtils.copyProperties(item, snap);
            snap.setId(null);
            snap.setVersionId(dataItemVersion.getId());
            snap.setDataItemId(item.getId());
            // 类型转换字段
            snap.setDataAmountCount(parseLong(item.getDataAmountCount()));
            snap.setIsAlgorithmAuto(parseInteger(item.getIsAlgorithmAuto()));
            snap.setIsCrossBorder(parseInteger(item.getIsCrossBorder()));
            snap.setIsCrossBorderAssess(parseInteger(item.getIsCrossBorderAssess()));
            snap.setIsExternalShare(parseInteger(item.getIsExternalShare()));
            snap.setIsCrossSubjectFlow(parseInteger(item.getIsCrossSubjectFlow()));
            snap.setIsForeignData(parseInteger(item.getIsForeignData()));
            snap.setIsCriticalInfra(parseInteger(item.getIsCriticalInfra()));
            snap.setIsDataRiskAssess(parseInteger(item.getIsDataRiskAssess()));
            // 快照人和快照时间
            snap.setSnapshotBy(item.getUpdateBy());
            snap.setSnapshotTime(new Date());
            snap.setDelFlag(item.getDelFlag());
            //todo 此处有bug，delFlag一直为0
            log.info("快照数据项：{}", snap.getDelFlag());
            return snap;
        }).collect(Collectors.toList());
        // 批量插入快照

        //todo 插入完之后需要删除原来的已经删除的数据
        return dataItemSnapshotService.saveBatch(snapshotList);
    }

    private Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (Exception e) {
            return null;
        }
    }
    private Long parseLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        try {
            return Long.valueOf(value.toString());
        } catch (Exception e) {
            return null;
        }
    }
}
