<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluateModel.mapper.EvaluateModelMapper">
    
    <resultMap type="EvaluateModel" id="EvaluateModelResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="typeId"    column="type_id"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluateModelVo">
        select id, title, type_id, remark, status, is_enabled, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_model
    </sql>

    <select id="selectEvaluateModelList" parameterType="EvaluateModel" resultMap="EvaluateModelResult">
        <include refid="selectEvaluateModelVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
        </where>
    </select>
    
    <select id="selectEvaluateModelById" parameterType="Long" resultMap="EvaluateModelResult">
        <include refid="selectEvaluateModelVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluateModel" parameterType="EvaluateModel" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="typeId != null">type_id,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluateModel" parameterType="EvaluateModel">
        update dsa_evaluate_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluateModelById" parameterType="Long">
        delete from dsa_evaluate_model where id = #{id}
    </delete>

    <delete id="deleteEvaluateModelByIds" parameterType="String">
        delete from dsa_evaluate_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量更新同type_id下的模型启用状态 -->
    <update id="updateEnabledStatusByTypeId">
        update dsa_evaluate_model
        set is_enabled = #{isEnabled}, update_by = #{updateBy}, update_time = #{updateTime}
        where type_id = #{typeId}
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </update>

</mapper>