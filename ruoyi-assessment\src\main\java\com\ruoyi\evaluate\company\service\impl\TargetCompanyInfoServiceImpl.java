package com.ruoyi.evaluate.company.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.evaluate.company.mapper.TargetCompanyInfoMapper;
import com.ruoyi.evaluate.company.domain.TargetCompanyInfo;
import com.ruoyi.evaluate.company.domain.TargetOrgDeptInfo;
import com.ruoyi.evaluate.company.domain.TargetMemberInfo;
import com.ruoyi.evaluate.company.domain.TargetManageDocuments;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetCompanyInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelContactUserInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetOrgDeptInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetMemberInfo;
import com.ruoyi.evaluate.company.domain.excel.ExcelTargetManageDocuments;
import com.ruoyi.evaluate.company.listener.TargetOrgDeptInfoListener;
import com.ruoyi.evaluate.company.listener.TargetMemberInfoListener;
import com.ruoyi.evaluate.company.listener.TargetManageDocumentsListener;
import com.ruoyi.evaluate.company.utils.FixedCellExcelReader;
import com.ruoyi.evaluate.company.utils.FileSystemMultipartFile;
import com.ruoyi.evaluate.company.service.ITargetCompanyInfoService;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.evaluate.company.service.ITargetOrgDeptInfoService;
import com.ruoyi.evaluate.company.service.ITargetMemberInfoService;
import com.ruoyi.evaluate.company.service.ITargetManageDocumentsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.config.RuoYiConfig;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Stream;

import com.ruoyi.common.utils.file.ZipFileUtils;

/**
 * 被评估单位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@Service
public class TargetCompanyInfoServiceImpl extends ServiceImpl<TargetCompanyInfoMapper, TargetCompanyInfo> implements ITargetCompanyInfoService {

    @Autowired
    private ITargetOrgDeptInfoService targetOrgDeptInfoService;

    @Autowired
    private ITargetMemberInfoService targetMemberInfoService;

    @Autowired
    private ITargetManageDocumentsService targetManageDocumentsService;

    private final String zipPath = RuoYiConfig.getZipPath() + "/targetCompany";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importZip(MultipartFile file) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<ModuleImportResult> moduleResults = new ArrayList<>();
        int totalSuccess = 0;
        int totalFailure = 0;
        StringBuilder allMessages = new StringBuilder();
        String extractDir = null;

        try {
            // 1. 验证ZIP文件
            ZipFileUtils.validateZipFile(file);
            log.info("ZIP文件验证通过: {}", file.getOriginalFilename());

            // 2. 保存ZIP文件到永久存储位置
            File savedZipFile = saveZipFileToPermanentLocation(file);
            log.info("ZIP文件已保存到永久位置: {}", savedZipFile.getAbsolutePath());

            // 3. 创建解压目录（ZIP文件同级目录）
            extractDir = createExtractDirectoryBesideZip(savedZipFile);

            // 4. 解压ZIP文件到同级目录
            ZipFileUtils.ZipExtractionResult extractionResult = ZipFileUtils.extractZipFile(savedZipFile, extractDir);
            log.info("ZIP文件解压完成，解压了{}个文件", extractionResult.getExtractedFileCount());

            // 添加ZIP解压结果到模块结果列表
            String zipMessage = "ZIP保存并解压成功，解压了" + extractionResult.getExtractedFileCount() + "个文件";
            moduleResults.add(new ModuleImportResult("ZIP文件处理", "zip", 1, 0, zipMessage));
            allMessages.append(zipMessage).append("；");

            // 5. 查找Excel文件
            MultipartFile excelFile = findExcelFileInExtractDir(extractDir);
            log.info("找到Excel文件: {}", excelFile.getOriginalFilename());

            // 6. 处理Excel文件的各个Sheet（传入解压目录用于资源文件访问）
            // 1) 导入并保存公司信息
            ImportResult companyResult = importCompanyInfo(excelFile, extractDir);
            totalSuccess += companyResult.getSuccessCount();
            totalFailure += companyResult.getFailureCount();
            allMessages.append(companyResult.getMessage());
            moduleResults.add(new ModuleImportResult("公司信息", "company",
                companyResult.getSuccessCount(), companyResult.getFailureCount(), companyResult.getMessage()));
            Long savedOrgId = companyResult.getOrgId();

            // 检查orgId是否为空，如果为空则直接返回错误
            if (savedOrgId == null) {
                result.put("success", totalSuccess);
                result.put("fail", totalFailure);
                // result.put("message", "公司信息保存失败，无法继续导入其他数据。详情：" + allMessages.toString());
                result.put("result", moduleResults);
                return result;
            }

            // 2) 导入部门信息
            ImportResult deptResult = importDeptInfo(excelFile, savedOrgId);
            totalSuccess += deptResult.getSuccessCount();
            totalFailure += deptResult.getFailureCount();
            allMessages.append(deptResult.getMessage());
            moduleResults.add(new ModuleImportResult("部门信息", "department",
                deptResult.getSuccessCount(), deptResult.getFailureCount(), deptResult.getMessage()));

            // 3) 导入人员信息
            ImportResult memberResult = importMemberInfo(excelFile, savedOrgId);
            totalSuccess += memberResult.getSuccessCount();
            totalFailure += memberResult.getFailureCount();
            allMessages.append(memberResult.getMessage());
            moduleResults.add(new ModuleImportResult("人员信息", "member",
                memberResult.getSuccessCount(), memberResult.getFailureCount(), memberResult.getMessage()));

            // 4) 导入文档信息
            ImportResult documentResult = importDocumentInfo(excelFile, savedOrgId);
            totalSuccess += documentResult.getSuccessCount();
            totalFailure += documentResult.getFailureCount();
            allMessages.append(documentResult.getMessage());
            moduleResults.add(new ModuleImportResult("文档信息", "document",
                documentResult.getSuccessCount(), documentResult.getFailureCount(), documentResult.getMessage()));

            result.put("success", totalSuccess);
            result.put("fail", totalFailure);
            // result.put("message", "导入完成。总计成功：" + totalSuccess + "条，失败：" + totalFailure + "条。详情：" + allMessages.toString());
            result.put("results", moduleResults);

        } catch (Exception e) {
            log.error("导入过程中发生异常: {}", e.getMessage(), e);
            // 添加异常信息到模块结果
            moduleResults.add(new ModuleImportResult("系统异常", "error", 0, 1, "导入失败：" + e.getMessage()));

            result.put("success", totalSuccess);
            result.put("fail", totalFailure + 1);
            // result.put("message", "导入失败：" + e.getMessage() + "。详情：" + allMessages.toString());
            result.put("results", moduleResults);
        } finally {
            // 注意：不再清理解压目录，因为解压的资源文件需要供后续业务调用
            // 解压目录现在位于ZIP文件同级目录，作为永久存储
            log.info("解压目录保留用于后续业务调用: {}", extractDir);
        }

        return result;
    }

    /**
     * 导入结果内部类
     */
    private static class ImportResult {
        private int successCount;
        private int failureCount;
        private String message;
        private Long orgId; // 用于公司信息导入结果

        public ImportResult(int successCount, int failureCount, String message) {
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.message = message;
        }

        public ImportResult(int successCount, int failureCount, String message, Long orgId) {
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.message = message;
            this.orgId = orgId;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public String getMessage() {
            return message;
        }

        public Long getOrgId() {
            return orgId;
        }
    }

    /**
     * 模块导入结果类 - 用于前端展示
     */
    private static class ModuleImportResult {
        private String moduleName;    // 模块名称
        private String moduleType;    // 模块类型（company, department, member, document）
        private int successCount;     // 成功数量
        private int failureCount;     // 失败数量
        private String status;        // 状态（success, failed, partial）
        private String message;       // 详细信息
        private String timestamp;     // 处理时间

        public ModuleImportResult(String moduleName, String moduleType, int successCount, int failureCount, String message) {
            this.moduleName = moduleName;
            this.moduleType = moduleType;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.message = message;
            this.timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 根据成功失败数量确定状态
            if (failureCount == 0 && successCount > 0) {
                this.status = "success";
            } else if (successCount == 0 && failureCount > 0) {
                this.status = "failed";
            } else if (successCount > 0 && failureCount > 0) {
                this.status = "partial";
            } else {
                this.status = "empty";
            }
        }

        // Getters
        public String getModuleName() { return moduleName; }
        public String getModuleType() { return moduleType; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public String getStatus() { return status; }
        public String getMessage() { return message; }
        public String getTimestamp() { return timestamp; }
    }

    /**
     * 导入公司信息（Sheet0 + Sheet1 + Sheet2 F2单元格）
     */
    private ImportResult importCompanyInfo(MultipartFile file, String extractDir) {
        int successCount = 0;
        int failureCount = 0;
        StringBuilder messages = new StringBuilder();
        Long savedOrgId = null;

        try {
            // 读取Sheet1公司基本信息
            ExcelTargetCompanyInfo sheet1Data = null;
            try {
                sheet1Data = FixedCellExcelReader.readSheet1FixedCells(file);
            } catch (Exception e) {
                messages.append("Sheet1读取失败：").append(e.getMessage()).append("；");
            }

            // 读取Sheet0联系人信息
            ExcelContactUserInfo sheet0Data = null;
            try {
                sheet0Data = FixedCellExcelReader.readSheet0FixedCells(file);
            } catch (Exception e) {
                messages.append("Sheet0读取失败：").append(e.getMessage()).append("；");
            }

            // 读取Sheet2组织架构示意图信息（JSON格式）
            String orgFlowChart = "";
            try {
                orgFlowChart = FixedCellExcelReader.readSheet2FlowChart(file, extractDir);
                log.info("读取到组织架构示意图信息: {}", orgFlowChart);
            } catch (Exception e) {
                log.error("读取Sheet2组织架构示意图信息失败: {}", e.getMessage());
            }

            // 合并并保存公司信息
            TargetCompanyInfo company = new TargetCompanyInfo();

            // 从Sheet1复制公司基本信息
            if (sheet1Data != null) {
                BeanUtils.copyProperties(sheet1Data, company);
                company.setStatus(1);
            }

            // 从Sheet0复制联系人信息
            if (sheet0Data != null) {
                mapContactInfo(sheet0Data, company);
            }

            // 设置组织架构示意图链接
            if (StringUtils.isNotEmpty(orgFlowChart)) {
                company.setFlow(orgFlowChart);
            }

            if (this.save(company)) {
                successCount++;
                savedOrgId = company.getId();
                log.info("公司信息保存成功，ID: {}", savedOrgId);
            } else {
                failureCount++;
            }

        } catch (Exception e) {
            failureCount++;
            log.error("保存公司信息失败: {}", e.getMessage());
        }

        messages.append("公司信息：成功").append(successCount).append("条，失败").append(failureCount).append("条；");
        return new ImportResult(successCount, failureCount, messages.toString(), savedOrgId);
    }

    /**
     * 映射联系人信息到公司实体
     */
    private void mapContactInfo(ExcelContactUserInfo contactInfo, TargetCompanyInfo company) {
        if (StringUtils.isNotEmpty(contactInfo.getContactName())) {
            company.setContactName(contactInfo.getContactName());
        }
        if (StringUtils.isNotEmpty(contactInfo.getJob())) {
            company.setJob(contactInfo.getJob());
        }
        if (StringUtils.isNotEmpty(contactInfo.getDept())) {
            company.setDept(contactInfo.getDept());
        }
        if (StringUtils.isNotEmpty(contactInfo.getTel())) {
            company.setTel(contactInfo.getTel());
        }
        if (StringUtils.isNotEmpty(contactInfo.getMobile())) {
            company.setMobile(contactInfo.getMobile());
        }
        if (StringUtils.isNotEmpty(contactInfo.getEmail())) {
            company.setEmail(contactInfo.getEmail());
        }
    }

    /**
     * 导入部门信息（Sheet2）
     */
    private ImportResult importDeptInfo(MultipartFile file, Long orgId) {
        int successCount = 0;
        int failureCount = 0;
        StringBuilder messages = new StringBuilder();

        // 检查orgId是否为空
        if (orgId == null) {
            messages.append("部门信息导入失败：orgId为空；");
            return new ImportResult(0, 0, messages.toString());
        }

        try {
            TargetOrgDeptInfoListener listener = new TargetOrgDeptInfoListener();
            EasyExcel.read(file.getInputStream(), ExcelTargetOrgDeptInfo.class, listener)
                    .headRowNumber(4) // 跳过前4行
                    .sheet(2)
                    .doRead();

            List<ExcelTargetOrgDeptInfo> dataList = listener.getCacheList();
            List<String> errors = listener.getErrorMessages();

            for (ExcelTargetOrgDeptInfo excelDept : dataList) {
                try {
                    if (isEmptyDeptData(excelDept)) {
                        log.debug("跳过空的部门数据行，序号: {}", excelDept.getSerialNumber());
                        continue;
                    }

                    TargetOrgDeptInfo dept = new TargetOrgDeptInfo();
                    mapDeptInfo(excelDept, dept, orgId);

                    if (targetOrgDeptInfoService.save(dept)) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("保存部门信息失败: {}", e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                errors.forEach(error -> messages.append(error).append("；"));
            }

        } catch (Exception e) {
            log.error("读取Sheet2部门信息失败: {}", e.getMessage());
            messages.append("Sheet2读取失败：").append(e.getMessage()).append("；");
        }

        messages.append("部门信息：成功").append(successCount).append("条，失败").append(failureCount).append("条；");
        return new ImportResult(successCount, failureCount, messages.toString());
    }

    /**
     * 映射部门信息
     */
    private void mapDeptInfo(ExcelTargetOrgDeptInfo excelDept, TargetOrgDeptInfo dept, Long orgId) {
        dept.setDepartmentName(excelDept.getDepartmentName());
        dept.setDepartmentDuty(excelDept.getDepartmentDuty());
        dept.setDepartmentLeader(excelDept.getDepartmentLeader());
        dept.setStatus(1);
        dept.setOrgId(orgId); // orgId已在调用前验证过，直接设置
    }

    /**
     * 导入人员信息（Sheet3）
     */
    private ImportResult importMemberInfo(MultipartFile file, Long orgId) {
        int successCount = 0;
        int failureCount = 0;
        StringBuilder messages = new StringBuilder();

        // 检查orgId是否为空
        if (orgId == null) {
            messages.append("人员信息导入失败：orgId为空；");
            return new ImportResult(0, 0, messages.toString());
        }

        try {
            TargetMemberInfoListener listener = new TargetMemberInfoListener();
            EasyExcel.read(file.getInputStream(), ExcelTargetMemberInfo.class, listener)
                    .headRowNumber(4) // 跳过前3行
                    .sheet(3)
                    .doRead();

            List<ExcelTargetMemberInfo> dataList = listener.getCacheList();
            List<String> errors = listener.getErrorMessages();

            for (ExcelTargetMemberInfo excelMember : dataList) {
                try {
                    if (isEmptyMemberData(excelMember)) {
                        log.debug("跳过空的人员数据行，序号: {}", excelMember.getSerialNumber());
                        continue;
                    }

                    TargetMemberInfo member = new TargetMemberInfo();
                    mapMemberInfo(excelMember, member, orgId);

                    if (targetMemberInfoService.save(member)) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("保存人员信息失败: {}", e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                errors.forEach(error -> messages.append(error).append("；"));
            }

        } catch (Exception e) {
            log.error("读取Sheet3人员信息失败: {}", e.getMessage());
            messages.append("Sheet3读取失败：").append(e.getMessage()).append("；");
        }

        messages.append("人员信息：成功").append(successCount).append("条，失败").append(failureCount).append("条；");
        return new ImportResult(successCount, failureCount, messages.toString());
    }

    /**
     * 映射人员信息
     */
    private void mapMemberInfo(ExcelTargetMemberInfo excelMember, TargetMemberInfo member, Long orgId) {
        member.setName(excelMember.getName());
        member.setPost(excelMember.getPost());
        member.setDuty(excelMember.getDuty());
        member.setDepartment(excelMember.getDepartment());
        member.setDataProcessing(excelMember.getDataProcessing());
        member.setFullTime(excelMember.getFullTime());
        member.setNationality(excelMember.getNationality());
        member.setStatus(1);
        member.setOrgId(orgId); // orgId已在调用前验证过，直接设置
    }

    /**
     * 导入文档信息（Sheet4）
     */
    private ImportResult importDocumentInfo(MultipartFile file, Long orgId) {
        int successCount = 0;
        int failureCount = 0;
        StringBuilder messages = new StringBuilder();

        // 检查orgId是否为空
        if (orgId == null) {
            messages.append("文档信息导入失败：orgId为空；");
            return new ImportResult(0, 0, messages.toString());
        }

        try {
            TargetManageDocumentsListener listener = new TargetManageDocumentsListener();
            EasyExcel.read(file.getInputStream(), ExcelTargetManageDocuments.class, listener)
                    .headRowNumber(4) // 跳过前3行
                    .sheet(4)
                    .doRead();

            List<ExcelTargetManageDocuments> dataList = listener.getCacheList();
            List<String> errors = listener.getErrorMessages();

            for (ExcelTargetManageDocuments excelDocument : dataList) {
                try {
                    if (isEmptyDocumentData(excelDocument)) {
                        log.debug("跳过空的文档数据行，序号: {}", excelDocument.getSerialNumber());
                        continue;
                    }

                    TargetManageDocuments document = new TargetManageDocuments();
                    mapDocumentInfo(excelDocument, document, orgId);

                    if (targetManageDocumentsService.save(document)) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("保存文档信息失败: {}", e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                errors.forEach(error -> messages.append(error).append("；"));
            }

        } catch (Exception e) {
            log.error("读取Sheet4文档信息失败: {}", e.getMessage());
            messages.append("Sheet4读取失败：").append(e.getMessage()).append("；");
        }

        messages.append("文档信息：成功").append(successCount).append("条，失败").append(failureCount).append("条；");
        return new ImportResult(successCount, failureCount, messages.toString());
    }

    /**
     * 映射文档信息
     */
    private void mapDocumentInfo(ExcelTargetManageDocuments excelDocument, TargetManageDocuments document, Long orgId) {
        document.setDocumentName(excelDocument.getDocumentName());
        document.setContent(excelDocument.getContent());
        document.setScope(excelDocument.getScope());
        document.setStatus(1);
        document.setOrgId(orgId); // orgId已在调用前验证过，直接设置
    }

    /**
     * 检查部门数据是否为空（只有序号没有其他数据）
     *
     * @param excelDept Excel部门数据
     * @return true表示数据为空，应该跳过
     */
    private boolean isEmptyDeptData(ExcelTargetOrgDeptInfo excelDept) {
        // 检查关键字段是否都为空
        return StringUtils.isEmpty(excelDept.getDepartmentName()) &&
                StringUtils.isEmpty(excelDept.getDepartmentDuty()) &&
                StringUtils.isEmpty(excelDept.getDepartmentLeader());
    }

    /**
     * 检查人员数据是否为空（只有序号没有其他数据）
     *
     * @param excelMember Excel人员数据
     * @return true表示数据为空，应该跳过
     */
    private boolean isEmptyMemberData(ExcelTargetMemberInfo excelMember) {
        // 检查关键字段是否都为空
        return StringUtils.isEmpty(excelMember.getName()) &&
                StringUtils.isEmpty(excelMember.getPost()) &&
                StringUtils.isEmpty(excelMember.getDuty()) &&
                StringUtils.isEmpty(excelMember.getDepartment()) &&
                StringUtils.isEmpty(excelMember.getDataProcessing()) &&
                StringUtils.isEmpty(excelMember.getFullTime()) &&
                StringUtils.isEmpty(excelMember.getNationality());
    }

    /**
     * 检查文档数据是否为空（只有序号没有其他数据）
     *
     * @param excelDocuments Excel文档数据
     * @return true表示数据为空，应该跳过
     */
    private boolean isEmptyDocumentData(ExcelTargetManageDocuments excelDocuments) {
        // 检查关键字段是否都为空
        return StringUtils.isEmpty(excelDocuments.getDocumentName()) &&
                StringUtils.isEmpty(excelDocuments.getContent()) &&
                StringUtils.isEmpty(excelDocuments.getScope());
    }


    /**
     * 创建ZIP文件同级目录的解压目录
     */
    private String createExtractDirectoryBesideZip(File zipFile) throws IOException {
        // 获取ZIP文件的父目录
        Path zipParentDir = zipFile.toPath().getParent();

        // 获取ZIP文件名（不含扩展名）
        String zipFileName = zipFile.getName();
        String nameWithoutExtension = zipFileName.substring(0, zipFileName.lastIndexOf('.'));

        // 创建解压目录名：ZIP文件名_extracted
        String extractDirName = nameWithoutExtension + "_extracted";
        Path extractPath = zipParentDir.resolve(extractDirName);

        // 如果目录已存在，先删除再创建（确保是全新的解压内容）
        if (Files.exists(extractPath)) {
            log.info("解压目录已存在，先清理: {}", extractPath.toAbsolutePath());
            ZipFileUtils.cleanupExtractedFiles(extractPath.toString());
        }

        // 创建解压目录
        Files.createDirectories(extractPath);

        log.info("创建ZIP文件同级解压目录: {}", extractPath.toAbsolutePath());
        return extractPath.toString();
    }

    /**
     * 保存ZIP文件到永久存储位置
     */
    private File saveZipFileToPermanentLocation(MultipartFile zipFile) throws IOException {
        String baseDir = zipPath;

        LocalDateTime now = LocalDateTime.now();
        String yearDir = String.valueOf(now.getYear());
        String monthDir = String.format("%02d", now.getMonthValue());
        String dayDir = String.format("%02d", now.getDayOfMonth());

        String targetDirectory = baseDir + "/" + yearDir + monthDir + dayDir;

        // 创建目录
        Path targetPath = Paths.get(targetDirectory);
        if (!Files.exists(targetPath)) {
            Files.createDirectories(targetPath);
        }

        // 生成文件名
        String originalFilename = zipFile.getOriginalFilename();
        String timestamp = now.format(DateTimeFormatter.ofPattern("HHmmss"));
        String fileName = timestamp + "_" + originalFilename;

        // 保存文件
        Path targetFilePath = targetPath.resolve(fileName);
        zipFile.transferTo(targetFilePath.toFile());

        log.info("ZIP文件已保存到永久位置: {}", targetFilePath.toAbsolutePath());
        return targetFilePath.toFile();
    }

    /**
     * 在解压目录中查找Excel文件
     */
    private MultipartFile findExcelFileInExtractDir(String extractDir) throws IOException {
        Path extractPath = Paths.get(extractDir);

        try (Stream<Path> paths = Files.walk(extractPath)) {
            List<Path> excelFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
                })
                .collect(java.util.stream.Collectors.toList());

            if (excelFiles.isEmpty()) {
                throw new IOException("ZIP文件中未找到Excel文件");
            }

            if (excelFiles.size() > 1) {
                log.warn("ZIP文件中包含多个Excel文件，将使用第一个: {}", excelFiles.get(0).getFileName());
            }

            Path excelPath = excelFiles.get(0);
            log.info("找到Excel文件: {}", excelPath.getFileName());

            // 将文件转换为MultipartFile
            return new FileSystemMultipartFile(excelPath.toFile());
        }
    }




}
