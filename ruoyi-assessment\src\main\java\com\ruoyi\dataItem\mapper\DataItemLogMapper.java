package com.ruoyi.dataItem.mapper;

import com.ruoyi.dataItem.domain.DataItemLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 数据项变更日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface DataItemLogMapper extends BaseMapper<DataItemLog> {

    /**
     * 根据数据项id批量物理删除
     */
    void deleteByDataItemIds(List<Long> dataItemIds);
}
