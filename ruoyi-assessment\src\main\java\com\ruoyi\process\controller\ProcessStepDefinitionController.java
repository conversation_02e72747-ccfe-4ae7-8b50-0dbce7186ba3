package com.ruoyi.process.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.service.IProcessStepDefinitionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程步骤定义Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/process/stepDefinition")
@Api(value = "流程步骤定义控制器", tags = {"流程步骤定义管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessStepDefinitionController extends BaseController
{
    private final IProcessStepDefinitionService processStepDefinitionService;

    /**
     * 查询流程步骤定义列表
     */
    @ApiOperation("查询流程步骤定义列表")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessStepDefinition processStepDefinition) {
        startPage();
        List<ProcessStepDefinition> list = processStepDefinitionService.list(new QueryWrapper<ProcessStepDefinition>(processStepDefinition));
        return getDataTable(list);
    }

    /**
     * 获取流程步骤定义详细信息
     */
    @ApiOperation("获取流程步骤定义详细信息")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processStepDefinitionService.getById(id));
    }

    /**
     * 新增流程步骤定义
     */
    @ApiOperation("新增流程步骤定义")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:add')")
    @Log(title = "流程步骤定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessStepDefinition processStepDefinition) {
        return toAjax(processStepDefinitionService.save(processStepDefinition));
    }

    /**
     * 修改流程步骤定义
     */
    @ApiOperation("修改流程步骤定义")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:edit')")
    @Log(title = "流程步骤定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessStepDefinition processStepDefinition) {
        return toAjax(processStepDefinitionService.updateById(processStepDefinition));
    }

    /**
     * 删除流程步骤定义
     */
    @ApiOperation("删除流程步骤定义")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:remove')")
    @Log(title = "流程步骤定义", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processStepDefinitionService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取下个步骤信息
     */
    @ApiOperation("获取下个步骤信息")
    @PreAuthorize("@ss.hasPermi('process:stepDefinition:query')")
    @GetMapping("/nextStep")
    public AjaxResult getNextStep(@ApiParam(value = "流程ID", required = true) Long processId,
                                  @ApiParam(value = "当前步骤顺序", required = true) Long currentStepOrder) {
        if (processId == null || currentStepOrder == null) {
            return AjaxResult.error("流程ID和当前步骤顺序不能为空");
        }

        ProcessStepDefinition nextStep = processStepDefinitionService.getNextStep(processId, currentStepOrder);

        if (nextStep == null) {
            return AjaxResult.error("未找到下个步骤信息，可能已是最后一个步骤");
        }

        return AjaxResult.success(nextStep);
    }
}