package com.ruoyi.evaluate.evaluatePlan.util;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.process.domain.ProcessStepInstance;

import java.util.Date;
import java.util.Map;

/**
 * 步骤数据转换工具类
 * <p>
 * 提供Map和实体类之间的转换功能，帮助逐步迁移到类型安全的实体类
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public class StepDataConverter {

    /**
     * 构建基础步骤数据响应
     */
    public static StepDataResponse buildBaseStepDataResponse(EvaluatePlanTask planTask,
                                                             ProcessStepInstance stepInstance,
                                                             String stepCode,
                                                             String evaluateType) {
        return StepDataResponse.builder()
                .stepCode(stepCode)
                .stepName(stepInstance.getStepName())
                .evaluateType(evaluateType)
                .planTask(planTask)
                .stepInstanceId(stepInstance.getId())
                .stepInstance(stepInstance)
                .build();
    }


    /**
     * 获取状态名称
     */
    private static String getStatusName(ProcessStepInstance stepInstance) {
        if (stepInstance == null) {
            return "未开始";
        }

        Integer status = stepInstance.getStatus();
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 0:
                return "待执行";
            case 1:
                return "执行中";
            case 2:
                return "已完成";
            case 3:
                return "已暂停";
            case 4:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 计算持续时间
     */
    private static Long calculateDuration(ProcessStepInstance stepInstance) {
        if (stepInstance == null || stepInstance.getStartTime() == null) {
            return null;
        }

        Date endTime = stepInstance.getEndTime() != null ? stepInstance.getEndTime() : new Date();
        return endTime.getTime() - stepInstance.getStartTime().getTime();
    }

    /**
     * 合并扩展数据到响应对象
     */
    public static void mergeExtensionData(StepDataResponse response, Map<String, Object> extensionData) {
        if (response != null && extensionData != null) {
            response.addStepData(extensionData);
        }
    }


}
