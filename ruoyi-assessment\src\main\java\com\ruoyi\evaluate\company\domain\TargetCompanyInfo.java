package com.ruoyi.evaluate.company.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 被评估单位对象 dsa_target_company_info
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_target_company_info")
public class TargetCompanyInfo extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** 序号 */
    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

    /** 单位简称 */
    @Excel(name = "单位简称")
    @TableField(value = "simple_company_name")
    private String simpleCompanyName;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @TableField(value = "company_address")
    private String companyAddress;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    @TableField(value = "post_code")
    private String postCode;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /** 职务/职称 */
    @Excel(name = "职务/职称")
    @TableField(value = "job")
    private String job;

    /** 所属部门 */
    @Excel(name = "所属部门")
    @TableField(value = "dept")
    private String dept;

    /** 办公电话 */
    @Excel(name = "办公电话")
    @TableField(value = "tel")
    private String tel;

    /** 移动电话 */
    @Excel(name = "移动电话")
    @TableField(value = "mobile")
    private String mobile;

    /** 电子邮件 */
    @Excel(name = "电子邮件")
    @TableField(value = "email")
    private String email;

    /** 注册地 */
    @Excel(name = "注册地")
    @TableField(value = "register_place")
    private String registerPlace;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    @TableField(value = "credit_code")
    private String creditCode;

    /** 组织机构类型，参照GB/T 20091-2021填写 */
    @Excel(name = "组织机构类型，参照GB/T 20091-2021填写")
    @TableField(value = "org_type")
    private String orgType;

    /** 法定代表人姓名及国籍 */
    @Excel(name = "法定代表人姓名及国籍")
    @TableField(value = "legal_representative")
    private String legalRepresentative;

    /** 分支单位 */
    @Excel(name = "分支单位")
    @TableField(value = "branch_unit")
    private String branchUnit;

    /** 运营控制情况 */
    @Excel(name = "运营控制情况")
    @TableField(value = "operation_control")
    private String operationControl;

    /** 人员情况 */
    @Excel(name = "人员情况")
    @TableField(value = "personnel_situation")
    private String personnelSituation;

    /** 经营范围 */
    @Excel(name = "经营范围")
    @TableField(value = "business_scope")
    private String businessScope;

    /** 上市情况 */
    @Excel(name = "上市情况")
    @TableField(value = "listing_situation")
    private String listingSituation;

    /** 是否开展了数据分类分级 */
    @Excel(name = "是否开展了数据分类分级")
    @TableField(value = "data_classification")
    private String dataClassification;

    /** 重要数据、核心数据是否备案 */
    @Excel(name = "重要数据、核心数据是否备案")
    @TableField(value = "data_record")
    private String dataRecord;

    /** 备案回复情况 */
    @Excel(name = "备案回复情况")
    @TableField(value = "record_reply")
    private String recordReply;

    /** 评估次数 */
    @Excel(name = "评估次数")
    @TableField(value = "times_number")
    private String timesNumber;

    /** 组织架构图 */
    @Excel(name = "组织架构图")
    @TableField(value = "flow")
    private String flow;

    /** 背景logo图 */
    @Excel(name = "背景logo图")
    @TableField(value = "bg_logo")
    private String bgLogo;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;







}