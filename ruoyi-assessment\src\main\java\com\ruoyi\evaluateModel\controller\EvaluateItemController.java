package com.ruoyi.evaluateModel.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.TemplateDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluateModel.domain.EvaluateItem;
import com.ruoyi.evaluateModel.service.IEvaluateItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估项Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/evaluateModel/item")
@Api(value = "评估项控制器", tags = {"评估项管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateItemController extends BaseController {
    private final IEvaluateItemService evaluateItemService;

    /**
     * 查询评估项列表
     */
    @ApiOperation("查询评估项列表")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluateItem evaluateItem) {
        startPage();
        List<EvaluateItem> list = evaluateItemService.list(new QueryWrapper<EvaluateItem>(evaluateItem));
        return getDataTable(list);
    }

    /**
     * 获取评估项详细信息
     */
    @ApiOperation("获取评估项详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateItemService.getById(id));
    }

    /**
     * 导出评估项列表
     */
    @ApiOperation("导出评估项列表")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:export')")
    @Log(title = "评估项列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluateItem evaluateItem) {
        List<EvaluateItem> list = evaluateItemService.list(new QueryWrapper<EvaluateItem>(evaluateItem));
        ExcelUtil<EvaluateItem> util = new ExcelUtil<EvaluateItem>(EvaluateItem.class);
        util.exportExcel(response, list, "评估项列表");
    }

    /**
     * 新增评估项
     */
    @ApiOperation("新增评估项")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:add')")
    @Log(title = "评估项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EvaluateItem evaluateItem) {
        return toAjax(evaluateItemService.save(evaluateItem));
    }

    /**
     * 修改评估项
     */
    @ApiOperation("修改评估项")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:edit')")
    @Log(title = "评估项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EvaluateItem evaluateItem) {
        return toAjax(evaluateItemService.updateById(evaluateItem));
    }

    /**
     * 删除评估项
     */
    @ApiOperation("删除评估项")
    @PreAuthorize("@ss.hasPermi('evaluateModel:item:remove')")
    @Log(title = "评估项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateItemService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取评估计划清单Excel模板
     *
     * @param response
     */
    @ApiOperation("获取评估项导入模板，文件流形式")
    @GetMapping("/getExcelTemplate")
    public void getExcelTemplate(HttpServletResponse response) {
        String configPath = RuoYiConfig.getConfigPath();
        String templatePath = configPath + "/template/evaluate_model/评估项模板.xlsx";
        TemplateDownloadUtil.downloadTemplate(response, templatePath, "评估项模板.xlsx");
    }
}