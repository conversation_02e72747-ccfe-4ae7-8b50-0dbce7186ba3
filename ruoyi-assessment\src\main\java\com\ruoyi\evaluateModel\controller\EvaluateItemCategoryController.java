package com.ruoyi.evaluateModel.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluateModel.domain.EvaluateItemCategory;
import com.ruoyi.evaluateModel.service.IEvaluateItemCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 评估项分类Controller
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/evaluateModel/category")
@Api(value = "评估项分类控制器", tags = {"评估项分类管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluateItemCategoryController extends BaseController
{
    private final IEvaluateItemCategoryService evaluateItemCategoryService;

    /**
     * 查询评估项分类列表
     */
    @ApiOperation("查询评估项分类列表")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:list')")
    @GetMapping("/list")
    public AjaxResult list(EvaluateItemCategory evaluateItemCategory) {
        List<EvaluateItemCategory> list = evaluateItemCategoryService.list(new QueryWrapper<EvaluateItemCategory>(evaluateItemCategory));
        return AjaxResult.success(list);
    }

    /**
     * 获取评估项分类详细信息
     */
    @ApiOperation("获取评估项分类详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluateItemCategoryService.getById(id));
    }

    /**
     * 新增评估项分类
     */
    @ApiOperation("新增评估项分类")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:add')")
    @Log(title = "评估项分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EvaluateItemCategory evaluateItemCategory) {
        return toAjax(evaluateItemCategoryService.save(evaluateItemCategory));
    }

    /**
     * 修改评估项分类
     */
    @ApiOperation("修改评估项分类")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:edit')")
    @Log(title = "评估项分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EvaluateItemCategory evaluateItemCategory) {
        return toAjax(evaluateItemCategoryService.updateById(evaluateItemCategory));
    }

    /**
     * 删除评估项分类
     */
    @ApiOperation("删除评估项分类")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:remove')")
    @Log(title = "评估项分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluateItemCategoryService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 批量更新评估项分类排序
     */
    @ApiOperation("批量更新评估项分类排序")
    @PreAuthorize("@ss.hasPermi('evaluateModel:category:edit')")
    @Log(title = "评估项分类排序", businessType = BusinessType.UPDATE)
    @PostMapping("/sort")
    public AjaxResult updateSort(@Validated @RequestBody List<EvaluateItemCategory> categories) {
        return toAjax(evaluateItemCategoryService.updateSort(categories));
    }
}