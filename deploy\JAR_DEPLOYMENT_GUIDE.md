# JAR包容器化部署指南

## 📦 JAR包准备

### 1. JAR包命名规范
- **文件名**: `ruoyi_admin.jar`
- **位置**: `deploy/` 目录下
- **要求**: 可执行的Spring Boot JAR包

### 2. 准备JAR包
```bash
# 从构建目录复制
cp ../ruoyi-admin/target/ruoyi-admin.jar ruoyi_admin.jar

# 从其他位置复制
cp /path/to/your/ruoyi-admin.jar ruoyi_admin.jar

# 重命名现有文件
mv your-app.jar ruoyi_admin.jar
```

### 3. 验证JAR包
```bash
# 检查JAR包是否可执行
java -jar ruoyi_admin.jar --help

# 查看JAR包信息
jar -tf ruoyi_admin.jar | head -10

# 检查文件大小（通常应该在50-200MB之间）
ls -lh ruoyi_admin.jar
```

## 🚀 容器化运行流程

### 1. Dockerfile处理
```dockerfile
# 复制JAR包到容器
COPY ruoyi_admin.jar /app/app.jar

# 设置启动命令
CMD ["java", "$JAVA_OPTS", "-jar", "/app/app.jar", "--spring.profiles.active=prod"]
```

### 2. 容器内路径映射
```
宿主机: deploy/ruoyi_admin.jar
容器内: /app/app.jar
```

### 3. 实际运行命令
容器启动时执行：
```bash
java -Dname=ruoyi_admin.jar \
     -Duser.timezone=Asia/Shanghai \
     -Xms512m \
     -Xmx1024m \
     -XX:MetaspaceSize=128m \
     -XX:MaxMetaspaceSize=512m \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:+PrintGCDateStamps \
     -XX:+PrintGCDetails \
     -XX:NewRatio=1 \
     -XX:SurvivorRatio=30 \
     -XX:+UseParallelGC \
     -XX:+UseParallelOldGC \
     -jar /app/app.jar \
     --spring.profiles.active=prod
```

## 🔧 环境配置

### 1. 动态配置
通过环境变量配置应用：
```yaml
environment:
  SPRING_PROFILES_ACTIVE: prod
  SPRING_DATASOURCE_URL: ****************************************
  SPRING_DATASOURCE_USERNAME: data_security_v3
  SPRING_DATASOURCE_PASSWORD: dKSaKe3aRTs7xAhb
  SPRING_REDIS_HOST: redis
  SPRING_REDIS_PORT: 6379
  SPRING_REDIS_PASSWORD: secdriver@q1!w2@
```

### 2. 数据卷挂载
```yaml
volumes:
  - app_logs:/app/logs          # 应用日志
  - app_upload:/app/uploadPath  # 文件上传
  - app_config:/app/config      # 配置文件
```

## 📋 部署步骤

### 1. 检查环境
```bash
# 检查Docker环境
docker --version
docker-compose --version

# 检查JAR包
./deploy.sh check
```

### 2. 启动服务
```bash
# 启动所有服务
./deploy.sh start

# 查看启动状态
./deploy.sh status
```

### 3. 验证部署
```bash
# 查看应用日志
./deploy.sh logs ruoyi-app

# 测试应用访问
curl http://localhost:8080/actuator/health

# 检查数据库连接
./deploy.sh logs mysql
```

## 🔍 故障排除

### 1. JAR包问题
```bash
# 问题：JAR包不存在
# 解决：确保ruoyi_admin.jar在deploy目录下

# 问题：JAR包无法执行
# 检查：java -jar ruoyi_admin.jar --version

# 问题：JAR包损坏
# 解决：重新复制或重新编译
```

### 2. 容器启动问题
```bash
# 查看容器状态
docker-compose ps

# 查看详细日志
docker-compose logs ruoyi-app

# 进入容器调试
docker-compose exec ruoyi-app sh
```

### 3. 应用连接问题
```bash
# 检查网络连接
docker-compose exec ruoyi-app ping mysql
docker-compose exec ruoyi-app ping redis

# 检查端口监听
docker-compose exec ruoyi-app netstat -tlnp
```

## 📊 监控维护

### 1. 性能监控
```bash
# 查看容器资源使用
docker stats ruoyi-app

# 查看JVM内存使用
docker-compose exec ruoyi-app jstat -gc 1
```

### 2. 日志管理
```bash
# 实时查看日志
./deploy.sh logs ruoyi-app

# 查看最近日志
docker-compose logs --tail=100 ruoyi-app

# 日志文件位置
# 容器内：/app/logs/
# 数据卷：app_logs
```

### 3. 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/actuator/health

# 容器健康状态
docker ps --format "table {{.Names}}\t{{.Status}}"
```

## 💡 最佳实践

### 1. JAR包优化
- 使用Spring Boot Maven插件打包
- 确保包含所有必要依赖
- 控制JAR包大小，避免过大

### 2. 部署优化
- 定期清理无用的Docker镜像
- 监控容器资源使用情况
- 设置合适的JVM参数

### 3. 安全考虑
- 定期更新基础镜像
- 使用非root用户运行
- 限制容器资源使用

---

**注意**: 此方案专为预编译JAR包设计，提供了完整的容器化部署解决方案。
