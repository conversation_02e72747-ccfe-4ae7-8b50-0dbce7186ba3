package com.ruoyi.evaluatePlan.strategy.impl.dataSecurity;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 数据安全评估步骤管理器
 * 负责步骤级别的执行控制、依赖管理、回退处理等
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Component
public class DataSecurityStepManager {

    /** 步骤执行结果缓存 */
    private final Map<String, Map<String, Object>> stepResultCache = new HashMap<>();

    /**
     * 执行指定步骤
     */
    public EvaluatePlanResponse executeStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        // 验证步骤执行条件
        if (!validateStepCondition(request, stepCode, processInstanceId)) {
            throw new ServiceException("步骤执行条件不满足: " + stepCode);
        }
        
        // 执行具体步骤
        Map<String, Object> stepResult = executeSpecificStep(request, stepCode, processInstanceId);
        
        // 缓存步骤结果
        String cacheKey = processInstanceId + "_" + stepCode;
        stepResultCache.put(cacheKey, stepResult);
        
        // 构建响应
        EvaluatePlanResponse response = EvaluatePlanResponse.success(
                request.getPlanId(), "步骤执行成功: " + stepCode);
        
        response.setProgress(calculateStepProgress(stepCode))
                .setStatus(1) // 进行中
                .setResultData(stepResult);
        
        // 添加步骤信息
        EvaluatePlanResponse.EvaluateStep step = new EvaluatePlanResponse.EvaluateStep()
                .setStepName(getStepNameByCode(stepCode))
                .setStepStatus(2) // 已完成
                .setDescription("步骤执行完成")
                .setResult(stepResult.toString())
                .setExecuteTime(new Date());
        
        response.setSteps(Arrays.asList(step));
        
        log.info("步骤执行完成，步骤编码: {}, 流程实例ID: {}", stepCode, processInstanceId);
        return response;
    }

    /**
     * 验证步骤执行条件
     */
    public boolean validateStepCondition(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        // 检查依赖步骤是否完成
        List<String> dependencies = getStepDependencies(stepCode);
        for (String dependency : dependencies) {
            String cacheKey = processInstanceId + "_" + dependency;
            if (!stepResultCache.containsKey(cacheKey)) {
                log.warn("依赖步骤未完成: {}", dependency);
                return false;
            }
        }
        return true;
    }

    /**
     * 获取步骤执行结果
     */
    public Object getStepResult(String stepCode, Long processInstanceId) {
        String cacheKey = processInstanceId + "_" + stepCode;
        return stepResultCache.get(cacheKey);
    }

    /**
     * 处理步骤回退
     */
    public boolean handleStepRollback(EvaluatePlanRequest request, String fromStepCode, 
                                     String toStepCode, Long processInstanceId, String reason) {
        log.info("处理步骤回退，从 {} 回退到 {}, 原因: {}", fromStepCode, toStepCode, reason);
        
        try {
            // 清除回退步骤之后的缓存结果
            String[] allSteps = {"data_asset_identify", "data_classify", "risk_identify", 
                               "risk_assessment", "control_measures", "report_generate"};
            
            boolean startClear = false;
            for (String step : allSteps) {
                if (step.equals(toStepCode)) {
                    startClear = true;
                    continue;
                }
                if (startClear) {
                    String cacheKey = processInstanceId + "_" + step;
                    stepResultCache.remove(cacheKey);
                }
            }
            
            log.info("步骤回退处理完成");
            return true;
            
        } catch (Exception e) {
            log.error("步骤回退处理异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取可回退的步骤列表
     */
    public List<String> getAvailableRollbackSteps(String currentStepCode, Long processInstanceId) {
        List<String> availableSteps = new ArrayList<>();
        String[] allSteps = {"data_asset_identify", "data_classify", "risk_identify", 
                           "risk_assessment", "control_measures", "report_generate"};
        
        for (String step : allSteps) {
            if (step.equals(currentStepCode)) {
                break;
            }
            String cacheKey = processInstanceId + "_" + step;
            if (stepResultCache.containsKey(cacheKey)) {
                availableSteps.add(step);
            }
        }
        
        return availableSteps;
    }

    /**
     * 检查是否可以跳过某个步骤
     */
    public boolean canSkipStep(String stepCode, Long processInstanceId) {
        // 某些关键步骤不能跳过
        Set<String> criticalSteps = new HashSet<>(Arrays.asList("risk_identify", "risk_assessment"));
        return !criticalSteps.contains(stepCode);
    }

    /**
     * 获取步骤预估执行时间
     */
    public Long getEstimatedStepDuration(String stepCode) {
        Map<String, Long> durations = new HashMap<>();
        durations.put("data_asset_identify", 30 * 60 * 1000L); // 30分钟
        durations.put("data_classify", 20 * 60 * 1000L);       // 20分钟
        durations.put("risk_identify", 45 * 60 * 1000L);       // 45分钟
        durations.put("risk_assessment", 60 * 60 * 1000L);     // 60分钟
        durations.put("control_measures", 40 * 60 * 1000L);    // 40分钟
        durations.put("report_generate", 15 * 60 * 1000L);     // 15分钟
        
        return durations.getOrDefault(stepCode, 30 * 60 * 1000L);
    }

    /**
     * 获取步骤依赖关系
     */
    public List<String> getStepDependencies(String stepCode) {
        Map<String, List<String>> dependencies = new HashMap<>();
        dependencies.put("data_asset_identify", new ArrayList<>());
        dependencies.put("data_classify", Arrays.asList("data_asset_identify"));
        dependencies.put("risk_identify", Arrays.asList("data_classify"));
        dependencies.put("risk_assessment", Arrays.asList("risk_identify"));
        dependencies.put("control_measures", Arrays.asList("risk_assessment"));
        dependencies.put("report_generate", Arrays.asList("control_measures"));
        
        return dependencies.getOrDefault(stepCode, new ArrayList<>());
    }

    /**
     * 执行具体步骤
     */
    private Map<String, Object> executeSpecificStep(EvaluatePlanRequest request, String stepCode, Long processInstanceId) {
        Map<String, Object> stepResult = new HashMap<>();
        
        switch (stepCode) {
            case "data_asset_identify":
                stepResult.put("identifiedAssets", 150);
                stepResult.put("assetCategories", Arrays.asList("数据库", "文件系统", "应用数据"));
                break;
            case "data_classify":
                stepResult.put("classifiedAssets", 150);
                stepResult.put("sensitivityLevels", Arrays.asList("高敏感", "中敏感", "低敏感"));
                break;
            case "risk_identify":
                stepResult.put("identifiedRisks", 18);
                stepResult.put("riskCategories", Arrays.asList("访问控制", "数据泄露", "系统漏洞"));
                break;
            case "risk_assessment":
                stepResult.put("assessedRisks", 18);
                // JDK8兼容的Map创建方式
                Map<String, Integer> riskDistribution = new HashMap<>();
                riskDistribution.put("高风险", 4);
                riskDistribution.put("中风险", 8);
                riskDistribution.put("低风险", 6);
                stepResult.put("riskDistribution", riskDistribution);
                break;
            case "control_measures":
                stepResult.put("proposedMeasures", 25);
                stepResult.put("measureTypes", Arrays.asList("技术措施", "管理措施", "物理措施"));
                break;
            case "report_generate":
                stepResult.put("reportGenerated", true);
                stepResult.put("reportSections", Arrays.asList("概述", "风险分析", "控制建议", "附录"));
                break;
            default:
                stepResult.put("message", "未知步骤: " + stepCode);
        }
        
        stepResult.put("stepCode", stepCode);
        stepResult.put("executeTime", new Date());
        stepResult.put("processInstanceId", processInstanceId);
        
        return stepResult;
    }

    /**
     * 根据步骤编码获取步骤名称
     */
    private String getStepNameByCode(String stepCode) {
        Map<String, String> stepNames = new HashMap<>();
        stepNames.put("data_asset_identify", "数据资产识别");
        stepNames.put("data_classify", "数据分类分级");
        stepNames.put("risk_identify", "风险识别分析");
        stepNames.put("risk_assessment", "风险等级评定");
        stepNames.put("control_measures", "控制措施建议");
        stepNames.put("report_generate", "报告生成");
        return stepNames.getOrDefault(stepCode, stepCode);
    }

    /**
     * 计算步骤进度
     */
    private Integer calculateStepProgress(String stepCode) {
        Map<String, Integer> progressMap = new HashMap<>();
        progressMap.put("data_asset_identify", 15);
        progressMap.put("data_classify", 30);
        progressMap.put("risk_identify", 50);
        progressMap.put("risk_assessment", 70);
        progressMap.put("control_measures", 85);
        progressMap.put("report_generate", 100);
        return progressMap.getOrDefault(stepCode, 0);
    }
}
