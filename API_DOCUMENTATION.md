# 评估团队成员导入功能 API 文档

## 功能概述

在 `EvaluatePlanTeamMemberController` 中新增了一个导入功能，可以从评估团队（`EvaluateCompanyTeam`）中导入成员到评估计划团队成员表（`EvaluatePlanTeamMember`）中。

## API 接口

### 导入评估团队成员

**接口地址：** `POST /teamMember/member/importFromTeam`

**接口描述：** 从指定的评估团队导入成员到当前评估计划中

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| teamId | Long | 是 | 评估团队ID |
| planId | Long | 是 | 评估计划ID |

**请求示例：**
```http
POST /teamMember/member/importFromTeam
Content-Type: application/x-www-form-urlencoded

teamId=1&planId=2
```

**响应格式：**

成功响应：
```json
{
    "code": 200,
    "msg": "成功导入 5 名团队成员",
    "data": null
}
```

失败响应：
```json
{
    "code": 500,
    "msg": "团队ID不能为空",
    "data": null
}
```

**权限要求：** `teamMember:member:import`

## 实现特点

### 1. 架构设计
- **控制器层**：负责参数接收和响应处理，业务逻辑委托给服务层
- **服务层**：包含完整的业务逻辑实现，包括参数校验、数据查询、转换和保存

### 2. 技术实现
- **Lambda表达式查询**：使用 MyBatis-Plus 的 lambda 查询方式，提高代码可读性和类型安全
- **BeanUtils属性复制**：使用 `BeanUtils.copyBeanProp()` 进行实体类属性复制，减少手动赋值代码
- **异常处理**：统一的异常处理机制，区分业务异常和系统异常

### 3. 数据转换逻辑
- 从 `EvaluateCompanyTeamMember` 转换为 `EvaluatePlanTeamMember`
- 自动处理字段类型转换（如 Long 转 Integer）
- 清空主键ID，让数据库自动生成新的ID
- 设置正确的计划ID和创建人信息

### 4. 查询条件
- 根据团队ID查询成员
- 只查询未删除的记录（del_flag = '0'）

## 使用场景

1. **评估计划创建**：在创建新的评估计划时，可以快速导入已有评估团队的成员结构
2. **团队复用**：将成熟的评估团队配置复用到新的评估项目中
3. **批量操作**：避免逐个添加团队成员的繁琐操作

## 注意事项

1. **数据完整性**：导入前会校验源团队是否存在成员数据
2. **权限控制**：需要相应的导入权限才能执行操作
3. **事务处理**：批量保存操作具有事务性，要么全部成功，要么全部失败
4. **日志记录**：重要操作会记录详细的日志信息，便于问题排查

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 500 | 团队ID不能为空 | 请求参数中缺少teamId |
| 500 | 计划ID不能为空 | 请求参数中缺少planId |
| 500 | 指定团队中没有找到成员数据 | 源团队没有可导入的成员 |
| 500 | 保存团队成员失败 | 数据库保存操作失败 |
| 500 | 导入失败: [具体错误信息] | 其他系统异常 |
