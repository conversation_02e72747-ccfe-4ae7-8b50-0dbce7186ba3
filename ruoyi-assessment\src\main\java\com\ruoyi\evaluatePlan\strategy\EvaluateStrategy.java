package com.ruoyi.evaluatePlan.strategy;

import com.ruoyi.evaluatePlan.domain.EvaluatePlanRequest;
import com.ruoyi.evaluatePlan.domain.EvaluatePlanResponse;

/**
 * 评估策略接口
 * 定义不同评估类型的通用处理方法
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface EvaluateStrategy {

    /**
     * 获取策略支持的评估类型编码
     * @return 评估类型编码
     */
    String getEvaluateType();

    /**
     * 获取策略名称
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 验证评估请求参数
     * @param request 评估请求
     * @return 验证结果，true表示通过
     */
    boolean validateRequest(EvaluatePlanRequest request);

    /**
     * 执行评估计划
     * @param request 评估请求
     * @return 评估响应
     */
    EvaluatePlanResponse executeEvaluate(EvaluatePlanRequest request);

    /**
     * 生成评估报告
     * @param request 评估请求
     * @return 报告内容
     */
    String generateReport(EvaluatePlanRequest request);

    /**
     * 获取评估进度
     * @param planId 计划ID
     * @return 进度百分比
     */
    Integer getProgress(Long planId);
}
