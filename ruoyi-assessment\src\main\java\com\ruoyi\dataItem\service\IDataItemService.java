package com.ruoyi.dataItem.service;

import com.ruoyi.common.utils.poi.ExcelImportHelper;
import com.ruoyi.dataItem.domain.DataItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.dataItem.domain.excel.ExcelDataItem;
import com.ruoyi.planList.domain.PlanList;
import com.ruoyi.planList.domain.excel.ExcelPlanList;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 数据项管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IDataItemService extends IService<DataItem> {

    /**
     * Excel导入评估计划清单
     */
    Map<String, Object> importExcelList(MultipartFile file, Long orgId) throws IOException;

    /**
     * 批量入库
     *
     * @param planList
     * @return
     */
    int batchImport(Long orgId, List<DataItem> planList);

    /**
     * 将excel数据转换成数据库实体
     *
     * @param excel
     * @param orgId
     * @return
     */
    DataItem convertToDbList(ExcelDataItem excel, Long orgId);

    /**
     * 导出Excel
     *
     * @param response
     * @param element
     * @param templatePath
     */
    void exportExcelWithTemplate(HttpServletResponse response, DataItem element, String templatePath);

    /**
     * 逻辑删除数据项并记录日志
     */
    boolean removeByIdWithLog(Long id);
}
