# 任务配置管理集成说明

## 概述

本文档说明了如何将DataSecurityTaskManager与流程控制模块集成，实现任务配置的动态管理，避免写死的配置。

## 重构内容

### 1. 扩展ProcessStepDefinition实体

为`ProcessStepDefinition`实体添加了以下任务配置相关字段：

- `taskType`: 任务类型编码
- `taskConfig`: 任务配置参数（JSON格式）
- `taskExecutor`: 任务执行器类名
- `estimatedDuration`: 预估执行时间（分钟）
- `taskPriority`: 任务优先级（1-低，2-中，3-高）
- `skippable`: 是否可跳过（0-否，1-是）

### 2. 创建TaskConfigService

创建了`ITaskConfigService`接口和`TaskConfigServiceImpl`实现类，提供以下功能：

- 根据流程编码和步骤编码获取任务配置
- 解析任务配置参数
- 获取默认任务参数
- 验证任务参数
- 缓存管理

### 3. 重构DataSecurityTaskManager

重构了`DataSecurityTaskManager`，主要改进：

- 使用`TaskConfigService`动态获取任务配置
- 从配置中获取默认参数，支持参数合并
- 移除写死的任务列表和参数验证逻辑
- 支持从流程定义中动态生成任务列表

## 使用方式

### 1. 数据库配置

首先执行SQL脚本更新表结构：

```sql
-- 执行 update_process_step_definition.sql
source ruoyi-assessment/src/main/resources/sql/update_process_step_definition.sql;
```

### 2. 配置任务定义

在`dsa_process_step_definition`表中配置任务：

```sql
-- 示例：配置数据收集任务
UPDATE dsa_process_step_definition 
SET 
    task_type = 'data_collection',
    task_config = '{"dataScope": "all", "includeBackup": false, "timeRange": "current"}',
    task_executor = 'com.ruoyi.evaluatePlan.strategy.impl.dataSecurity.DataSecurityTaskManager',
    estimated_duration = 30,
    task_priority = 2,
    skippable = 0
WHERE process_id = ? AND step_code = 'data_identify';
```

### 3. 代码使用

#### 获取任务配置

```java
@Autowired
private ITaskConfigService taskConfigService;

// 获取特定任务配置
ProcessStepDefinition taskConfig = taskConfigService.getTaskConfig("data_security_plan", "data_identify");

// 获取流程的所有任务配置
List<ProcessStepDefinition> taskConfigs = taskConfigService.getTaskConfigsByProcess("data_security_plan");

// 解析任务配置参数
Map<String, Object> configParams = taskConfigService.parseTaskConfig(taskConfig.getTaskConfig());
```

#### 使用DataSecurityTaskManager

```java
@Autowired
private DataSecurityTaskManager taskManager;

// 获取可用任务列表（从配置动态生成）
List<Map<String, Object>> availableTasks = taskManager.getAvailableTasks();

// 执行任务
EvaluatePlanRequest request = new EvaluatePlanRequest();
request.setPlanId(1006L);
request.setEvaluateType("data_security_plan");

Map<String, Object> taskParams = new HashMap<>();
taskParams.put("dataScope", "sensitive_only");

EvaluatePlanResponse response = taskManager.executeTask(request, "data_collection", taskParams);
```

## 配置示例

### 任务配置JSON示例

#### 数据收集任务
```json
{
  "dataScope": "all",
  "includeBackup": false,
  "timeRange": "current"
}
```

#### 风险分析任务
```json
{
  "analysisDepth": "standard",
  "includeThirdParty": false,
  "riskCategories": ["访问控制", "数据泄露", "系统漏洞"]
}
```

#### 控制措施任务
```json
{
  "measureTypes": ["技术措施", "管理措施", "物理措施"],
  "priorityLevel": "medium",
  "implementationPlan": "分阶段实施"
}
```

#### 报告生成任务
```json
{
  "reportFormat": "pdf",
  "includeCharts": true,
  "detailLevel": "standard"
}
```

## 优势

### 1. 配置化管理
- 任务配置存储在数据库中，支持动态修改
- 无需重启应用即可调整任务参数
- 支持不同环境的差异化配置

### 2. 扩展性
- 新增任务类型只需在数据库中配置
- 支持自定义任务执行器
- 支持任务优先级和跳过策略

### 3. 缓存优化
- 使用Redis缓存任务配置，提高性能
- 支持缓存刷新，保证数据一致性

### 4. 参数验证
- 统一的参数验证逻辑
- 支持参数默认值和合并

## 测试

运行测试类验证功能：

```bash
# 运行任务配置服务测试
mvn test -Dtest=TaskConfigServiceTest
```

测试内容包括：
- 任务配置获取
- 参数解析和验证
- 缓存功能
- DataSecurityTaskManager集成

## 注意事项

1. **数据库兼容性**: 确保数据库支持JSON字段类型
2. **缓存配置**: 确保Redis配置正确
3. **参数格式**: 任务配置必须是有效的JSON格式
4. **向下兼容**: 保留了默认任务列表作为降级方案

## 扩展指南

### 添加新任务类型

1. 在`TaskConfigServiceImpl`中添加默认参数和验证逻辑
2. 在数据库中配置任务定义
3. 实现对应的任务执行逻辑

### 自定义任务执行器

1. 实现任务执行接口
2. 在任务配置中指定执行器类名
3. 通过反射或Spring容器获取执行器实例

这样的设计使得任务管理更加灵活和可维护，支持业务需求的快速变化。
