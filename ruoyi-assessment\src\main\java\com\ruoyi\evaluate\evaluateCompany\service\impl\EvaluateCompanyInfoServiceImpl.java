package com.ruoyi.evaluate.evaluateCompany.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateCompany.mapper.EvaluateCompanyInfoMapper;
import com.ruoyi.evaluate.evaluateCompany.domain.EvaluateCompanyInfo;
import com.ruoyi.evaluate.evaluateCompany.service.IEvaluateCompanyInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 评估单位信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class EvaluateCompanyInfoServiceImpl extends ServiceImpl<EvaluateCompanyInfoMapper, EvaluateCompanyInfo> implements IEvaluateCompanyInfoService {

}
