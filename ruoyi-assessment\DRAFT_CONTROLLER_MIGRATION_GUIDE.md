# 暂存功能控制器迁移指南

## 概述

本文档说明了暂存功能从`EvaluatePlanTaskController`独立为专门控制器的迁移过程和影响。

## 迁移背景

### 迁移前架构
- 暂存功能集成在`EvaluatePlanTaskController`中
- 所有暂存接口都在`/evaluatePlan/task/draft/*`路径下
- 功能耦合度较高，不利于复用

### 迁移后架构
- 创建了两个独立的暂存控制器
- 功能解耦，职责更加清晰
- 支持更好的复用和扩展

## 新的控制器结构

### 1. DraftStorageController (通用暂存控制器)
- **路径**: `/common/draft/*`
- **功能**: 提供通用的暂存功能，支持任意业务类型
- **特点**: 
  - 业务无关，高度通用
  - 支持多种数据类型
  - 提供完整的CRUD和批量操作

### 2. EvaluatePlanTaskDraftController (专用暂存控制器)
- **路径**: `/evaluatePlan/task/draft/*`
- **功能**: 专门处理评估计划任务的暂存操作
- **特点**:
  - 业务专用，类型安全
  - 提供业务特定的功能（如从暂存创建任务）
  - 接口路径保持不变，兼容现有前端

## API接口变化

### 保持不变的接口
以下接口路径和功能完全保持不变，现有前端代码无需修改：

```
POST   /evaluatePlan/task/draft/save
GET    /evaluatePlan/task/draft/get/{draftKey}
DELETE /evaluatePlan/task/draft/delete/{draftKey}
GET    /evaluatePlan/task/draft/list
DELETE /evaluatePlan/task/draft/clear
GET    /evaluatePlan/task/draft/exists/{draftKey}
POST   /evaluatePlan/task/draft/create/{draftKey}
```

### 新增的接口

#### 通用暂存接口
```
POST   /common/draft/save
GET    /common/draft/get
DELETE /common/draft/delete
GET    /common/draft/list
DELETE /common/draft/clear
GET    /common/draft/exists
POST   /common/draft/batch/save
DELETE /common/draft/batch/delete
POST   /common/draft/updateTtl
```

#### 评估计划任务批量接口
```
POST   /evaluatePlan/task/draft/batch/save
POST   /evaluatePlan/task/draft/batch/get
DELETE /evaluatePlan/task/draft/batch/delete
```

## 权限变化

### 新增权限
- `common:draft:save` - 通用暂存保存权限
- `common:draft:query` - 通用暂存查询权限
- `common:draft:remove` - 通用暂存删除权限
- `common:draft:list` - 通用暂存列表权限
- `common:draft:clear` - 通用暂存清理权限
- `common:draft:edit` - 通用暂存编辑权限

### 保持不变的权限
- `evaluatePlan:task:draft` - 评估计划任务暂存权限
- `evaluatePlan:task:add` - 从暂存创建任务权限

## 代码变化

### 移除的代码
从`EvaluatePlanTaskController`中移除了以下内容：
- 暂存相关的导入语句
- `IDraftStorageService`依赖注入
- 所有暂存相关的方法（约200行代码）

### 新增的文件
- `DraftStorageController.java` - 通用暂存控制器
- `EvaluatePlanTaskDraftController.java` - 专用暂存控制器
- `DraftStorageControllerTest.java` - 通用控制器测试
- `EvaluatePlanTaskDraftControllerTest.java` - 专用控制器测试

## 迁移步骤

### 1. 数据库配置更新
执行更新的配置脚本：
```sql
source ruoyi-assessment/src/main/resources/draft-config.sql
```

### 2. 权限配置
确保管理员角色拥有新增的权限：
- 检查`sys_menu`表中是否有新的权限记录
- 检查`sys_role_menu`表中是否正确分配了权限

### 3. 前端代码（可选）
现有前端代码无需修改，但可以考虑以下优化：
- 对于新的业务模块，可以使用通用暂存接口
- 利用新增的批量操作接口提升性能

## 兼容性说明

### 向后兼容
- ✅ 现有API接口路径完全保持不变
- ✅ 接口参数和返回格式保持一致
- ✅ 权限要求保持不变
- ✅ 现有前端代码无需修改

### 功能增强
- ✅ 新增通用暂存功能，支持其他业务模块
- ✅ 新增批量操作接口，提升性能
- ✅ 新增TTL更新功能，更灵活的过期时间管理
- ✅ 更好的代码组织和职责分离

## 测试验证

### 1. 单元测试
运行以下测试确保功能正常：
```bash
mvn test -Dtest=DraftStorageServiceTest
mvn test -Dtest=DraftStorageControllerTest
mvn test -Dtest=EvaluatePlanTaskDraftControllerTest
```

### 2. 集成测试
- 验证现有前端功能是否正常
- 测试新增的批量操作接口
- 验证权限控制是否生效

### 3. 性能测试
- 对比迁移前后的接口响应时间
- 测试批量操作的性能提升

## 使用建议

### 对于现有业务
- 继续使用`/evaluatePlan/task/draft/*`接口
- 可以逐步迁移到批量操作接口以提升性能

### 对于新业务
- 优先考虑使用通用暂存接口`/common/draft/*`
- 如果有特殊业务需求，可以创建专用控制器

### 对于系统管理员
- 定期检查暂存数据使用情况
- 根据需要调整配置参数
- 监控Redis内存使用情况

## 故障排除

### 常见问题

1. **权限不足错误**
   - 检查用户是否拥有相应权限
   - 确认权限配置是否正确执行

2. **接口404错误**
   - 确认控制器是否正确加载
   - 检查Spring Boot扫描路径配置

3. **暂存数据丢失**
   - 检查Redis连接是否正常
   - 确认过期时间配置是否合理

### 日志检查
关键日志位置：
- 暂存操作日志：`DraftStorageServiceImpl`
- 控制器访问日志：`DraftStorageController`、`EvaluatePlanTaskDraftController`
- 权限验证日志：Spring Security相关日志

## 总结

本次迁移实现了暂存功能的模块化和通用化，在保持完全向后兼容的同时，提供了更好的代码组织和更强的功能扩展性。迁移过程对现有系统无任何破坏性影响，同时为未来的功能扩展奠定了良好基础。
