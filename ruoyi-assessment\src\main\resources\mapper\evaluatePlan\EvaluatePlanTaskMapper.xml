<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluatePlan.mapper.EvaluatePlanTaskMapper">
    
    <resultMap type="EvaluatePlanTask" id="EvaluatePlanTaskResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="evaluateOrgId"    column="evaluate_org_id"    />
        <result property="evaluateType"    column="evaluate_type"    />
        <result property="modelId"    column="model_id"    />
        <result property="reportNo"    column="report_no"    />
        <result property="name"    column="name"    />
        <result property="planStartDate"    column="plan_start_date"    />
        <result property="planEndDate"    column="plan_end_date"    />
        <result property="realStartDate"    column="real_start_date"    />
        <result property="realEndDate"    column="real_end_date"    />
        <result property="deadline"    column="deadline"    />
        <result property="mode"    column="mode"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="masterUser"    column="master_user"    />
        <result property="masterDept"    column="master_dept"    />
        <result property="taskDescription"    column="task_description"    />
        <result property="completionRate"    column="completion_rate"    />
        <result property="planHours"    column="plan_hours"    />
        <result property="realHours"    column="real_hours"    />
        <result property="originTaskId"    column="origin_task_id"    />
        <result property="versionId"    column="version_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluatePlanTaskVo">
        select id, org_id, evaluate_org_id, evaluate_type, model_id, report_no, name, plan_start_date, plan_end_date, real_start_date, real_end_date, deadline, mode, task_status, master_user, master_dept, task_description, completion_rate, plan_hours, real_hours, origin_task_id, version_id, status, create_by, create_time, update_by, update_time, remark, del_flag from dsa_evaluate_plan_task
    </sql>

    <select id="selectEvaluatePlanTaskList" parameterType="EvaluatePlanTask" resultMap="EvaluatePlanTaskResult">
        <include refid="selectEvaluatePlanTaskVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="evaluateOrgId != null "> and evaluate_org_id = #{evaluateOrgId}</if>
            <if test="evaluateType != null  and evaluateType != ''"> and evaluate_type = #{evaluateType}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="reportNo != null  and reportNo != ''"> and report_no = #{reportNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="planStartDate != null "> and plan_start_date = #{planStartDate}</if>
            <if test="planEndDate != null "> and plan_end_date = #{planEndDate}</if>
            <if test="realStartDate != null "> and real_start_date = #{realStartDate}</if>
            <if test="realEndDate != null "> and real_end_date = #{realEndDate}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="mode != null "> and mode = #{mode}</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="masterUser != null  and masterUser != ''"> and master_user = #{masterUser}</if>
            <if test="masterDept != null  and masterDept != ''"> and master_dept = #{masterDept}</if>
            <if test="taskDescription != null  and taskDescription != ''"> and task_description = #{taskDescription}</if>
            <if test="completionRate != null "> and completion_rate = #{completionRate}</if>
            <if test="planHours != null "> and plan_hours = #{planHours}</if>
            <if test="realHours != null "> and real_hours = #{realHours}</if>
            <if test="originTaskId != null "> and origin_task_id = #{originTaskId}</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluatePlanTaskById" parameterType="Long" resultMap="EvaluatePlanTaskResult">
        <include refid="selectEvaluatePlanTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluatePlanTask" parameterType="EvaluatePlanTask" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_evaluate_plan_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="evaluateOrgId != null">evaluate_org_id,</if>
            <if test="evaluateType != null  and evaluateType != ''">evaluate_type,</if>
            <if test="modelId != null">model_id,</if>
            <if test="reportNo != null">report_no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="planStartDate != null">plan_start_date,</if>
            <if test="planEndDate != null">plan_end_date,</if>
            <if test="realStartDate != null">real_start_date,</if>
            <if test="realEndDate != null">real_end_date,</if>
            <if test="deadline != null">deadline,</if>
            <if test="mode != null">mode,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="masterUser != null">master_user,</if>
            <if test="masterDept != null">master_dept,</if>
            <if test="taskDescription != null">task_description,</if>
            <if test="completionRate != null">completion_rate,</if>
            <if test="planHours != null">plan_hours,</if>
            <if test="realHours != null">real_hours,</if>
            <if test="originTaskId != null">origin_task_id,</if>
            <if test="versionId != null">version_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="evaluateOrgId != null">#{evaluateOrgId},</if>
            <if test="evaluateType != null  and evaluateType != ''">#{evaluateType},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="reportNo != null">#{reportNo},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="planStartDate != null">#{planStartDate},</if>
            <if test="planEndDate != null">#{planEndDate},</if>
            <if test="realStartDate != null">#{realStartDate},</if>
            <if test="realEndDate != null">#{realEndDate},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="mode != null">#{mode},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="masterUser != null">#{masterUser},</if>
            <if test="masterDept != null">#{masterDept},</if>
            <if test="taskDescription != null">#{taskDescription},</if>
            <if test="completionRate != null">#{completionRate},</if>
            <if test="planHours != null">#{planHours},</if>
            <if test="realHours != null">#{realHours},</if>
            <if test="originTaskId != null">#{originTaskId},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluatePlanTask" parameterType="EvaluatePlanTask">
        update dsa_evaluate_plan_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="evaluateOrgId != null">evaluate_org_id = #{evaluateOrgId},</if>
            <if test="evaluateType != null  and evaluateType != ''">evaluate_type = #{evaluateType},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="planStartDate != null">plan_start_date = #{planStartDate},</if>
            <if test="planEndDate != null">plan_end_date = #{planEndDate},</if>
            <if test="realStartDate != null">real_start_date = #{realStartDate},</if>
            <if test="realEndDate != null">real_end_date = #{realEndDate},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="mode != null">mode = #{mode},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="masterUser != null">master_user = #{masterUser},</if>
            <if test="masterDept != null">master_dept = #{masterDept},</if>
            <if test="taskDescription != null">task_description = #{taskDescription},</if>
            <if test="completionRate != null">completion_rate = #{completionRate},</if>
            <if test="planHours != null">plan_hours = #{planHours},</if>
            <if test="realHours != null">real_hours = #{realHours},</if>
            <if test="originTaskId != null">origin_task_id = #{originTaskId},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluatePlanTaskById" parameterType="Long">
        delete from dsa_evaluate_plan_task where id = #{id}
    </delete>

    <delete id="deleteEvaluatePlanTaskByIds" parameterType="String">
        delete from dsa_evaluate_plan_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>