# Sheet0联系人信息实现说明

## 实现背景

根据用户提供的实际Sheet0结构，这是一个联系人信息表格，包含以下字段：
- 姓名
- 职务/职称  
- 所属部门
- 办公电话
- 移动电话
- 电子邮件

## 实现调整

### 1. Excel数据模型调整

**文件：** `ExcelTargetCompanyInfo0.java`

```java
@Data
public class ExcelTargetCompanyInfo0 {
    /** 姓名 */
    @ExcelProperty(index = 0)
    private String name;

    /** 职务/职称 */
    @ExcelProperty(index = 1)
    private String position;

    /** 所属部门 */
    @ExcelProperty(index = 2)
    private String department;

    /** 办公电话 */
    @ExcelProperty(index = 3)
    private String officePhone;

    /** 移动电话 */
    @ExcelProperty(index = 4)
    private String mobilePhone;

    /** 电子邮件 */
    @ExcelProperty(index = 5)
    private String email;
}
```

### 2. 数据映射逻辑调整

**文件：** `TargetCompanyInfoServiceImpl.java`

```java
// 从Sheet0复制联系人信息
if (i < sheet0Data.size()) {
    ExcelTargetCompanyInfo0 excel0 = sheet0Data.get(i);
    if (StringUtils.isNotEmpty(excel0.getName())) {
        company.setContactName(excel0.getName());
    }
    if (StringUtils.isNotEmpty(excel0.getPosition())) {
        company.setJob(excel0.getPosition());
    }
    if (StringUtils.isNotEmpty(excel0.getDepartment())) {
        company.setDept(excel0.getDepartment());
    }
    if (StringUtils.isNotEmpty(excel0.getOfficePhone())) {
        company.setTel(excel0.getOfficePhone());
    }
    if (StringUtils.isNotEmpty(excel0.getMobilePhone())) {
        company.setMobile(excel0.getMobilePhone());
    }
    if (StringUtils.isNotEmpty(excel0.getEmail())) {
        company.setEmail(excel0.getEmail());
    }
}
```

### 3. Listener调整

**文件：** `TargetCompanyInfo0Listener.java`

- 更新了日志信息，明确标识为"联系人信息"
- 调整了错误信息的描述
- 保持了原有的错误处理逻辑

## 字段映射关系

| Sheet0字段 | Excel列 | TargetCompanyInfo字段 | 说明 |
|-----------|---------|----------------------|------|
| name | A列 | contactName | 联系人姓名 |
| position | B列 | job | 联系人职务 |
| department | C列 | dept | 联系人部门 |
| officePhone | D列 | tel | 办公电话 |
| mobilePhone | E列 | mobile | 移动电话 |
| email | F列 | email | 电子邮件 |

## 数据处理流程

1. **读取Sheet1**：获取公司基本信息
2. **读取Sheet0**：获取联系人信息
3. **数据合并**：将联系人信息合并到对应的公司记录中
4. **保存数据**：将合并后的完整公司信息保存到数据库

## 特点优势

### 1. 灵活的数据结构
- 支持联系人信息与公司信息的分离管理
- 便于用户分别维护不同类型的信息

### 2. 智能数据合并
- 按行索引自动对应Sheet0和Sheet1的数据
- 支持部分字段为空的情况
- 保证数据完整性

### 3. 详细的错误处理
- 记录具体的解析错误位置
- 提供友好的错误信息
- 支持部分成功导入

## 使用示例

### Excel文件结构
```
Sheet0 (联系人信息):
A1: 联系人信息
A2: 姓名    B2: 职务/职称  C2: 所属部门  D2: 办公电话
A3: 移动电话  B3: 电子邮件
A4: 张三    B4: 经理      C4: 技术部    D4: 010-12345678
A5: 13800138000  B5: <EMAIL>

Sheet1 (公司基本信息):
A1: 公司名称  B1: 单位简称  C1: 公司地址  ...
A2: 测试公司A  B2: 公司A    C2: 北京市朝阳区  ...
```

### 导入结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalSuccess": 1,
    "totalFailure": 0,
    "message": "多Sheet导入完成。总计成功：1条，失败：0条。详情：公司信息：成功1条，失败0条；部门信息：成功0条，失败0条；人员信息：成功0条，失败0条；文档信息：成功0条，失败0条；"
  }
}
```

## 扩展建议

### 1. 多联系人支持
如果需要支持一个公司多个联系人：
- 可以在Sheet0中添加多行数据
- 修改合并逻辑，支持一对多关系
- 考虑创建独立的联系人表

### 2. 联系人类型
可以添加联系人类型字段：
- 主要联系人
- 技术联系人
- 商务联系人
- 财务联系人

### 3. 联系人验证
增强数据验证：
- 电话号码格式验证
- 邮箱地址格式验证
- 必填字段检查

## 测试验证

### 1. 正常场景测试
- 完整联系人信息导入
- 部分字段为空的情况
- 多行联系人数据

### 2. 异常场景测试
- 邮箱格式错误
- 电话号码格式错误
- 特殊字符处理

### 3. 边界条件测试
- Sheet0数据行数与Sheet1不一致
- 空的Sheet0
- 只有表头没有数据

## 总结

根据实际的Sheet0结构，成功调整了联系人信息的处理逻辑：

1. **准确映射**：Excel字段与数据库字段的准确对应
2. **数据完整性**：保证联系人信息与公司信息的正确合并
3. **错误处理**：提供详细的错误信息和位置定位
4. **扩展性**：为后续功能扩展预留了空间

该实现完全符合用户提供的Sheet0结构，能够正确处理联系人信息的导入和合并。
