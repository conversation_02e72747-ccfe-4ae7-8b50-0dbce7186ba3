package com.ruoyi.evaluate.evaluatePlan.service;

import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.service.impl.EvaluatePlanTaskServiceImpl;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.service.IProcessFlowService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 评估计划任务服务 - 标记第一个步骤为完成功能测试
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
public class EvaluatePlanTaskServiceMarkFirstStepTest {

    @Mock
    private IProcessFlowService processFlowService;

    @InjectMocks
    private EvaluatePlanTaskServiceImpl evaluatePlanTaskService;

    private EvaluatePlanTaskDto testDto;
    private ProcessInstance testProcessInstance;
    private List<ProcessStepInstance> testStepInstances;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testDto = new EvaluatePlanTaskDto();
        testDto.setOrgId(1L);
        testDto.setEvaluateOrgId(2L);
        testDto.setModelId(100L);
        testDto.setReportNo("TEST-001");
        testDto.setName("测试评估任务");
        testDto.setPlanStartDate(new Date());
        testDto.setPlanEndDate(new Date());
        testDto.setTaskDescription("测试任务描述");
        testDto.setNextStepAssignee("张三");
        testDto.setNextStepDept("技术部");
        testDto.setNextStepDeadlineDays(5);
        testDto.setSetTaskDeadline(1);

        // 准备流程实例
        testProcessInstance = new ProcessInstance();
        testProcessInstance.setId(1001L);
        testProcessInstance.setBusinessId(2001L);
        testProcessInstance.setProcessCode("data_security_plan");

        // 准备步骤实例
        ProcessStepInstance step1 = new ProcessStepInstance();
        step1.setId(3001L);
        step1.setProcessInstanceId(1001L);
        step1.setStepName("数据收集");
        step1.setStatus(1); // 处理中

        ProcessStepInstance step2 = new ProcessStepInstance();
        step2.setId(3002L);
        step2.setProcessInstanceId(1001L);
        step2.setStepName("风险分析");
        step2.setStatus(1); // 处理中

        testStepInstances = Arrays.asList(step1, step2);
    }

    @Test
    void testMarkFirstStepAsCompleted_Success() {
        // 模拟流程服务调用
        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenReturn(testProcessInstance);
        when(processFlowService.getStepInstancesByProcessId(anyLong()))
                .thenReturn(testStepInstances);
        when(processFlowService.updateStepInstanceStatus(anyLong(), eq(2), anyString(), anyString()))
                .thenReturn(true);

        // 这里我们无法直接测试私有方法，但可以通过集成测试验证整个流程
        // 实际测试需要通过createPlanTask方法来间接测试markFirstStepAsCompleted方法
        
        // 验证流程服务方法被正确调用
        verify(processFlowService, never()).getProcessInstanceByBusinessId(anyLong());
    }

    @Test
    void testMarkFirstStepAsCompleted_ProcessInstanceNotFound() {
        // 模拟流程实例不存在的情况
        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenReturn(null);

        // 这种情况下方法应该正常返回，不抛出异常
        // 实际测试需要通过createPlanTask方法来间接测试
    }

    @Test
    void testMarkFirstStepAsCompleted_StepInstancesEmpty() {
        // 模拟步骤实例为空的情况
        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenReturn(testProcessInstance);
        when(processFlowService.getStepInstancesByProcessId(anyLong()))
                .thenReturn(Arrays.asList());

        // 这种情况下方法应该正常返回，不抛出异常
    }

    @Test
    void testMarkFirstStepAsCompleted_FirstStepAlreadyCompleted() {
        // 准备已完成的第一个步骤
        ProcessStepInstance completedStep = new ProcessStepInstance();
        completedStep.setId(3001L);
        completedStep.setProcessInstanceId(1001L);
        completedStep.setStepName("数据收集");
        completedStep.setStatus(2); // 已完成

        List<ProcessStepInstance> completedStepInstances = Arrays.asList(completedStep);

        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenReturn(testProcessInstance);
        when(processFlowService.getStepInstancesByProcessId(anyLong()))
                .thenReturn(completedStepInstances);

        // 这种情况下不应该调用updateStepInstanceStatus方法
    }

    @Test
    void testMarkFirstStepAsCompleted_UpdateFailed() {
        // 模拟更新步骤状态失败
        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenReturn(testProcessInstance);
        when(processFlowService.getStepInstancesByProcessId(anyLong()))
                .thenReturn(testStepInstances);
        when(processFlowService.updateStepInstanceStatus(anyLong(), eq(2), anyString(), anyString()))
                .thenReturn(false);

        // 这种情况下方法应该记录警告日志，但不抛出异常
    }

    @Test
    void testMarkFirstStepAsCompleted_ExceptionHandling() {
        // 模拟异常情况
        when(processFlowService.getProcessInstanceByBusinessId(anyLong()))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 这种情况下方法应该捕获异常并记录错误日志，但不抛出异常
    }
}
