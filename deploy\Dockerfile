# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="secdriver"

# 设置工作目录
WORKDIR /app

# 创建应用用户
RUN addgroup -g 1000 app && \
    adduser -D -s /bin/sh -u 1000 -G app app

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploadPath /app/config && \
    chown -R app:app /app

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 复制jar包到容器中（JAR包名为ruoyi_admin.jar）
COPY ruoyi_admin.jar /app/app.jar

# 设置文件权限
RUN chown -R app:app /app

# 切换到应用用户
USER app

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Dname=ruoyi_admin.jar \
    -Duser.timezone=Asia/Shanghai \
    -Xms512m \
    -Xmx1024m \
    -XX:MetaspaceSize=128m \
    -XX:MaxMetaspaceSize=512m \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:+PrintGCDateStamps \
    -XX:+PrintGCDetails \
    -XX:NewRatio=1 \
    -XX:SurvivorRatio=30 \
    -XX:+UseParallelGC \
    -XX:+UseParallelOldGC"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/actuator/health || exit 1

# 启动应用
CMD ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar --spring.profiles.active=prod"]
