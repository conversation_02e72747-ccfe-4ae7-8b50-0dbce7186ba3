package com.ruoyi.evaluate.evaluateData.service;

import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanTeamMember;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 评估单位评估团队成员Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IEvaluatePlanTeamMemberService extends IService<EvaluatePlanTeamMember> {

    /**
     * 从评估团队导入成员到当前数据库
     *
     * @param teamId 团队ID
     * @param planId 计划ID
     * @return 导入结果信息
     */
    String importFromTeam(Long teamId, Long planId);
}
